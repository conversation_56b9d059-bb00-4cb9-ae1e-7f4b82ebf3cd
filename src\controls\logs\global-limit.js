
const pool = require("../db"); 
const queries = require("../queries");
const getGlobalLimits = (req, res) => {
  pool.query(queries.get.getGlobalLimits, (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

const getGlobalLimitByType = (req, res) => {
  const rewardType = req.params.reward_type;
  pool.query(queries.getby.getGlobalLimitByType, [rewardType], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

const updateGlobalLimitCount = (req, res) => {
  const rewardType = req.params.reward_type;
  const { amount } = req.body;
  pool.query(queries.getby.getGlobalLimitByType, [rewardType], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }

    if (results.rows.length === 0) {
      res.status(404).send(`No global limit found for reward type: ${rewardType}`);
      return;
    }

    const currentCount = parseFloat(results.rows[0].current_count);
    const newCount = currentCount + parseFloat(amount);

    // Update the count
    pool.query(
      queries.up.updateGlobalLimitCount,
      [newCount, rewardType],
      (updateError, updateResults) => {
        if (updateError) {
          res.status(500).send(updateError.message);
          console.error(updateError);
          return;
        }
        res.status(200).send(`Global limit count updated for ${rewardType}`);
      }
    );
  });
};

const resetGlobalLimitCount = (req, res) => {
  const rewardType = req.params.reward_type;
  // Calculate new expiry date (24 hours from now by default)
  const now = new Date();
  const expiryDate = new Date(now);
  // Get the duration from the database first
  pool.query(queries.getby.getGlobalLimitByType, [rewardType], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }

    if (results.rows.length === 0) {
      res.status(404).send(`No global limit found for reward type: ${rewardType}`);
      return;
    }

    const durationHours = results.rows[0].duration_hours || 24;
    expiryDate.setHours(expiryDate.getHours() + durationHours);
    // Reset the count and update expiry date
    pool.query(
      queries.up.resetGlobalLimitCount,
      [0, expiryDate, rewardType],
      (resetError, resetResults) => {
        if (resetError) {
          res.status(500).send(resetError.message);
          console.error(resetError);
          return;
        }
        res.status(200).send(`Global limit reset for ${rewardType}`);
      }
    );
  });
};

const createGlobalLimit = (req, res) => {
  const { reward_type, max_limit, duration_hours } = req.body;

  // Calculate expiry date
  const now = new Date();
  const expiryDate = new Date(now);
  expiryDate.setHours(expiryDate.getHours() + duration_hours);

  pool.query(
    queries.add.addGlobalLimit,
    [reward_type, 0, max_limit, duration_hours, expiryDate],
    (error, results) => {
      if (error) {
        res.status(500).send(error.message);
        console.error(error);
        return;
      }
      res.status(201).send(`Global limit created for ${reward_type}`);
    }
  );
};

module.exports = {
  getGlobalLimits,
  getGlobalLimitByType,
  updateGlobalLimitCount,
  resetGlobalLimitCount,
  createGlobalLimit
};