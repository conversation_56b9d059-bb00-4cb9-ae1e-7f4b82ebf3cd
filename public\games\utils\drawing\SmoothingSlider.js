// SmoothingSlider.js - Smoothing control for drawing lines
class SmoothingSlider {
    constructor(container, options = {}) {
        this.container = container;
        this.onSmoothingChange = options.onSmoothingChange || (() => {});
        this.smoothing = options.defaultSmoothing || 0.5;
        this.minSmoothing = options.minSmoothing || 0.1;
        this.maxSmoothing = options.maxSmoothing || 1.0;
        this.trackWidth = options.trackWidth || 80;
        this.createSlider();
    }

    createSlider() {
        this.sliderContainer = new PIXI.Container();
        this.container.addChild(this.sliderContainer);

        // Background track
        this.track = new PIXI.Graphics();
        this.track.beginFill(0xCCCCCC);
        this.track.drawRoundedRect(0, 0, this.trackWidth, 12, 6);
        this.track.endFill();
        this.track.lineStyle(1, 0x666666, 1);
        this.track.drawRoundedRect(0, 0, this.trackWidth, 12, 6);
        this.sliderContainer.addChild(this.track);

        // Slider handle
        this.handle = new PIXI.Graphics();
        this.handle.beginFill(0x4A90E2);
        this.handle.drawCircle(0, 6, 7);
        this.handle.endFill();
        this.handle.lineStyle(1, 0x2E5C8A, 1);
        this.handle.drawCircle(0, 6, 7);
        
        this.handle.x = this.getHandleX();
        this.handle.y = 0;
        this.handle.interactive = true;
        this.handle.cursor = 'pointer';
        this.handle.buttonMode = true;
        
        this.handle.on('pointerdown', this.onHandleDown.bind(this));
        this.handle.on('pointerup', this.onHandleUp.bind(this));
        this.handle.on('pointerupoutside', this.onHandleUp.bind(this));
        this.handle.on('pointermove', this.onHandleMove.bind(this));
        
        this.sliderContainer.addChild(this.handle);

        // Labels
        this.createLabels();

        // Make track also interactive for clicking
        this.track.interactive = true;
        this.track.cursor = 'pointer';
        this.track.on('pointerdown', this.onTrackClick.bind(this));
    }

    createLabels() {
        // Remove Smooth and Rough labels, add centered Smoothing label
        this.smoothingLabel = new PIXI.Text('Smoothing', {
            fontFamily: 'Arial',
            fontSize: 10,
            fill: 0x333333
        });
        this.smoothingLabel.anchor = new PIXI.Point(0.5, 0);
        this.smoothingLabel.x = this.trackWidth / 2;
        this.smoothingLabel.y = 22;
        this.sliderContainer.addChild(this.smoothingLabel);

        // Value label
        this.valueLabel = new PIXI.Text(`${Math.round(this.smoothing * 100)}%`, {
            fontFamily: 'Arial',
            fontSize: 8,
            fill: 0x666666
        });
        this.valueLabel.x = this.trackWidth / 2;
        this.valueLabel.y = -10;
        this.valueLabel.anchor = new PIXI.Point(0.5, 0);
        this.sliderContainer.addChild(this.valueLabel);
    }

    getHandleX() {
        const normalizedValue = (this.smoothing - this.minSmoothing) / (this.maxSmoothing - this.minSmoothing);
        return normalizedValue * this.trackWidth;
    }

    getSmoothingFromX(x) {
        const clampedX = Math.max(0, Math.min(this.trackWidth, x));
        const normalizedValue = clampedX / this.trackWidth;
        return this.minSmoothing + (normalizedValue * (this.maxSmoothing - this.minSmoothing));
    }

    onHandleDown(event) {
        this.isDragging = true;
        this.dragStartX = event.data.global.x;
        this.dragStartHandleX = this.handle.x;
    }

    onHandleUp(event) {
        this.isDragging = false;
    }

    onHandleMove(event) {
        if (!this.isDragging) return;
        
        const deltaX = event.data.global.x - this.dragStartX;
        const newX = this.dragStartHandleX + deltaX;
        
        this.setHandlePosition(newX);
    }

    onTrackClick(event) {
        const localPos = event.data.getLocalPosition(this.track);
        this.setHandlePosition(localPos.x);
    }

    setHandlePosition(x) {
        const newSmoothing = this.getSmoothingFromX(x);
        this.setSmoothing(newSmoothing);
    }

    setSmoothing(value) {
        this.smoothing = Math.max(this.minSmoothing, Math.min(this.maxSmoothing, value));
        this.handle.x = this.getHandleX();
        this.valueLabel.text = `${Math.round(this.smoothing * 100)}%`;
        this.onSmoothingChange(this.smoothing);
    }

    getSmoothing() {
        return this.smoothing;
    }

    getMaxPoints() {
        // Convert smoothing to max points (inverse relationship)
        // Higher smoothing = fewer points = smoother lines
        // Lower smoothing = more points = more jagged lines
        const minPoints = 2;
        const maxPoints = 20;
        const normalizedSmoothing = (this.smoothing - this.minSmoothing) / (this.maxSmoothing - this.minSmoothing);
        return Math.round(minPoints + (normalizedSmoothing * (maxPoints - minPoints)));
    }

    getPointThreshold() {
        // Distance threshold for adding new points
        // Higher smoothing = larger threshold = fewer points
        const minThreshold = 5;
        const maxThreshold = 50;
        const normalizedSmoothing = (this.smoothing - this.minSmoothing) / (this.maxSmoothing - this.minSmoothing);
        return minThreshold + (normalizedSmoothing * (maxThreshold - minThreshold));
    }

    shouldAddPoint(lastPoint, currentPoint) {
        if (!lastPoint) return true;
        
        const distance = Math.sqrt(
            Math.pow(currentPoint.x - lastPoint.x, 2) + 
            Math.pow(currentPoint.y - lastPoint.y, 2)
        );
        
        return distance >= this.getPointThreshold();
    }
}

window.SmoothingSlider = SmoothingSlider; 