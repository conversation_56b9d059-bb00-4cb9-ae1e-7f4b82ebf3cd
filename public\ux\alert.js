function showAlert(message) {
  var alertElement = document.getElementById('main-alert');
  alertElement.innerHTML = `${message}<br><button class="mt-4 btn-secondary" onclick="this.parentElement.style.display='none'">OK</button>`;
  alertElement.style.display = 'block';
}

function hideTooltip() {
  $('#tooltip').hide();
}

function createInventoryStatusMessage(message = 'No items found', schema = null) {
  const itemContainer = $('<div>').addClass('inventory-item status-message');
  const icon = $('<div>').addClass('nav-icon info-icon');
  const messageContainer = $('<div>').addClass('inventory-status-message');
  messageContainer.text(message);
  itemContainer.append(icon, messageContainer);
  
  // Add Atomic Hub button if schema is provided
  if (schema) {
    // Create buttons container
    const buttonsContainer = $('<div>').addClass('buttons');
    
    // Special case: when "items" schema is selected, show both items and foods buttons
    if (schema === 'items') {
      const itemsUrl = `https://atomichub.io/market?blockchain=wax-mainnet&collection_name=cutecrushies&order=asc&primary_chain=wax-mainnet&schema_name=items&sort=price&symbol=WAX#sales`;
      const foodsUrl = `https://atomichub.io/market?blockchain=wax-mainnet&collection_name=cutecrushies&order=asc&primary_chain=wax-mainnet&schema_name=foods&sort=price&symbol=WAX#sales`;
      
      const itemsButton = $('<button>')
        .addClass('btn-secondary mt-2 mr-2')
        .text('Shop items on Atomic Hub')
        .on('click', function() {
          window.open(itemsUrl, '_blank');
        });
      
      const foodsButton = $('<button>')
        .addClass('btn-secondary mt-2')
        .text('Shop food on Atomic Hub')
        .on('click', function() {
          window.open(foodsUrl, '_blank');
        });
      
      buttonsContainer.append(itemsButton, foodsButton);
    } else {  
      const atomicHubUrl = `https://atomichub.io/market?blockchain=wax-mainnet&collection_name=cutecrushies&order=asc&primary_chain=wax-mainnet&schema_name=${schema}&sort=price&symbol=WAX#sales`;
       // if creature schema, we need the button text to say plural 'creatures'
      if(schema==='creature'){
        schema='creatures';
      }
      const shopButton = $('<button>')
        .addClass('btn-secondary mt-2')
        .text(`Shop ${schema} on Atomic Hub`)
        .on('click', function() {
          window.open(atomicHubUrl, '_blank');
        });
      buttonsContainer.append(shopButton);
    }
    
    // Append the buttons container to the item container
    itemContainer.append(buttonsContainer);
  }
  
  return itemContainer;
}
