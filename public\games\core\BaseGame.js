class BaseGame {
    constructor() { 
        this.time = 0;
        this.score = 0;
        this.level = 1;
        this.gameInterval = null;
        this.countdownInterval = null;
        this.isCountdownActive = false;
        this.hudHeight = 40; // Height of the HUD area in pixels
        this._isPaused = false;
        this._wasPlaying = false;
        this._pauseOverlay = null; // New property for pause overlay
        // Listen for visibility and focus changes
        document.addEventListener('visibilitychange', () => this._handleVisibilityChange());
        window.addEventListener('blur', () => this._handleVisibilityChange());
        // Remove auto-resume on focus
        // window.addEventListener('focus', () => this._handleVisibilityChange());
        // Add click-to-resume logic
        setTimeout(() => {
            if (this.app && this.app.view) {
                this.app.view.addEventListener('click', () => {
                    if (this._isPaused) {
                        this._resumeGame();
                    }
                });
            }
        }, 0);

        try { 
            this.app = new PIXI.Application({
                width: 500,
                height: 500, // Full height - games should avoid top 40px HUD area
                backgroundColor: 0x1099bb,
                resolution: window.devicePixelRatio || 1,
                antialias: true
            });

            const gameCanvas = document.getElementById('game-canvas');

            if (gameCanvas) {
                gameCanvas.appendChild(this.app.view);

                // Force canvas to be visible
                const canvasElement = this.app.view;
                canvasElement.style.display = 'block';
                canvasElement.style.visibility = 'visible';
                canvasElement.style.opacity = '1';
            }

            // --- HUD Elements Example ---
            // ResourceBar (HP/MP/Resource bar) with background
            this.hpBar = HUDConfig.createHudElement('resource_bar', 'hud_med_position_1', {
                max: 100,
                value: 100,
                color: 0x39ff14, // neon green
                underColor: 0xff2222 // red
            });
            HUDConfig.addToStage(this.app.stage, this.hpBar);

            // HeartMeter (Hearts/Powerups) with background
            this.heartMeter = HUDConfig.createHudElement('heart_meter', 'hud_med_position_2', {
                count: 3,
                value: 3,
                fullIcon: 'images/games/hud/heart.png',
                emptyIcon: 'images/games/hud/heart-empty.png',
                iconWidth: 24,
                iconHeight: 24,
                spacing: 4
            });
            HUDConfig.addToStage(this.app.stage, this.heartMeter);

            // PowerupMeter (Powerups) with background
            this.powerupMeter = HUDConfig.createHudElement('heart_meter', 'hud_lg_position_1', {
                count: 3,
                value: 0,
                fullIcon: 'images/games/hud/powerup.png',
                emptyIcon: 'images/games/hud/powerup-empty.png',
                iconWidth: 24,
                iconHeight: 24,
                spacing: 4
            });
            HUDConfig.addToStage(this.app.stage, this.powerupMeter);
            // --- End HUD Example ---
        } catch (error) {
            // Handle error silently
        }
    }

    _handleVisibilityChange() {
        const isHidden = document.hidden || document.visibilityState !== 'visible';
        if (isHidden) {
            this._pauseGame();
        }
        // Do not auto-resume on focus/visibilitychange
    }

    showPauseOverlay() {
        if (this._pauseOverlay) return;
        const gameArea = document.getElementById('game-area');
        const overlay = document.createElement('div');
        overlay.id = 'pause-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,1.0);
            border: 3px solid #FFD700;
            border-radius: 10px;
            width: 400px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        `;
        overlay.innerHTML = `<span style="font-family: Arial; font-size: 48px; font-weight: bold; color: #FFD700; -webkit-text-stroke: 4px #000; text-shadow: 2px 2px 0 #000;">Paused</span>`;
        gameArea.appendChild(overlay);
        this._pauseOverlay = overlay;
    }
    hidePauseOverlay() {
        if (this._pauseOverlay) {
            this._pauseOverlay.remove();
            this._pauseOverlay = null;
        }
    }
    _pauseGame() {
        if (this._isPaused) return;
        this._isPaused = true;
        this._wasPlaying = (this.gameInterval || this.countdownInterval);
        clearInterval(this.gameInterval);
        clearInterval(this.countdownInterval);
        this.gameInterval = null;
        this.countdownInterval = null;
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
            this.gameLoop = null;
        }
        if (this.physicsManager && this.physicsManager.physicsRunning !== undefined) {
            this.physicsManager.physicsRunning = false;
        }
        this.showPauseOverlay();
    }
    _resumeGame() {
        if (!this._isPaused) return;
        this._isPaused = false;
        if (typeof this.gameState === 'undefined' || this.gameState === 'playing') {
            if (this._wasPlaying) {
                this.startGameTimer && this.startGameTimer();
                if (typeof this.startGameLogic === 'function' && !this.gameLoop) {
                    this.startGameLogic();
                }
                if (this.physicsManager && this.physicsManager.physicsRunning !== undefined) {
                    this.physicsManager.physicsRunning = true;
                    if (typeof this.physicsManager.runPhysics === 'function') {
                        this.physicsManager.runPhysics();
                    }
                }
            }
        }
        this.hidePauseOverlay();
    }

    async startGame() {
        // First check if player has enough credits
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }

        try {
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);

            // Continue with game initialization after successful transaction
            this.initGame(true); // Reset level for initial game start
            document.getElementById('start-screen').style.display = 'none';
            document.getElementById('game-area').style.display = 'block';
            document.getElementById('game-over-screen').style.display = 'none';
            
            // Reset game instructions visibility
            const gameInstructions = document.getElementById('game-instructions');
            if (gameInstructions) {
                gameInstructions.style.opacity = '1';
                gameInstructions.style.visibility = 'visible';
                gameInstructions.style.transition = 'none';
            }

            // Start countdown sequence before actual game begins
            this.startCountdown();
        } catch (error) {
            showAlert("Failed to start game. Please try again.");
        }
    }

    startCountdown() {
        this.isCountdownActive = true;
        let countdown = 3; // 3 second countdown
        
        // Show countdown overlay
        this.showCountdownOverlay(countdown);
        
        this.countdownInterval = setInterval(() => {
            countdown--;
            
            // Play countdown sound for each number (with small delay to prevent overlap)
            if (countdown > 0 && window.GameSounds) {
                setTimeout(() => {
                    window.GameSounds.playCountdown();
                }, 50);
            }
            
            this.showCountdownOverlay(countdown);
            
            if (countdown <= 0) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null;
                this.isCountdownActive = false;
                this.hideCountdownOverlay();
                this.onCountdownComplete();
            }
        }, 1000);
    }

    showCountdownOverlay(countdown) {
        let countdownElement = document.getElementById('countdown-overlay');
        if (!countdownElement) {
            countdownElement = document.createElement('div');
            countdownElement.id = 'countdown-overlay';
            countdownElement.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                font-size: 48px;
                font-weight: bold;
                padding: 20px 40px;
                border-radius: 10px;
                z-index: 1000;
                text-align: center;
            `;
            document.getElementById('game-area').appendChild(countdownElement);
        }
        
        if (countdown > 0) {
            countdownElement.textContent = countdown;
        } else {
            countdownElement.textContent = 'GO!';
        }
    }

    hideCountdownOverlay() {
        const countdownElement = document.getElementById('countdown-overlay');
        if (countdownElement) {
            countdownElement.remove();
        }
    }

    onCountdownComplete() {
        // This method will be overridden by child classes to start their specific game logic
        this.startGameTimer();
    }

    startGameTimer() {
        // Default game timer implementation - can be overridden by child classes
        this.gameInterval = setInterval(() => {
            this.time++;
            this.updateHUD();
        }, 1000);
    }

    initGame(resetLevel = true) {
        clearInterval(this.gameInterval);
        clearInterval(this.countdownInterval);
        this.gameInterval = null;
        this.countdownInterval = null;
        this.isCountdownActive = false;
        this.time = 0;
        this.score = 0;
        if (resetLevel) {
            this.level = 1;
        }

        this.app.stage.removeChildren();
        this.hideCountdownOverlay();
    }

    updateHUD() {
        document.getElementById('game-timer').textContent = this.time;
        document.getElementById('game-level').textContent = this.level;
        document.getElementById('game-score').textContent = this.score;
    }

    /**
     * Set the cursor visibility for the game canvas. Use this to hide the webpage cursor when using a custom in-canvas cursor.
     * @param {boolean} visible - If true, show the default cursor. If false, hide the cursor (set to 'none').
     */
    setGameCursorVisibility(visible) {
        if (this.app && this.app.view) {
            this.app.view.style.cursor = visible ? '' : 'none';
        }
    }

    /**
     * Add an event listener for gameplay input that is automatically disabled when the game is paused.
     * @param {string} type - The event type (e.g., 'keydown').
     * @param {function} handler - The event handler function.
     * @param {EventTarget} [target=window] - The target to attach the event to (default: window).
     */
    addGameEventListener(type, handler, target = window) {
        if (!this._inputListeners) this._inputListeners = [];
        const wrappedHandler = (event) => {
            if (this._isPaused) return;
            handler.call(this, event);
        };
        target.addEventListener(type, wrappedHandler);
        this._inputListeners.push({ type, handler: wrappedHandler, target });
    }

    /**
     * Remove all gameplay input event listeners added via addGameEventListener.
     */
    removeAllGameEventListeners() {
        if (this._inputListeners) {
            for (const { type, handler, target } of this._inputListeners) {
                target.removeEventListener(type, handler);
            }
            this._inputListeners = [];
        }
    }

    // Helper method to get the available game area dimensions
    getGameAreaDimensions() {
        return {
            width: 500,
            height: 500, // Full height available
            hudHeight: this.hudHeight,
            safeArea: {
                top: this.hudHeight,
                bottom: 500,
                left: 0,
                right: 500
            }
        };
    }

    // Helper method to check if a position is within the safe game area
    isInSafeGameArea(x, y) {
        return y >= this.hudHeight && y <= 500;
    }

    endGame(result) {
        clearInterval(this.gameInterval);
        clearInterval(this.countdownInterval);
        this.gameInterval = null;
        this.countdownInterval = null;
        this.isCountdownActive = false;
        this.hideCountdownOverlay(); 
        if (window.GameSounds) {
            window.GameSounds.playGameOver();
        }
        
        document.getElementById('end-time').textContent = this.time;
        document.getElementById('end-level').textContent = this.level;
        document.getElementById('end-score').textContent = this.score;
        document.getElementById('result-message').textContent = result;
        document.getElementById('game-over-screen').style.display = 'flex';
        document.getElementById('game-area').style.display = 'none';
    }

    victory(message = 'Victory!') {
        // Play victory sound
        if (window.GameSounds) {
            window.GameSounds.playVictory();
        }
        
        // Games can override this method to add their own victory logic
        this.endGame(message);
    }

    stopGame() {
        // Clear the game interval
        clearInterval(this.gameInterval);
        clearInterval(this.countdownInterval);
        this.gameInterval = null;
        this.countdownInterval = null;
        this.isCountdownActive = false;

        // Reset game state
        this.time = 0;
        this.score = 0;
        this.level = 1;

        // Clear the stage
        this.app.stage.removeChildren();

        // Update HUD
        this.updateHUD();

        // Reset display states
        document.getElementById('start-screen').style.display = 'flex';
        document.getElementById('game-area').style.display = 'none';
        document.getElementById('game-over-screen').style.display = 'none';

        // Reset game instructions visibility
        const gameInstructions = document.getElementById('game-instructions');
        if (gameInstructions) {
            gameInstructions.style.opacity = '1';
            gameInstructions.style.visibility = 'visible';
            gameInstructions.style.transition = 'none';
        }

        // Hide countdown overlay
        this.hideCountdownOverlay();

        // Hide the modal
        document.getElementById('game-modal').style.display = "none";
    }
}

// Make BaseGame globally available
window.BaseGame = BaseGame;