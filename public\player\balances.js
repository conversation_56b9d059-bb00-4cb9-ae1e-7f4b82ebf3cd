async function getPlayerDetails(url) {
  axios.get(url, {
    wax_id: wax.userAccount
  }).then((response) => {
    console.log(response.data[0])
    console.log(response.data)
    if (response.data && response.data[0]) {
      var r = response.data[0].gxp;
      playerData.gxp = Number(response.data[0].gxp);
      playerData.nectar = response.data[0].nectar ? Number(response.data[0].nectar) : 0;
      playerData.credits = response.data[0].credits;
      playerData.joinDate = response.data[0].date_joined;
      playerData.lastOnline = response.data[0].last_online;
      playerData.lv = Number(response.data[0].lv);
      playerData.xp = Number(response.data[0].xp);
    } else {
      console.log("No player data found for this wallet", wax.userAccount);
    }
  }, (error) => {
    console.log(error);
  })
}

async function getWaxBalances(url) {
  try {
    // Initialize default values in case the API call fails
    playerData.wax = playerData.wax || "0.00";
    playerData.dust = playerData.dust || "0.00";

    const response = await axios.get(url);

    // Check if response data and tokens exist
    if (response.data && response.data.tokens) {
      const tokens = response.data.tokens;
      tokens.forEach(token => {
        if (token.symbol === 'WAX') {
          playerData.wax = (Math.round(token.amount * 100) / 100).toFixed(2);
        }
        if ((token.contract == 'niftywizards' && token.symbol === 'DUST') || token.contract === 'niftywizards') {
          playerData.dust = (Math.round(token.amount * 100) / 100).toFixed(2);
        }
      });
    } else {
      console.log("No token data found in response");
    }

    return {
      dust: playerData.dust,
      wax: playerData.wax
    }
  } catch (error) {
    console.error("Error fetching WAX balances:", error);
    // Return default values if there's an error
    return {
      dust: playerData.dust,
      wax: playerData.wax
    }
  }
}

async function displayBalances(db_url, wax_url) {
  try {
    // Set default values for player data to prevent undefined errors
    playerData.lv = playerData.lv || 1;
    playerData.xp = playerData.xp || 0;
    playerData.gxp = playerData.gxp || 0;
    playerData.nectar = playerData.nectar || 0;
    playerData.credits = playerData.credits || 0;

    // Fetch data
    await getPlayerDetails(db_url);
    await getWaxBalances(wax_url);

    // Get XP required for next level
    const xpRequired = await getRequiredXPForNextLevel(playerData.lv);

    // Update UI elements
    if (document.getElementById('dustBalance')) {
      document.getElementById('dustBalance').innerText = abbreviateNumber(playerData.dust);
    }
    if (document.getElementById('waxBalance')) {
      document.getElementById('waxBalance').innerText = abbreviateNumber(playerData.wax);
    }
    if (document.getElementById('gxpBalance')) {
      document.getElementById('gxpBalance').innerText = abbreviateNumber(playerData.gxp);
    }
    if (document.getElementById('nectarBalance')) {
      document.getElementById('nectarBalance').innerText = abbreviateNumber(playerData.nectar);
    }
    if (document.getElementById('creditsBalance')) {
      document.getElementById('creditsBalance').innerText = abbreviateNumber(playerData.credits);
    }

    // Create and update progress bar
    const progressBar = document.createElement('div');
    progressBar.id = 'player-level-progress-bar';
    progressBar.classList.add('progress-bar', 'mesmerize', 'bottom-0', 'start-0');
    progressBar.style.width = '0%';
    progressBar.setAttribute('role', 'progressbar');
    progressBar.setAttribute('aria-valuenow', playerData.xp || 0);
    progressBar.setAttribute('aria-valuemin', 0);
    progressBar.setAttribute('aria-valuemax', xpRequired || 100);

    // Format XP values
    var cur_xp = playerData.xp || 0;
    var req_xp = xpRequired || 100;
    let xpToNext = req_xp - cur_xp;

    // Update level up tooltip
    if (xpToNext < 0) {
      $('#playerLevelBar').attr('title', 'Level Up!');
    } else {
      $('#playerLevelBar').attr('title', `${xpToNext} XP to Level Up!`);
    }

    progressBar.innerText = cur_xp + '/' + req_xp + ' XP';

    // Calculate percentage
    const percentage = cur_xp / req_xp;
    var width = percentage * 100;
    if (width > 100) {
      width = 100;
    }
    progressBar.style.width = `${width}%`;

    // Update player level display
    const playerLevel = document.querySelector('#playerLevel');
    if (playerLevel) {
      $('#playerLevel').html(`<img src="../images/ui/level_icon.png" width="12px" alt='${playerData.lv} Level'> ${playerData.lv}`);
      playerLevel.append(progressBar);
    }

    // Update login button tooltip with dates
    if (playerData.lastOnline && playerData.joinDate) {
      var dateFixed = convertDateSimple(playerData.lastOnline);
      var dateFixed2 = convertDateSimple(playerData.joinDate);
      $('#loginButton').attr('title', `Join date: ${dateFixed2} - Last online: ${dateFixed}`);
    }
  } catch (error) {
    console.error("Error displaying balances:", error);
  }
}

async function getPlayerBalances() {
 var url = 'https://express-crushie.herokuapp.com/players/';
 try {
   const response = await axios.get(url);
   if (response.data) {
     var balancesData = response.data;
     var playerBalances = {}; // Initialize the playerBalances object
     for (var i = 0; i < balancesData.length; i++) {
       var playerData = {};
       var wax_id = balancesData[i].wax_id;
       playerData.gxp = Number(balancesData[i].gxp);
       playerData.nectar = balancesData[i].nectar;
       playerData.credits = balancesData[i].credits;
       playerData.joinDate = balancesData[i].date_joined;
       playerData.lastOnline = balancesData[i].last_online;
       playerBalances[wax_id] = playerData;
     }
     return playerBalances;
   } else {
     console.log("No player data found");
     return null;
   }
 } catch (error) {
   console.log("Error occurred while fetching player data:", error);
   return null;
 }
}

async function updatePlayerBalances() {
  await getPlayerBalances();
  const newDate = new Date();
  const url = `${domain_url}/players/${wax.userAccount}`;
  const gxp = Math.round(Number(playerData.gxp));
  const change = {
    gxp: gxp,
    last_online: newDate,
    nectar: playerData.nectar,
    credits: playerData.credits,
  };
  if (!sessionToken) {
    console.error('ERROR: updatePlayerBalances - Failed to obtain sessionToken');
    return;
  }
  const config = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': sessionToken.startsWith('Bearer ') ? sessionToken : `Bearer ${sessionToken}`,
    },
  };

  try {
    const response = await axios.put(url, change, config);
    playerData.gxp = gxp;
    await updateBalanceUI();
    return true;
  } catch (error) {
    if (error.response?.status === 401) {
      try {
        sessionToken = await login();
        if (sessionToken) {
          config.headers.Authorization = sessionToken.startsWith('Bearer ') ? sessionToken : `Bearer ${sessionToken}`;
          try {
            const retryResponse = await axios.put(url, change, config);
            playerData.gxp = gxp;
            await updateBalanceUI();
            return true;
          } catch (retryError) {
            console.error('ERROR: Retry failed:', retryError.response?.data || retryError.message);
            console.error('ERROR: Retry status code:', retryError.response?.status);
            console.error('ERROR: Retry full error:', JSON.stringify(retryError, Object.getOwnPropertyNames(retryError)));
            return false;
          }
        } else {
          console.error('ERROR: Failed to refresh sessionToken');
          return false;
        }
      } catch (loginError) {
        console.error('ERROR: Login refresh error:', loginError);
        console.error('ERROR: Login refresh error details:', loginError.message, loginError.stack);
        return false;
      }
    }
    return false;
  }
}

async function updateBalanceUI() {
  document.getElementById('gxpBalance').innerText = abbreviateNumber(playerData.gxp);
  document.getElementById('nectarBalance').innerText = abbreviateNumber(playerData.nectar);
  document.getElementById('creditsBalance').innerText = abbreviateNumber(playerData.credits);
  document.getElementById('waxBalance').innerText = abbreviateNumber(playerData.wax);
  document.getElementById('dustBalance').innerText = abbreviateNumber(playerData.dust);
}