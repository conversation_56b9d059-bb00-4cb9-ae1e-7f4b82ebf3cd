const { app_url } = require("../../config");
const axios = require("axios");
const { getPlayerBalances } = require("../api/player");
const { updatePlayerBalances, addReward } = require("../api/rewards-api");
const { checkPlayerRewardLimit } = require("../api/player-limit");
const { checkGlobalRewardLimit } = require("../api/global-limit");
const { mintNFTReward, transferDUSTReward, updatePlayerPoints } = require("./blockchain-rewards");

var datestamp = new Date().toISOString().slice(0, 10);
var payload = [];
var allRewards = {};
var claimedRewards = {};
var playerBalances = {};
 
async function getClaimedNFTRewards() {
  var url = `${app_url}/rewards`;

  try {
    const response = await axios.get(url);
    const allRewards = response.data;

    // Count rewards by status and type
    const statusCounts = {};
    const typeCounts = {};

    allRewards.forEach(reward => {
      // Count by status
      if (!statusCounts[reward.status]) {
        statusCounts[reward.status] = 0;
      }
      statusCounts[reward.status]++;

      // Count by type
      if (!typeCounts[reward.type]) {
        typeCounts[reward.type] = 0;
      }
      typeCounts[reward.type]++;
    });

    playerBalances = await getPlayerBalances();
    const accumulatedRewards = new Map();

    // Filter rewards that need processing: 'Claimed', 'Player Limit', and 'Global Limit'
    const rewardsToProcess = allRewards.filter(reward =>
      reward.status === 'Claimed' ||
      reward.status === 'Player Limit' ||
      reward.status === 'Global Limit'
    );

    for (const reward of rewardsToProcess) {
      try {
        let canProcess = false;

        if (reward.status === 'Player Limit') {
          // For Player Limit rewards, check if the player limit has been reset
          const canIssuePlayerReward = await checkPlayerRewardLimit(reward.wax_id, reward.type);
          if (canIssuePlayerReward) {
            canProcess = true;
          } else {
            continue;
          }
        } else if (reward.status === 'Global Limit') {
          // For Global Limit rewards, check if the global limit has been reset
          const canIssueGlobalReward = await checkGlobalRewardLimit(reward.type);
          if (canIssueGlobalReward) {
            canProcess = true;
          } else {
            continue;
          }
        } else if (reward.status === 'Claimed') {
          // For Claimed rewards, check if the player limit is reached
          const canIssuePlayerReward = await checkPlayerRewardLimit(reward.wax_id, reward.type);
          if (canIssuePlayerReward) {
            canProcess = true;
          } else {
            // Update status to 'Player Limit'
            await updateRewardToPlayerLimit(reward.event_id);
            continue;
          }
        }

        if (canProcess) {
          // Process the blockchain operation first, then update status only if successful
          let blockchainSuccess = false;
          
          if (reward.type === 'NFT') {
            blockchainSuccess = await mintNFTReward(reward);
          } else if (reward.type === 'DUST') {
            blockchainSuccess = await transferDUSTReward(reward);
          } else {
            // For XP/GXP rewards, accumulate them for batch processing
            const playerId = reward.wax_id;
            let playerRewards = accumulatedRewards.get(playerId);
            if (!playerRewards) {
              playerRewards = [];
              accumulatedRewards.set(playerId, playerRewards);
            }
            playerRewards.push(reward);
            blockchainSuccess = true; // XP/GXP rewards are processed in batch later
          }
          
          // Only mark as "Disbursed" if blockchain operation succeeded
          if (blockchainSuccess) {
            await updateRewardStatus(reward.event_id);
          } else {
            // Update status to "Blockchain Failed" so we can track and potentially retry later
            await updateRewardToBlockchainFailed(reward.event_id);
          }
        }

        claimedRewards = {...claimedRewards, ...reward};
      } catch (error) {
        // Mark the reward as "Processing Failed" so we can track and potentially retry later
        await updateRewardToProcessingFailed(reward.event_id);
      }
    }

    // Update player balances in batch
    for (const [playerId, playerRewards] of accumulatedRewards) {
      let xpReward = 0;
      let gxpReward = 0;
      const processedRewards = []; // Track which rewards were successfully processed

      for (const reward of playerRewards) {
        if (reward.type === 'XP') {
          xpReward += Number(reward.amount);
        } else if (reward.type === 'GXP') {
          gxpReward += Number(reward.amount);
        }
        processedRewards.push(reward);
      }

      try {
        let updateSuccess = true;
        
        if (xpReward > 0) {
          const xpSuccess = await updatePlayerPoints(playerId, 'xp', xpReward);
          if (!xpSuccess) {
            updateSuccess = false;
          }
        }

        if (gxpReward > 0) {
          const gxpSuccess = await updatePlayerPoints(playerId, 'gxp', gxpReward);
          if (!gxpSuccess) {
            updateSuccess = false;
          }
        }

        // Only mark rewards as "Disbursed" if database updates succeeded
        if (updateSuccess) {
          for (const reward of processedRewards) {
            await updateRewardStatus(reward.event_id);
          }
        } else {
          for (const reward of processedRewards) {
            await updateRewardToDatabaseFailed(reward.event_id);
          }
        }
      } catch (error) {
        for (const reward of processedRewards) {
          await updateRewardToProcessingFailed(reward.event_id);
        }
      }
    } 
    // Check if any global or player caps were updated
    try {
      const globalLimitsResponse = await axios.get(`${app_url}/global-limits`);
      const playerLimitsResponse = await axios.get(`${app_url}/player-limits`);
    } catch (error) { 
    }

  } catch (error) { 
  }
}

async function updateRewardStatus(event_id) {
  var url = `${app_url}/rewards/sys/${event_id}`;

  const disbursalDate = new Date();
  let change = {
    "event_id": event_id,
    "disbursed_date": disbursalDate,
    "status": "Disbursed"
  }

  try {
    const response = await axios.put(url, change, {
      headers: {
        "Content-Type": "application/json",
      }
    });

    if (response.status === 200) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

async function updateRewardToPlayerLimit(event_id) {
  var url = `${app_url}/rewards/sys/${event_id}`;

  let change = {
    "event_id": event_id,
    "status": "Player Limit"
  }

  try {
    const response = await axios.put(url, change, {
      headers: {
        "Content-Type": "application/json",
      }
    });

    if (response.status === 200) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

async function updateRewardToBlockchainFailed(event_id) {
  var url = `${app_url}/rewards/sys/${event_id}`;

  let change = {
    "event_id": event_id,
    "status": "Blockchain Failed"
  }

  try {
    const response = await axios.put(url, change, {
      headers: {
        "Content-Type": "application/json",
      }
    });

    if (response.status === 200) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
} 

async function updateRewardToProcessingFailed(event_id) {
  var url = `${app_url}/rewards/sys/${event_id}`;

  let change = {
    "event_id": event_id,
    "status": "Processing Failed"
  }

  try {
    const response = await axios.put(url, change, {
      headers: {
        "Content-Type": "application/json",
      }
    });

    if (response.status === 200) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

async function updateRewardToDatabaseFailed(event_id) {
  var url = `${app_url}/rewards/sys/${event_id}`;

  let change = {
    "event_id": event_id,
    "status": "Database Failed"
  }

  try {
    const response = await axios.put(url, change, {
      headers: {
        "Content-Type": "application/json",
      }
    });

    if (response.status === 200) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
} 

async function processRewardResult(rewardResult, owner_id, adventure_id) {
  // If the reward already exists, return the result
  if (rewardResult.rewardExists) {
    return rewardResult;
  }

  // If there was an error but it's due to a limit being reached, we'll still create the reward with appropriate status
  let initialStatus = "Unclaimed";
  if (!rewardResult.success) {
    if (rewardResult.limitType === "global") {
      initialStatus = "Global Limit";
    } else if (rewardResult.limitType === "player") {
      initialStatus = "Player Limit";
    } else {
      // For other errors, return the error result
      return rewardResult;
    }
  }

  try {
    const rewardType = rewardResult.rewardType;
    let title, desc, schema, template, amount;

    // Set default values based on reward type
    if (rewardType === "DUST") {
      title = "DUST Reward";
      desc = `You found ${rewardResult.amount} DUST on your adventure!`;
      schema = "None";
      template = 0;
      amount = rewardResult.amount;
    } else if (rewardType === "NFT") {
      title = rewardResult.title || "NFT Reward";
      desc = rewardResult.description || `You found an NFT on your adventure!`;
      schema = rewardResult.schema || "items";
      template = rewardResult.template_id || 0;
      amount = 1;
    } else if (rewardType === "GXP") {
      title = "GXP Reward";
      desc = `You earned ${rewardResult.amount} GXP on your adventure!`;
      schema = "None";
      template = 0;
      amount = rewardResult.amount;
    } else {
      return {
        success: false,
        message: `Unknown reward type: ${rewardType}`
      };
    }

    // Call addReward with the appropriate parameters
    const result = await addReward(
      owner_id,
      adventure_id,
      rewardType,
      title,
      desc,
      schema,
      template,
      amount,
      new Date(),
      initialStatus
    );

    if (result) {
      return {
        success: true,
        message: `${rewardType} reward created successfully`,
        rewardType: rewardType,
        amount: amount
      };
    } else {
      return {
        success: false,
        message: `Failed to create ${rewardType} reward`,
        rewardType: rewardType
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Error processing reward: ${error.message}`,
      error: error.message
    };
  }
}
 
module.exports = {
  getClaimedNFTRewards,
  processRewardResult,
  updateRewardStatus,
  updateRewardToPlayerLimit,
  updateRewardToBlockchainFailed,
  updateRewardToDatabaseFailed,
  updateRewardToProcessingFailed
};