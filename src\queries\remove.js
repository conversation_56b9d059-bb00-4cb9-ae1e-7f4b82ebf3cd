// remove item
const removePlayer = "DELETE FROM new_players WHERE wax_id = $1";
const removeTeam = "DELETE from teams WHERE team_id = $1";
const removeAdventure = "DELETE from adventures WHERE adventure_id = $1 RETURNING team_id";
const removeHouse = "DELETE from houses WHERE owner_id = $1 AND asset_id= $2";
const removeReward = "DELETE from rewards WHERE event_id = $1";
const removeEscrow = "DELETE from escrow WHERE asset_id = $1";
const removeUserSession = "DELETE FROM user_sessions WHERE wax_id = $1";
// const removeGameLog = "DELETE FROM os_log WHERE wax_id = $1 AND created_at < NOW() - INTERVAL '30 days'";
//const removeGameLog = "DELETE FROM os_log WHERE created_at < NOW() - INTERVAL '30 days'";
const removeGameLog = "DELETE FROM os_log WHERE created_at::timestamp < (NOW() - INTERVAL '30 days')::timestamp";

module.exports = {
	removePlayer,
	removeTeam,
	removeAdventure,
	removeHouse,
	removeReward,
	removeEscrow,
	removeUserSession,
	removeGameLog
}
