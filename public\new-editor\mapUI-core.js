// mapUI-core.js - Core logic and state for the map editor

let selectedTile = null;
let currentMode = 'paint'; // 'paint' or 'edit'

// Getter functions
const getCurrentMode = function() { return currentMode; };
const getSelectedTile = function() { return selectedTile; };

// Make currentMode globally accessible
window.currentMode = currentMode;

// Mode switching
const switchMode = function(mode) {
  currentMode = mode;
  window.currentMode = mode;
  selectedTile = null;
  if (window.updatePendingChangesDisplay) window.updatePendingChangesDisplay();

  // --- UI feedback logic (from mapUI-ui.js) ---
  const paintModeBtn = document.getElementById('paintModeBtn');
  const editModeBtn = document.getElementById('editModeBtn');
  const paintModeInfo = document.getElementById('paintModeInfo');
  const editModeInfo = document.getElementById('editModeInfo');
  if (paintModeBtn && editModeBtn) {
    if (mode === 'paint') {
      paintModeBtn.classList.add('active', 'btn-blue');
      paintModeBtn.classList.remove('btn-unselected');
      editModeBtn.classList.remove('active', 'btn-blue');
      editModeBtn.classList.add('btn-unselected');
    } else {
      editModeBtn.classList.add('active', 'btn-blue');
      editModeBtn.classList.remove('btn-unselected');
      paintModeBtn.classList.remove('active', 'btn-blue');
      paintModeBtn.classList.add('btn-unselected');
    }
  }
  if (paintModeInfo && editModeInfo) {
    if (mode === 'paint') {
      paintModeInfo.style.display = 'block';
      editModeInfo.style.display = 'none';
    } else {
      paintModeInfo.style.display = 'none';
      editModeInfo.style.display = 'block';
    }
  }
};

// Tile painting functionality
const paintTile = function(tileIndex) {
  const tileNameInput = document.getElementById('tileName');
  const tileTypeInput = document.getElementById('tileType');
  const tileTerrainInput = document.getElementById('tileTerrain');
  const tileName = tileNameInput ? tileNameInput.value : '';
  const tileType = tileTypeInput ? tileTypeInput.value : 'grassplains';
  const tileTerrain = tileTerrainInput ? tileTerrainInput.value : 'land';
  const result = window.updateTileData(tileIndex, {
    name: tileName,
    type: tileType,
    variant: tileTerrain
  });
  if (result) {
    if (window.updateTileVisual) window.updateTileVisual(tileIndex);
    if (window.updatePendingChangesDisplay) window.updatePendingChangesDisplay();
    console.log('Painted tile ' + tileIndex + ' with:', result.newData);
  }
};

// Tile editing functionality
const loadTileForEditing = function(tileIndex) {
  const mapData = window.getMapData();
  if (tileIndex >= 0 && tileIndex < mapData.length) {
    const tile = mapData[tileIndex];
    const tileNameInput = document.getElementById('tileName');
    const tileTypeInput = document.getElementById('tileType');
    const tileTerrainInput = document.getElementById('tileTerrain');
    if (tileNameInput) tileNameInput.value = tile.name || '';
    if (tileTypeInput) tileTypeInput.value = tile.type || 'grassplains';
    if (tileTerrainInput) tileTerrainInput.value = tile.variant || 'land';
    selectedTile = tileIndex;
    if (window.highlightSelectedTile) window.highlightSelectedTile();
    const selectedTileInfo = document.getElementById('selectedTileInfo');
    if (selectedTileInfo) {
      selectedTileInfo.innerHTML = '<strong>Tile ' + (tileIndex + 1) + '</strong><br>' +
        'Name: ' + (tile.name || 'Unnamed') + '<br>' +
        'Type: ' + (tile.type || 'grassplains') + '<br>' +
        'Terrain: ' + (tile.variant || 'land');
    }
    console.log('Loaded tile ' + tileIndex + ' for editing:', tile);
  }
  if (window.updateTileTypePreview) window.updateTileTypePreview();
};

// Remove change functionality
const removeChange = function(changeIndex) {
  const pendingChanges = window.getPendingChanges();
  if (changeIndex >= 0 && changeIndex < pendingChanges.length) {
    const change = pendingChanges[changeIndex];
    const tileIndex = change.tileIndex;
    if (window.removePendingChange) window.removePendingChange(changeIndex);
    window.revertTileData(tileIndex, change.oldData);
    const remainingChanges = window.getPendingChanges();
    const hasMoreChangesForTile = remainingChanges.some(function(c) { return c.tileIndex === tileIndex; });
    if (!hasMoreChangesForTile && window.removeEditedTile) window.removeEditedTile(tileIndex);
    if (window.updateTileVisual) window.updateTileVisual(tileIndex);
    if (window.updatePendingChangesDisplay) window.updatePendingChangesDisplay();
    console.log('Removed change ' + changeIndex + ' for tile ' + tileIndex);
  }
};

// Apply tile change
const applyTileChange = function() {
  if (currentMode === 'edit' && selectedTile !== null) {
    const tileNameInput = document.getElementById('tileName');
    const tileTypeInput = document.getElementById('tileType');
    const tileTerrainInput = document.getElementById('tileTerrain');
    const tileName = tileNameInput ? tileNameInput.value : '';
    const tileType = tileTypeInput ? tileTypeInput.value : 'grassplains';
    const tileTerrain = tileTerrainInput ? tileTerrainInput.value : 'land';
    const result = window.updateTileData(selectedTile, {
      name: tileName,
      type: tileType,
      variant: tileTerrain
    });
    if (result) {
      if (window.updateTileVisual) window.updateTileVisual(selectedTile);
      if (window.updatePendingChangesDisplay) window.updatePendingChangesDisplay();
      console.log('Applied changes to tile ' + selectedTile + ':', result.newData);
    }
  } else {
    alert('Please select a tile in Edit Mode first, or use Paint Mode to click tiles directly.');
  }
};

// Clear tile selection
const clearTileSelection = function() {
  selectedTile = null;
  const selectedTileInfo = document.getElementById('selectedTileInfo');
  if (selectedTileInfo) selectedTileInfo.textContent = 'No tile selected.';
  if (window.highlightSelectedTile) window.highlightSelectedTile();
};

// Save as JSON (single row, must match map_zones table)
const saveMapAsJsonHandler = function() {
  if (window.saveMapAsJson) window.saveMapAsJson();
};

// Save all 64 zones as backup
const saveAllZonesBackupHandler = function() {
  if (window.getAllZones) {
    const allZones = window.getAllZones();
    // must match the formatting of the row for map_zones table
    const backup = allZones.map(zone => ({
      mapgrid_4: zone.mapgrid_4,
      mapgrid_16: zone.mapgrid_16,
      status: zone.status,
      gxp_paid: zone.gxp_paid,
      gxp_required: zone.gxp_required,
      zone_name: zone.zone_name,
      zone_type: zone.zone_type,
      locales: (zone.data && zone.data.locales) ? zone.data.locales.map(locale => ({
        Tile: locale.Tile || 'grassplains',
        Locale_Name: locale.Locale_Name || '',
        Terrain: locale.Terrain || 'land'
      })) : []
    }));
    const dateStr = new Date().toISOString().replace(/[:.]/g, '-');
    const dataStr = JSON.stringify(backup, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'map_zones_backup_' + dateStr + '.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
};

// Save changes to database
const saveChangesToDatabaseHandler = async function() {
  try {
    await window.saveChangesToDatabase();
    if (window.updatePendingChangesDisplay) window.updatePendingChangesDisplay();
    alert('Changes saved successfully!');
  } catch (error) {
    console.error('Error saving changes:', error);
    alert('Error saving changes: ' + error.message);
  }
};

// Clear all changes
const clearAllChanges = function() {
  const pendingChanges = window.getPendingChanges();
  if (pendingChanges.length === 0) {
    alert('No changes to clear.');
    return;
  }
  if (confirm('Are you sure you want to clear all pending changes? This action cannot be undone.')) {
    pendingChanges.forEach(function(change) {
      window.revertTileData(change.tileIndex, change.oldData);
      if (window.updateTileVisual) window.updateTileVisual(change.tileIndex);
    });
    if (window.clearPendingChanges) window.clearPendingChanges();
    if (window.clearEditedTiles) window.clearEditedTiles();
    if (window.updatePendingChangesDisplay) window.updatePendingChangesDisplay();
    if (window.updateAllEditedTilesVisual) window.updateAllEditedTilesVisual();
    if (window.clearSelectionHighlight) window.clearSelectionHighlight();
    console.log('All changes cleared');
  }
};

// Export to window
window.getCurrentMode = getCurrentMode;
window.getSelectedTile = getSelectedTile;
window.switchMode = switchMode;
window.paintTile = paintTile;
window.loadTileForEditing = loadTileForEditing;
window.removeChange = removeChange;
window.clearTileSelection = clearTileSelection;
window.saveMapAsJsonHandler = saveMapAsJsonHandler;
window.clearAllChanges = clearAllChanges;
window.saveAllZonesBackupHandler = saveAllZonesBackupHandler;
window.saveChangesToDatabaseHandler = saveChangesToDatabaseHandler;
window.applyTileChange = applyTileChange; 