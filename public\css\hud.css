#tab-menu {
  border-radius: 3px;
  display: flex;
}

#player-name{
  background: #c5ffc3;
}

/* Rewards, Ready, Napping */
#playerCountersContainer{
  display: flex;
  align-items: center;
  background: white;
  margin-left: 0.5em;
  padding: 0.75em;
 }

#playerCountersContainer ul {
  padding: 0;
  margin: 0;
}

 #playerCountersContainer li {
  cursor: default;
  display: inline-block;
  margin-right: 10px;
 }

 #playerCountersContainer li:hover {
  cursor: pointer;
}

 #playerCountersContainer img {
  margin-right: 8px;
  vertical-align: middle;
 }

 .volume-control {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(255, 233, 27, 0.8);
  border: 2px solid #333;
  border-radius: 10px;
  padding: 8px;
  display: flex;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.volume-icon {
  margin-right: 8px;
}

.volume-slider {
  width: 100px;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: url('../images/ui/buttons/slider.png') no-repeat center;
  background-size: 100% 100%;
  outline: none;
  border-radius: 4px;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: url('../images/ui/buttons/thumb.png') no-repeat center;
  background-size: contain;
  cursor: pointer;
  border: none; /* Remove border since we're using an image */
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: url('../images/ui/buttons/thumb.png') no-repeat center;
  background-size: contain;
  cursor: pointer;
  border: none; /* Remove border since we're using an image */
}

.volume-handle {
  cursor: move;
  margin-left: 8px;
  font-size: 12px;
  color: #333;
}

/* Horizontal Balance Bar Styles */
.balance-bar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px; 
  min-height: 32px;
}

.balance-box {
  display: flex;
  align-items: center;
  gap: 4px;  
  padding: 2px 8px;
  font-size: 12px; 
  transition: background 0.15s, box-shadow 0.15s;
  cursor: pointer;
  min-width: 44px;
  min-height: 24px;
}
 

.balance-box img {
  margin-right: 2px;
  vertical-align: middle;
  width: 12px;
}

@media (max-width: 600px) {
  .balance-bar {
    gap: 2px;
    padding: 2px 2px;
    min-height: 24px;
  }
  .balance-box {
    font-size: 11px;
    padding: 1px 4px;
    min-width: 32px;
    min-height: 18px;
  }
  .balance-box img {
    width: 14px;
    height: 14px;
  }
}