var Battle = (function() {
    'use strict';
    
    var currentTeam = null;
    var battleState = {
        bossHP: 70,
        turn: 1,
        phase: 1,
        actionPoints: 3,
        autoBattle: false,
        paused: false,
        effects: []
    };
    
    var BATTLE_CONFIG = {
        maxActionPoints: 3,
        atbSpeed: 1000,
        bossMaxHP: 100,
        phaseRequirements: {
            1: { tanks: 2, minHP: 200 },
            2: { healers: 1, minPower: 300 },
            3: { balanced: 3, minSpeed: 15 }
        }
    };

    function init() {
        try {
            console.log('Battle MVP: Initializing battle system...');
            var userAccount = getUrlParameter('user') || localStorage.getItem('wax_user_account');
            
            if (!userAccount) {
                console.log('Battle MVP: No user account found');
                UI.showNoDataMessage('No user account found');
                return;
            }
            
            console.log('Battle MVP: Loading data for user:', userAccount);
            
            Data.loadPlayerData(userAccount).then(function(playerData) {
                if (!playerData.teams || playerData.teams.length === 0) {
                    console.log('No teams found for user');
                    UI.showNoDataMessage('No teams found for user');
                    return;
                }
                
                var readyTeams = Data.getReadyTeams();
                if (readyTeams.length === 0) {
                    console.log('No ready teams found');
                    UI.showNoDataMessage('No ready teams found');
                    return;
                }
                
                console.log('Battle MVP: Found ready teams:', readyTeams);
                console.log('Battle MVP: Creatures data:', playerData.creatures);
                console.log('Battle MVP: Vehicles data:', playerData.vehicles);
                
                window.myTeams = playerData.teams || [];
                window.creaturesData = playerData.creatures || [];
                window.vehiclesData = playerData.vehicles || [];
                window.playerSettings = playerData.settings || {};
                
                currentTeam = readyTeams[0];
                UI.loadTeamData(currentTeam);
                UI.updateBattleUI();
                
                var teamName = currentTeam.team_name || 'Unknown Team';
                $('#team-display').text(teamName + ' (ID: ' + (currentTeam.team_id || 'Unknown') + ')');
                $('#team-name-label').text(teamName);
                
                $('#debug-status').html(
                    'User: ' + userAccount + '<br>' +
                    'Teams: ' + playerData.teams.length + '<br>' +
                    'Ready Teams: ' + readyTeams.length + '<br>' +
                    'Creatures: ' + playerData.creatures.length + '<br>' +
                    'Vehicles: ' + playerData.vehicles.length + '<br>' +
                    'Current Team: ' + (currentTeam.team_name || 'Unknown') + ' (ID: ' + (currentTeam.team_id || 'Unknown') + ')'
                );
                
            }).catch(function(error) {
                console.error('Error initializing battle:', error);
                UI.showNoDataMessage('Error loading battle data: ' + error.message);
            });
            
        } catch (error) {
            console.error('Error initializing battle:', error);
            UI.showNoDataMessage('Error loading battle data: ' + error.message);
        }
    }
    
    function getUrlParameter(name) {
        var urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }
    
    function getCurrentTeam() {
        return currentTeam;
    }
    
    function getBattleState() {
        return battleState;
    }
    
    function getBattleConfig() {
        return BATTLE_CONFIG;
    }
    
    function updateBattleState(newState) {
        for (var key in newState) {
            if (newState.hasOwnProperty(key)) {
                battleState[key] = newState[key];
            }
        }
    }
    
    function showModal(id) {
        $('#' + id).css('display', 'flex');
    }
    
    function hideModal(id) {
        $('#' + id).css('display', 'none');
    }
    
    function nextBattle() {
        hideModal('result-modal');
        init();
    }
    
    return {
        init: init,
        getCurrentTeam: getCurrentTeam,
        getBattleState: getBattleState,
        getBattleConfig: getBattleConfig,
        updateBattleState: updateBattleState,
        showModal: showModal,
        hideModal: hideModal,
        nextBattle: nextBattle
    };
})(); 