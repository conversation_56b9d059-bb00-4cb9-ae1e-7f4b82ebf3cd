// mapUI-ui.js - UI event listeners and DOM initialization for the map editor

// --- Mode/TileType Change Handlers ---
function handleTileTypeChange() {
  if (window.updateTileTypePreview) window.updateTileTypePreview();
  if (window.currentMode === 'paint') {
    const tileTypeSelect = document.getElementById('tileType');
    const type = tileTypeSelect.value;
    if (window.showMiniTilePreview) window.showMiniTilePreview(window.TILE_TEXTURES[type] || window.TILE_TEXTURES['grassplains']);
  }
}
function handleModeSwitch(mode) {
  if (mode === 'paint') {
    if (window.updateTileTypePreview) window.updateTileTypePreview();
    const tileTypeSelect = document.getElementById('tileType');
    const type = tileTypeSelect.value;
    if (window.showMiniTilePreview) window.showMiniTilePreview(window.TILE_TEXTURES[type] || window.TILE_TEXTURES['grassplains']);
    document.addEventListener('mousemove', window.updateMiniTilePreviewPosition);
  } else {
    if (window.hideMiniTilePreview) window.hideMiniTilePreview();
    document.removeEventListener('mousemove', window.updateMiniTilePreviewPosition);
  }
}

// --- UI Initialization ---
const initializeUI = function() {
  // Mode switching
  const paintModeBtn = document.getElementById('paintModeBtn');
  const editModeBtn = document.getElementById('editModeBtn');
  if (paintModeBtn) paintModeBtn.addEventListener('click', function() { window.switchMode('paint'); });
  if (editModeBtn) editModeBtn.addEventListener('click', function() { window.switchMode('edit'); });
  // Button handlers
  const clearSelectionBtn = document.getElementById('clearSelectionBtn');
  const showChangesBtn = document.getElementById('showChangesBtn');
  const saveJsonBtn = document.getElementById('saveJsonBtn');
  const clearAllChangesBtn = document.getElementById('clearAllChangesBtn');
  const saveAllZonesBackupBtn = document.getElementById('saveAllZonesBackupBtn');
  if (clearSelectionBtn) clearSelectionBtn.onclick = window.clearTileSelection;
  if (showChangesBtn) showChangesBtn.onclick = window.showChangesModal;
  if (saveJsonBtn) saveJsonBtn.onclick = window.saveMapAsJsonHandler;
  if (clearAllChangesBtn) clearAllChangesBtn.onclick = window.clearAllChanges;
  if (saveAllZonesBackupBtn) saveAllZonesBackupBtn.onclick = window.saveAllZonesBackupHandler;
  // Tile type change handler
  const tileTypeSelect = document.getElementById('tileType');
  if (tileTypeSelect) {
    tileTypeSelect.addEventListener('change', function() {
      if (window.updateTerrainTypeForTile) window.updateTerrainTypeForTile(this.value);
      if (window.updateTileTypePreview) window.updateTileTypePreview();
      if (window.currentMode === 'edit' && window.getSelectedTile && window.getSelectedTile() !== null) {
        if (window.updateTileVisual) window.updateTileVisual(window.getSelectedTile());
      }
    });
  }
  // Terrain type change handler
  const tileTerrainSelect = document.getElementById('tileTerrain');
  if (tileTerrainSelect) {
    tileTerrainSelect.addEventListener('change', function() {
      if (window.currentMode === 'edit' && window.getSelectedTile && window.getSelectedTile() !== null) {
        if (window.updateTileVisual) window.updateTileVisual(window.getSelectedTile());
      }
    });
  }
  // Initialize tile type dropdown based on current world
  if (window.updateTileTypeDropdown) window.updateTileTypeDropdown();
  const updateTileBtnContainer = document.getElementById('editModeUpdateBtnContainer');
  const updateTileBtn = document.getElementById('updateTileBtn');
  if (updateTileBtn) updateTileBtn.onclick = function() { if (window.applyTileChange) window.applyTileChange(); };
  function toggleUpdateTileBtn() {
    if (!updateTileBtnContainer) return;
    if (window.currentMode === 'edit') {
      updateTileBtnContainer.style.display = 'block';
    } else {
      updateTileBtnContainer.style.display = 'none';
    }
  }
  toggleUpdateTileBtn();
  if (paintModeBtn) paintModeBtn.addEventListener('click', toggleUpdateTileBtn);
  if (editModeBtn) editModeBtn.addEventListener('click', toggleUpdateTileBtn);
};

// --- Modal and Info Handlers ---
const showChangesModal = function() {
  const pendingChanges = window.getPendingChanges();
  const changesJson = document.getElementById('editor-changes-json');
  const changesRowJson = document.getElementById('editor-changes-row-json');
  let changesZoneInfo = document.getElementById('editor-changes-zoneinfo');
  if (!changesZoneInfo && changesRowJson && changesRowJson.parentElement) {
    changesZoneInfo = document.createElement('pre');
    changesZoneInfo.id = 'editor-changes-zoneinfo';
    changesZoneInfo.style.marginBottom = '12px';
    changesZoneInfo.style.background = '#222';
    changesZoneInfo.style.color = '#fff';
    changesZoneInfo.style.padding = '8px';
    changesZoneInfo.style.borderRadius = '4px';
    changesZoneInfo.style.fontSize = '14px';
    changesZoneInfo.style.maxHeight = '120px';
    changesZoneInfo.style.overflowY = 'auto';
    changesRowJson.parentElement.insertBefore(changesZoneInfo, changesRowJson);
  }
  if (changesJson) changesJson.textContent = JSON.stringify(pendingChanges, null, 2);
  const nav = window.getNavigation ? window.getNavigation() : { world: 0, zone: 0 };
  const allZones = window.getAllZones ? window.getAllZones() : [];
  const zone = allZones.find(z => z.mapgrid_4 === nav.world && z.mapgrid_16 === nav.zone);
  const mapData = window.getMapData ? window.getMapData() : [];
  const editedTiles = window.getEditedTiles ? window.getEditedTiles() : new Set();
  // Build HTML for locales, highlighting changed ones
  let localesHtml = '[\n';
  mapData.forEach((tile, idx) => {
    const isChanged = editedTiles.has(idx);
    const localeObj = { Tile: tile.type || 'grassplains', Locale_Name: tile.name || '', Terrain: tile.variant || 'land' };
    const localeStr = JSON.stringify(localeObj, null, 2).replace(/^{|}$/g, '');
    localesHtml += `  <div style=\"background:${isChanged ? '#b6fcb6' : 'none'};padding:2px 0;\">{${localeStr}}</div>,\n`;
  });
  localesHtml += ']';
  if (changesRowJson) {
    changesRowJson.innerHTML = localesHtml;
  }
  if (changesZoneInfo) {
    let info = {};
    if (zone) {
      info = {
        mapgrid_4: zone.mapgrid_4,
        mapgrid_16: zone.mapgrid_16,
        zone_name: zone.zone_name,
        zone_type: zone.zone_type,
        status: zone.status,
        gxp_paid: zone.gxp_paid,
        gxp_required: zone.gxp_required
      };
    } else {
      info = { mapgrid_4: nav.world, mapgrid_16: nav.zone };
    }
    changesZoneInfo.textContent = JSON.stringify(info, null, 2);
  }
  const modal = new bootstrap.Modal(document.getElementById('editor-changes-modal'));
  modal.show();
};

// --- Tile Type Dropdown Update ---
const updateTileTypeDropdown = function() {
  const tileTypeSelect = document.getElementById('tileType');
  const tileTerrainSelect = document.getElementById('tileTerrain');
  if (!tileTypeSelect) return;
  const currentWorld = window.getNavigation ? window.getNavigation().world : 0;
  tileTypeSelect.innerHTML = '';
  const worldTiles = {
    0: [ { value: 'grassplains', label: 'Grass Plains' }, { value: 'water', label: 'Water' }, { value: 'forest', label: 'Forest' }, { value: 'crushieforest', label: 'Crushie Forest' }, { value: 'castle', label: 'Castle' }, { value: 'ruins', label: 'Ruins' }, { value: 'town', label: 'Town' } ],
    1: [ { value: 'tr_water', label: 'Tropical Water' }, { value: 'tr_castle', label: 'Tropical Castle' }, { value: 'tr_island', label: 'Tropical Island' }, { value: 'tr_waterland', label: 'Tropical Waterland' } ],
    2: [ { value: 'ds_dirt', label: 'Desert Dirt' }, { value: 'ds_castle', label: 'Desert Castle' }, { value: 'ds_dunes', label: 'Desert Dunes' }, { value: 'ds_ruins', label: 'Desert Ruins' }, { value: 'ds_town', label: 'Desert Town' } ],
    3: [ { value: 'sp_normal', label: 'Space Normal' }, { value: 'sp_gas1', label: 'Space Gas' }, { value: 'sp_debris', label: 'Space Debris' }, { value: 'sp_station1', label: 'Space Station' }, { value: 'sp_gplanet1', label: 'Space Green Planet' }, { value: 'sp_dplanet1', label: 'Space Desert Planet' }, { value: 'sp_iplanet1', label: 'Space Ice Planet' }, { value: 'sp_rplanet1', label: 'Space Red Planet' } ]
  };
  const tiles = worldTiles[currentWorld] || worldTiles[0];
  tiles.forEach(function(tile) {
    const option = document.createElement('option');
    option.value = tile.value;
    option.textContent = tile.label;
    tileTypeSelect.appendChild(option);
  });
  if (tiles.length > 0) {
    tileTypeSelect.value = tiles[0].value;
    if (window.updateTerrainTypeForTile) window.updateTerrainTypeForTile(tiles[0].value);
  }
  if (tileTerrainSelect) {
    tileTerrainSelect.innerHTML = '';
    const worldTerrains = {
      0: [ { value: 'land', label: 'Land' }, { value: 'water', label: 'Water' } ],
      1: [ { value: 'water', label: 'Water' }, { value: 'land', label: 'Land' } ],
      2: [ { value: 'land', label: 'Land' } ],
      3: [ { value: 'space', label: 'Space' } ]
    };
    const terrains = worldTerrains[currentWorld] || worldTerrains[0];
    terrains.forEach(function(terrain) {
      const option = document.createElement('option');
      option.value = terrain.value;
      option.textContent = terrain.label;
      tileTerrainSelect.appendChild(option);
    });
    if (terrains.length > 0) {
      const firstTileType = tiles[0].value;
      const tileToTerrain = {
        'water': 'water', 'tr_water': 'water', 'sp_normal': 'space', 'sp_gas1': 'space', 'sp_debris': 'space', 'sp_station1': 'space', 'sp_gplanet1': 'space', 'sp_dplanet1': 'space', 'sp_iplanet1': 'space', 'sp_rplanet1': 'space'
      };
      const defaultTerrain = tileToTerrain[firstTileType] || 'land';
      tileTerrainSelect.value = defaultTerrain;
    }
  }
};

// --- Terrain Type Update ---
const updateTerrainTypeForTile = function(tileType) {
  const tileTerrainSelect = document.getElementById('tileTerrain');
  if (!tileTerrainSelect) return;
  const currentWorld = window.getNavigation ? window.getNavigation().world : 0;
  const tileToTerrain = {
    'water': 'water', 'tr_water': 'water', 'sp_normal': 'space', 'sp_gas1': 'space', 'sp_debris': 'space', 'sp_station1': 'space', 'sp_gplanet1': 'space', 'sp_dplanet1': 'space', 'sp_iplanet1': 'space', 'sp_rplanet1': 'space'
  };
  const terrainType = tileToTerrain[tileType] || 'land';
  if (tileTerrainSelect.value !== terrainType) tileTerrainSelect.value = terrainType;
};

// --- Tile Type Preview ---
const updateTileTypePreview = function() {
  const tileTypeSelect = document.getElementById('tileType');
  const tileTypePreview = document.getElementById('tileTypePreview');
  if (!tileTypeSelect || !tileTypePreview || !window.TILE_TEXTURES) return;
  const type = tileTypeSelect.value;
  tileTypePreview.src = window.TILE_TEXTURES[type] || window.TILE_TEXTURES['grassplains'];
};

// --- Pending Changes Display ---
const updatePendingChangesDisplay = function() {
  const container = document.getElementById('pendingChangesList');
  if (!container) return;
  const pendingChanges = window.getPendingChanges();
  const editedTiles = window.getEditedTiles();
  if (pendingChanges.length === 0) {
    container.innerHTML = 'No changes yet.';
  } else {
    const changesList = pendingChanges.map(function(change, index) {
      return '<div class="change-item" style="margin-bottom: 8px; padding: 6px; background: #2a2a1b; border-radius: 4px; border-left: 3px solid #ffb347; position: relative;">' +
        '<strong>Change ' + (index + 1) + ':</strong> Tile ' + (change.tileIndex + 1) + '<br>' +
        change.oldData.type + ' → ' + change.newData.type + '<br>' +
        'Name: ' + (change.newData.name || 'Unnamed') +
        '<button onclick="removeChange(' + index + ')" style="position: absolute; top: 4px; right: 4px; background: #ff4444; color: white; border: none; border-radius: 2px; padding: 2px 6px; font-size: 10px; cursor: pointer;">×</button>' +
        '</div>';
    }).join('');
    container.innerHTML = '<div style="margin-bottom: 8px; color: #ffb347; font-weight: bold;">' + pendingChanges.length + ' pending changes (' + editedTiles.size + ' tiles edited)</div>' + changesList;
  }
};

// --- Export to window ---
window.handleTileTypeChange = handleTileTypeChange;
window.handleModeSwitch = handleModeSwitch;
window.initializeUI = initializeUI;
window.showChangesModal = showChangesModal;
window.updateTileTypeDropdown = updateTileTypeDropdown;
window.updateTerrainTypeForTile = updateTerrainTypeForTile;
window.updateTileTypePreview = updateTileTypePreview;
window.updatePendingChangesDisplay = updatePendingChangesDisplay;

document.addEventListener('DOMContentLoaded', function() {
  if (window.initializeUI) window.initializeUI();
}); 