function updateNavButtons(world, zone) {
  AudioManager.playUISound('map');
  var w = Number(world) + 1;
  var z = Number(zone) + 1;
  // Remove the "active" class from all buttons
  $(".world_nav_buttons").removeClass("active");
  // Update the text of the buttons while preserving the icons
  $("#world_btn").html('<span class="nav-icon world-icon"></span> World ' + w);
  $("#zone_btn").html('<span class="nav-icon zone-icon"></span> Zone ' + z);
}

function displayAvailableVehicles(displayFunction, event){
  var i, tabcontent, tabbuttons;
  tabcontent = document.getElementsByClassName("tab-container");
  for (i = 0; i < tabcontent.length; i++) {
    tabcontent[i].style.display = "none";
  }
  tabbuttons = document.getElementsByClassName("tab-button");
  for (i = 0; i < tabbuttons.length; i++) {
    tabbuttons[i].style.background = "";
  }
  document.getElementById('inventory').style.display = "block";

  // Use the event parameter instead of the global event object
  if (event && event.currentTarget) {
    event.currentTarget.style.background = "#ededed";
  }

  // Remove active class from all inventory buttons
  $('#inventory button').removeClass('inventory-active-view');

  // Add active class to the vehicles button
  $('#inventory button[value="vehicles"]').addClass('inventory-active-view');

  // Update the current inventory view
  currentInventoryView = 'vehicles';

  displayFunction('vehicles', 'general-inventory', notInTeamFilter);
}
async function openTab(tabName, event) {
  var i, tabcontent, tabbuttons;
  tabcontent = document.getElementsByClassName("tab-container");
  for (i = 0; i < tabcontent.length; i++) {
    tabcontent[i].style.display = "none";
  }

  tabbuttons = document.getElementsByClassName("tab-button");
  for (i = 0; i < tabbuttons.length; i++) {
    tabbuttons[i].style.background = "";
  }

  document.getElementById(tabName).style.display = "block";

  // Use the event parameter instead of the global event object
  if (event && event.currentTarget) {
    event.currentTarget.style.background = "#ededed";
  }

  // Remove any existing filter container
  $('#inventory-filter-container').remove();

  if(tabName === 'inventory') {
    // Remove active class from all inventory buttons
    $('#inventory button').removeClass('inventory-active-view');

    // Add active class to the creatures button (default)
    $('#inventory button[value="creature"]').addClass('inventory-active-view');

    // Update the current inventory view
    currentInventoryView = 'creature';

    displayInventory('creature', 'general-inventory');
  }
  if(tabName === 'adventure') {
    // Remove active class from all adventure buttons
    $('#adventure button').removeClass('inventory-active-view');

    // Add active class to the In Progress button (default)
    $('#adventure button:contains("In Progress")').addClass('inventory-active-view');

    // Add filter dropdown for adventures
    addAdventuresFilterDropdown();

    reloadPlayerTeamData('In Progress');
  }
  if(tabName === 'teams') {
    // Remove active class from all team buttons
    $('#teams button').removeClass('inventory-active-view');

    // Add active class to the Ready button (default)
    $('#teams button:contains("Ready")').addClass('inventory-active-view');

    // Add filter dropdown for teams
    addTeamsFilterDropdown();

    reloadPlayerActiveTeams('Ready');
  }
  if(tabName === 'games') {
    $('#general-inventory').empty();
    // Remove any filter container for games tab
    $('#inventory-filter-container').remove();
  }
}

// Updated team type selector
async function setSelectedTeamType(value) {
  // Remove active class from all team buttons
  $('#teams button').removeClass('inventory-active-view');

  // Add active class to the clicked button
  $(`#teams button:contains('${value}')`).addClass('inventory-active-view');

  // Add filter dropdown for teams
  addTeamsFilterDropdown();

  const teamsDiv = $('#general-inventory');
  showInventoryLoadingSpinner(teamsDiv);
  
  const coreData = await getPlayerCoreData();
  const filteredTeams = coreData.teams.filter(team => team.status === value);

  teamsDiv.empty(); // Clear the spinner before adding content
  if (filteredTeams.length === 0) {
    const noTeamsMessage = createInventoryStatusMessage(`No teams found for this status: ${value}`);
    teamsDiv.append(noTeamsMessage);
  } else {
    filteredTeams.forEach(team => {
      const teamItem = createTeamItem(team);
      teamsDiv.append(teamItem);
    });
  }
}

function createTeamItem(team) {
    const teamItem = $('<div>').addClass('inventory-item')
      .attr('data-team-id', team.team_id)
      .attr('data-creatures-assigned', team.data?.creatures)
      .attr('data-vehicles-assigned', team.data?.vehicles)
      .attr('data-house-assigned', team.data?.house)
      .attr('data-team-status', team.status);

    var img = $('<img>');
    var imageUrl = getAssetImage(team.data.vehicles[0], vehiclesData);
    if (imageUrl !== '') {
      img.attr('src', imageUrl).addClass('img-team-vehicle');
    } else {
      img.attr('src', 'images/ui/team_icon.png').addClass('img-item');
    }
  const details = $("<div>", { class: "item-details" });
  const buttons = $("<div>", { class: "buttons" });
  teamItem.append(img);
  const teamIdSpan = $('<span>').text(team['team_name'] + ' #' + team['team_id']).addClass('team-name');
  details.append(teamIdSpan);
    // Validate assets for this team
    const validation = window.validateAssets ? window.validateAssets([team], vehiclesData, creaturesData, housesData)[team.team_id] : null;
    let hasMissing = validation && validation.hasMissing;
    let onlyHouseMissing = validation && validation.onlyHouseMissing;
    // House display: always show either the house name or 'None'
    let teamHouse;
    if (team.data.house && team.data.house !== 'None') {
      var houseName = getAssetInfo(team.data.house, 'name', housesData);
      if (houseName) {
        teamHouse = $('<span><img src="images/ui/house_icon_sm.png" width="12">House: ' + houseName + '</span>');
      } else {
        teamHouse = $('<span><img src="images/ui/house_icon_sm.png" width="12">House: None</span>');
      }
    } else {
      teamHouse = $('<span><img src="images/ui/house_icon_sm.png" width="12">House: None</span>');
    }
  var team_vehicle = findTeamData(myTeams, team.team_id, 'vehicles', 0);
  var team_leader = findTeamData(myTeams, team.team_id, 'creatures', 0);
  var team_leader_name = getImmutableData(team_leader, 'name', creaturesData) || 'Unknown';
  var team_vehicle_terrain = getImmutableData(team_vehicle, 'terrain', vehiclesData) || 'land';
  var terrain_span = $('<span><img src="images/ui/' + team_vehicle_terrain + '_type.png" width="12" title="Terrain: ' + team_vehicle_terrain + '">' + team_vehicle_terrain.charAt(0).toUpperCase() + team_vehicle_terrain.slice(1) + ' only</span>');
  var team_leader_span = $('<span><img src="images/ui/leader_icon.png" width="12" title="Leader: ' + team_leader_name + '">Leader: '+ team_leader_name + '</span>');

  var max_capacity = getMaxCapacity(team_vehicle, vehiclesData);
  var occupancy = team.data.creatures.length;
  var capacity_span = $('<span>');
  var cap_icon = $('<img>', {
   src: '../images/ui/vehicle_capacity.png',
   width: 12,
   height: 12,
  css: {
    'margin-right': '0px !important;'
  }
 });
  capacity_span.append(cap_icon);
  capacity_span.append(' ' + occupancy + ' / ' + max_capacity);
  details.append(capacity_span); // added this
  details.append(teamHouse);
  details.append(terrain_span);
  details.append(team_leader_span);

  if (team.status === 'Napping') {
    const timeLeft = getTimeLeft(team.nap_current, team.nap_total);
    const timer = $("<span>");
    if (team.data.house && team.data.house !== 'None') {
      const text = $("<span>").text("Nap Boosted!");
      const image = $("<img>")
      .addClass("nap-boost-icon")
      .attr("src", "../images/ui/boost_icon.png")
      .css({"width": "12px"});
      text.prepend(image);
      details.append(text);
    }
    timer.text(timeLeft);
    details.append(timer);
  }

  teamItem.append(details);
  const bar = createProgressBar(team.status, team.nap_current, team.nap_total);
  teamItem.append(bar);
  const viewButton = createViewButton(team, displayTeamViewModal);

  if (team.status === 'Ready') {
    const adventureButton = createAdventureButton(team);
    const disbandButton = createDisbandButton(team);
    // Disable adventure if missing vehicle/creature
    if (hasMissing && !onlyHouseMissing) {
      adventureButton.prop('disabled', true);
      adventureButton.css('border', '2px solid red');
    } else if (onlyHouseMissing) {
      adventureButton.css('border', '2px solid red');
    }
    buttons.append(adventureButton);
    if (team.data.house === 'None') {
      const selectHouseButton = createSelectHouseButton(team);
      buttons.append(selectHouseButton);
    } else {
      const moveHouseButton = createMoveOutHouseButton(team, updateTeamHouse, reloadPlayerTeamData)
      buttons.append(moveHouseButton);
    }
    buttons.append(disbandButton);
  }
  teamItem.append(buttons);
  buttons.append(viewButton);
  // Add red border if missing assets
  if (hasMissing) {
    teamItem.css('border', '2px solid red');
  }
  return teamItem;
}

// Updated adventure type selector
function setSelectedAdventureType(value) {
  // Remove active class from all adventure buttons
  $('#adventure button').removeClass('inventory-active-view');

  // Add active class to the clicked button
  $(`#adventure button:contains('${value}')`).addClass('inventory-active-view');

  // Add filter dropdown for adventures
  addAdventuresFilterDropdown();

  const inventory = $("#general-inventory");
  showInventoryLoadingSpinner(inventory);
  
  const filteredAdventures = adventures.filter(adventure => adventure.status === value);

  inventory.empty(); // Clear the spinner before adding content
  if (filteredAdventures.length === 0) {
    const noAdventuresMessage = createInventoryStatusMessage(`No adventures found for this status: ${value}`);
    inventory.append(noAdventuresMessage);
  } else {
    filteredAdventures.forEach(adventure => {
      const adventureItem = createAdventureItem(adventure);
      inventory.append(adventureItem);
    });
  }
}
