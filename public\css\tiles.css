.map {
	cursor: url('../images/ui/pointers/cursor-small-new.png'), auto;
    width: 512px;
    height: 512px;
    background-color: #222;
    animation-name: pulse-anim;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    -webkit-user-select: none;
    display: flex;
    flex-wrap: wrap;
		transition: all 0.3s ease-in-out;
}

.mapsquare {
    width: 32px;
    height: 32px;
    background: #139F2E;
    color: white;
    background-image: url(../images/forest_world/grass.png);
    background-repeat: no-repeat;
    background-size: contain;
    color: transparent;
    display: inline-block;
}

.tile-glow:hover {
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.75);
  filter: brightness(1.2);
}

.shadow{
	box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

#tooltip {
		background: #333;
		color: white;
		font-weight: bold;
		padding: 4px 8px;
		font-size: 13px;
		border-radius: 4px;
	}

#global_gxp {
		text-align: center;
		font-family: 'Commodore 64 Pixelized', sans-serif;
		padding: 0.75em;
}

#map-bg{
	background:black;
}

.world_nav_container{
 	text-transform: uppercase;
	font-size: 14px;
}


.worldsquare {
	width: 256px;
	height: 256px;
	color: white;
	display: flex;
	justify-content: center;
	align-content: center;
	flex-direction: column;
	text-align: center;
	background-repeat: no-repeat;
	background-size: contain;
	text-transform:uppercase;
	color: #0F9;
	-webkit-transition: color 3s, font-size 3s;
	-moz-transition: color 3s, font-size 3s;
	-o-transition: color 3s, font-size 3s;
	transition: color 3s, font-size 3s;
	-webkit-user-select: none;
	font-size: 16px;
	font-weight: bold;
  font-family: 'Press Start 2P', cursive;
  position: relative;
}

.worldsquare:hover{
	filter: brightness(1.1);
}

.world-overlay-container {
  position: absolute;
  right: 12px;
  bottom: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
  z-index: 10;
}

.unlocked-zones-text {
  font-size: 9px;
  font-weight: normal;
  color: #fff;
  background: rgba(0,0,0,0.5);
  border-radius: 4px;
  padding: 2px 6px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.world-info-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  background: rgba(0,0,0,0.5);
  border-radius: 4px;
  padding: 2px 6px;
  transition: background 0.2s;
}
.world-info-link:hover {
  background: rgba(80,80,80,0.7);
}

.world-info-text {
  font-size: 8px;
  color: #fff;
  margin-left: 4px;
  font-family: 'Press Start 2P', cursive;
  letter-spacing: 0.05em;
}

.zonesquare {
	position: relative;
	width: 128px;
	height: 128px;
	background: black;
	color: white;
	display: flex;
	justify-content: center;
	align-content: center;
	flex-direction: column;
	text-align: center;
	background-image: url('../images/ui/map-viewer/Locked_Zone_Off.png');
	background-repeat: repeat;
	-webkit-transition: font-size 3s;
	-moz-transition: font-size 3s;
	-o-transition: font-size 3s;
	transition: font-size 3s;
	-webkit-user-select: none;
	font-size: 12px;
	font-family: 'Press Start 2P', cursive;
	text-transform: uppercase;
	letter-spacing: 0.1em;
}
.zonesquare:hover {
	filter: brightness(1.1);
	/* font-size: 21px; */
}
.z_on {
  background-image: url('../images/ui/map-viewer/Locked_Zone_On.png');
	font-weight: bold;
}
.z_normal {
  background-image: url('../images/ui/map-viewer/Overlay_Border.png');
}
/* Different zone types when they have been unlocked, change color bg images */
.uw_1 {
	background-image: url('../images/ui/map-viewer/World1_UnlockedTile.png');
}
.uw_2 {
	background-image: url('../images/ui/map-viewer/World2_UnlockedTile.png');
}
.uw_3 {
	background-image: url('../images/ui/map-viewer/World3_UnlockedTile.png');
}
.uw_4 {
	background-image: url('../images/ui/map-viewer/World4_UnlockedTile.png');
}


.map-presence{
	font-size: 6px;
	font-weight: normal;
	padding: 2px;
	display: flex;
	background: rgba(0, 0, 0, 0.5);
	position: absolute;
	bottom: 0;
}

.map-presence img{
	width:10px;
	height:10px;
}

.world-presence{
	font-size: 6px;
	font-weight: normal;
	padding: 2px;
}

.map-gxp-bal {
	border: 1px solid white;
	border-radius: 6px;
	background: black;
	color: cyan;
	padding: 2px;
	margin-left: 8px;
	margin-right: 8px;
	font-size: 6px;
	letter-spacing: 0;
}

.castle {
	background: purple;
	background-image: url('../images/forest_world/castle.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.water {
	background: #24b1dd;
	background-image: url('../images/forest_world/water.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.forest {
	background: #24523a;
	background-image: url('../images/forest_world/forest.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.crushieforest {
	background: #24523a;
	background-image: url('../images/forest_world/c_forest.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.lava {
	background: red;
}
.mountain {
	background: brown;
}
.cyber {
	background: silver;
}
.fortress {
	background: magenta;
}
/* TROPIC tiles */

.tr_water {
	background-image: url('../images/tropic_world/tr_water.png');
	background-repeat: no-repeat;
}
.tr_castle {
	background-image: url('../images/tropic_world/tr_castle.png');
	background-repeat: no-repeat;
}
.tr_island {
	background-image: url('../images/tropic_world/tr_island.png');
	background-repeat: no-repeat;
}
.tr_waterland {
	background-image: url('../images/tropic_world/tr_waterland.png');
	background-repeat: no-repeat;
}

/* DESERT tiles */
.ds_dirt {
	background-image: url('../images/desert_world/ds_dirt.png');
	background-repeat: no-repeat;
}
.ds_castle {
	background-image: url('../images/desert_world/ds_castle.png');
	background-repeat: no-repeat;
}
.ds_dunes {
	background-image: url('../images/desert_world/ds_dunes.png');
	background-repeat: no-repeat;
}
.ds_ruins {
	background-image: url('../images/desert_world/ds_ruins.png');
	background-repeat: no-repeat;
}
.ds_town {
	background-image: url('../images/desert_world/ds_town.png');
	background-repeat: no-repeat;
}
/* SPACE tiles */

.sp_normal {
	background-image: url('../images/space_world/sp_normalB.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.sp_gas1 {
	background-image: url('../images/space_world/sp_gas1.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.sp_debris {
	background-image: url('../images/space_world/sp_debris.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.sp_station1 {
	background-image: url('../images/space_world/sp_station1.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.sp_gplanet1 {
	background-image: url('../images/space_world/sp_gplanet1.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.sp_dplanet1 {
	background-image: url('../images/space_world/sp_dplanet1.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.sp_iplanet1 {
	background-image: url('../images/space_world/sp_iplanet1.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.sp_rplanet1 {
	background-image: url('../images/space_world/sp_rplanet1.png');
	background-repeat: no-repeat;
	/* background-size: cover; */
}
.grassplains {
	background: #24523a;
	background-image: url('../images/forest_world/grass.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.town {
	background: #24523a;
	background-image: url('../images/forest_world/town.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.ruins {
	background: #24523a;
	background-image: url('../images/forest_world/ruins.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.swamp {
	background: beige;
}
.farm {
	background: green;
}

/* MAP RELATED CSS */
#map-unlock-gxp-required,
#map-unlock-gxp-balance {
  display: block;
  padding: 1em 1.25em;
}

#map-unlock-gxp-required {
	background: red;
	color: white;
}

#map-unlock-gxp-balance {
  margin-bottom: 1em;
}

.mapsquare-team-id {
  display: none;
}

.mapsquare:hover .mapsquare-team-id {
  display: block;
  position: absolute;
  bottom: -27px;
  left: -1px;
  padding: 5px 10px;
  color: #fff;
  font-size: 10px;
	/* font-weight:bold; */
}

.player_location{
	z-index: 15;
	position: relative;
	animation: pulse 3s ease-in-out infinite;
	border: 2px solid #0D6EFD;
}
.npc_location{
	z-index: 15;
	position: relative;
	animation: pulse 3s ease-in-out infinite;
	border: 2px solid #0D6EFD;
}

.treasure_highlight{
	z-index: 15;
	position: relative;
	animation: gold-pulse 1.5s ease-in-out infinite;
	border: 2px solid gold;
}


#main-alert {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border-radius: 10px;
  padding: 3em;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  text-align: center;
  color: #333;
  z-index: 9999;
  transition: transform 0.3s ease, opacity 0.3s ease;
  transform: translate(-50%, -60%);
}

@media (max-width: 600px) {
  #main-alert {
    width: 50%;
    font-size: 0.9rem;
  }
}
