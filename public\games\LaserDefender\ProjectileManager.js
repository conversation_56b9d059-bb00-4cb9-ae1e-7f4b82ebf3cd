class LaserDefenderProjectileManager {
    constructor(game) {
        this.game = game;
        this.playerProjectiles = [];
        this.projectileBodies = [];
        this.enemyProjectiles = [];
        this.enemyProjectileBodies = [];
        this.trajectoryLines = [];
        this.trajectoryHistories = []; // Store line history for fading
        this.fadingTrajectories = []; // { line: PIXI.Graphics, alpha: number, color: number }
        this.neonColors = [0x00FFFF, 0xFF00FF, 0x39FF14, 0xFFD700, 0x00FFEA, 0xFF61A6, 0xFFFB00];
        this.lastNeonColorIndex = 0;
    }

    createPlayerProjectile(angle, speed) {
        const color = this.neonColors[this.lastNeonColorIndex % this.neonColors.length];
        this.lastNeonColorIndex++;
        const graphics = new PIXI.Graphics();
        graphics.beginFill(color);
        graphics.drawCircle(0, 0, 6);
        graphics.endFill();
        const neonTexture = this.game.app.renderer.generateTexture(graphics);
        const projectile = new PIXI.Sprite(neonTexture);
        projectile.anchor.set(0.5, 0.5);
        projectile._neonColor = color;
        
        const turretSprite = this.game.turretManager.turret;
        const turretX = turretSprite.x;
        const turretY = turretSprite.y;
        const turretRotation = turretSprite.rotation;
        const fireAngle = turretRotation - Math.PI / 2;
        const offset = 8;
        const spawnX = turretX + Math.cos(fireAngle) * offset;
        const spawnY = turretY + Math.sin(fireAngle) * offset;
        
        projectile.x = spawnX;
        projectile.y = spawnY;
        
        const body = Matter.Bodies.circle(
            spawnX,
            spawnY,
            6,
            {
                label: 'playerProjectile',
                friction: 0,
                frictionAir: 0,
                collisionFilter: {
                    category: 0x0004,
                    mask: 0x0008
                }
            }
        );
        
        Matter.Body.setVelocity(body, {
            x: Math.cos(angle) * speed,
            y: Math.sin(angle) * speed
        });
        
        this.game.app.stage.addChild(projectile);
        this.playerProjectiles.push(projectile);
        this.projectileBodies.push(body);
        Matter.World.add(this.game.physicsManager.world, body);
        const trajectory = new PIXI.Graphics();
        trajectory.zIndex = 999;
        this.game.app.stage.addChild(trajectory);
        this.trajectoryLines.push(trajectory);
        this.trajectoryHistories.push([]); // Add history for this projectile
        if (window.VisualEffects) {
            window.VisualEffects.createSparkle(projectile.x, projectile.y, this.game.app.stage, color);
        }
    }

    createEnemyProjectile(enemy) {
        const projectile = new PIXI.Sprite(this.game.textures.enemyProjectile);
        projectile.anchor.set(0.5);
        projectile.x = enemy.x;
        projectile.y = enemy.y + 20;
        
        const body = Matter.Bodies.circle(
            enemy.x,
            enemy.y + 20,
            3,
            {
                label: 'enemyProjectile',
                friction: 0,
                frictionAir: 0,
                collisionFilter: {
                    category: 0x0002,
                    mask: 0x0001
                }
            }
        );
        
        const dx = this.game.turretManager.turretX - enemy.x;
        const dy = this.game.turretManager.turretY - enemy.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const speed = 3;
        
        Matter.Body.setVelocity(body, {
            x: (dx / distance) * speed,
            y: (dy / distance) * speed
        });
        
        this.game.app.stage.addChild(projectile);
        this.enemyProjectiles.push(projectile);
        this.enemyProjectileBodies.push(body);
        Matter.World.add(this.game.physicsManager.world, body);
    }

    updateProjectiles() {
        for (let i = this.playerProjectiles.length - 1; i >= 0; i--) {
            const projectile = this.playerProjectiles[i];
            const body = this.projectileBodies[i];
            const trajectory = this.trajectoryLines[i];
            // Draw line only if projectile is on canvas
            const onCanvas = projectile.x >= 0 && projectile.x <= this.game.app.screen.width &&
                             projectile.y >= 0 && projectile.y <= this.game.app.screen.height;
            // Remove trajectory if turret is too far from projectile
            const turretX = this.game.turretManager.turret.x;
            const turretY = this.game.turretManager.turret.y;
            const dx = projectile.x - turretX;
            const dy = projectile.y - turretY;
            const dist = Math.sqrt(dx * dx + dy * dy);
            if (dist > 600) { // If turret moved far away, remove
                if (trajectory && trajectory.parent) trajectory.parent.removeChild(trajectory);
                this.trajectoryLines.splice(i, 1);
                this.playerProjectiles.splice(i, 1);
                Matter.World.remove(this.game.physicsManager.world, body);
                this.projectileBodies.splice(i, 1);
                continue;
            }
            if (trajectory && projectile) {
                trajectory.clear();
                if (onCanvas) {
                    trajectory.alpha = 1;
                    trajectory.lineStyle(2, 0x00FFFF, 0.7);
                    trajectory.moveTo(turretX, turretY);
                    trajectory.lineTo(projectile.x, projectile.y);
                }
            }

            // --- BEGIN: Trajectory collision with enemies and enemy projectiles ---
            // Helper: distance from point to line segment
            function pointToSegmentDistance(px, py, x1, y1, x2, y2) {
                const A = px - x1;
                const B = py - y1;
                const C = x2 - x1;
                const D = y2 - y1;
                const dot = A * C + B * D;
                const len_sq = C * C + D * D;
                let param = -1;
                if (len_sq !== 0) param = dot / len_sq;
                let xx, yy;
                if (param < 0) {
                    xx = x1;
                    yy = y1;
                } else if (param > 1) {
                    xx = x2;
                    yy = y2;
                } else {
                    xx = x1 + param * C;
                    yy = y1 + param * D;
                }
                const dx = px - xx;
                const dy = py - yy;
                return Math.sqrt(dx * dx + dy * dy);
            }
            // Check enemies
            const blastRadius = 60; // Area damage radius
            let blastCenters = [];
            for (let j = this.game.enemyManager.enemies.length - 1; j >= 0; j--) {
                const enemy = this.game.enemyManager.enemies[j];
                const enemyBody = this.game.enemyManager.enemyBodies[j];
                if (!enemy || !enemyBody) continue;
                // Estimate radius from width/height
                const radius = (enemy.width || 32) / 2;
                const d = pointToSegmentDistance(enemy.x, enemy.y, turretX, turretY, projectile.x, projectile.y);
                if (d <= radius) {
                    // Remove enemy as if hit by projectile
                    this.game.app.stage.removeChild(enemy);
                    this.game.enemyManager.enemies.splice(j, 1);
                    Matter.World.remove(this.game.physicsManager.world, enemyBody);
                    this.game.enemyManager.enemyBodies.splice(j, 1);
                    if (window.VisualEffects) {
                        window.VisualEffects.createExplosion(enemy.x, enemy.y, 0xFF0000, 10, this.game.app.stage);
                        window.VisualEffects.createScorePopup(enemy.x, enemy.y, enemy.score || 10, this.game.app.stage, 0x00FF00);
                    }
                    this.game.score += enemy.score || 10;
                    this.game.powerupProgress += 5;
                    // Add blast center for area damage and FX
                    blastCenters.push({x: enemy.x, y: enemy.y});
                }
            }
            // Area damage: destroy all enemies within blastRadius of any blast center
            for (const center of blastCenters) {
                // Visual feedback: blast wave FX
                if (window.BlastWaveFX) {
                    new window.BlastWaveFX(this.game.app.stage, {
                        x: center.x,
                        y: center.y,
                        color: 0x00FFFF,
                        radius: blastRadius,
                        lineWidth: 6,
                        duration: 400
                    });
                }
                for (let j = this.game.enemyManager.enemies.length - 1; j >= 0; j--) {
                    const enemy = this.game.enemyManager.enemies[j];
                    const enemyBody = this.game.enemyManager.enemyBodies[j];
                    if (!enemy || !enemyBody) continue;
                    const distToBlast = Math.sqrt((enemy.x - center.x) ** 2 + (enemy.y - center.y) ** 2);
                    if (distToBlast <= blastRadius) {
                        this.game.app.stage.removeChild(enemy);
                        this.game.enemyManager.enemies.splice(j, 1);
                        Matter.World.remove(this.game.physicsManager.world, enemyBody);
                        this.game.enemyManager.enemyBodies.splice(j, 1);
                        if (window.VisualEffects) {
                            window.VisualEffects.createExplosion(enemy.x, enemy.y, 0xFF0000, 6, this.game.app.stage);
                            window.VisualEffects.createScorePopup(enemy.x, enemy.y, enemy.score || 10, this.game.app.stage, 0x00FF00);
                        }
                        this.game.score += enemy.score || 10;
                        this.game.powerupProgress += 5;
                    }
                }
            }
            // Check enemy projectiles
            for (let j = this.enemyProjectiles.length - 1; j >= 0; j--) {
                const eproj = this.enemyProjectiles[j];
                const ebody = this.enemyProjectileBodies[j];
                if (!eproj || !ebody) continue;
                const radius = (eproj.width || 6) / 2;
                const d = pointToSegmentDistance(eproj.x, eproj.y, turretX, turretY, projectile.x, projectile.y);
                if (d <= radius) {
                    this.game.app.stage.removeChild(eproj);
                    this.enemyProjectiles.splice(j, 1);
                    Matter.World.remove(this.game.physicsManager.world, ebody);
                    this.enemyProjectileBodies.splice(j, 1);
                    if (window.VisualEffects) {
                        window.VisualEffects.createExplosion(eproj.x, eproj.y, 0x00FFFF, 5, this.game.app.stage);
                    }
                }
            }
            // --- END: Trajectory collision with enemies and enemy projectiles ---

            // If projectile leaves canvas, start fast fade-out
            if (!onCanvas) {
                this.game.app.stage.removeChild(projectile);
                this.playerProjectiles.splice(i, 1);
                Matter.World.remove(this.game.physicsManager.world, body);
                this.projectileBodies.splice(i, 1);
                if (trajectory) {
                    // Instead of setInterval, add to fadingTrajectories
                    this.fadingTrajectories.push({ line: trajectory, alpha: 1 });
                }
                this.trajectoryLines.splice(i, 1);
                this.trajectoryHistories && this.trajectoryHistories.splice(i, 1);
                continue;
            }
        }
        
        for (let i = this.enemyProjectiles.length - 1; i >= 0; i--) {
            const projectile = this.enemyProjectiles[i];
            const body = this.enemyProjectileBodies[i];
            
            if (projectile.x < 0 || projectile.x > this.game.app.screen.width ||
                projectile.y < 0 || projectile.y > this.game.app.screen.height) {
                this.game.app.stage.removeChild(projectile);
                this.enemyProjectiles.splice(i, 1);
                Matter.World.remove(this.game.physicsManager.world, body);
                this.enemyProjectileBodies.splice(i, 1);
            }
        }

        // Handle fading trajectory lines
        for (let i = this.fadingTrajectories.length - 1; i >= 0; i--) {
            const fadeObj = this.fadingTrajectories[i];
            fadeObj.alpha -= 0.1; // Fade speed
            if (fadeObj.line.parent) fadeObj.line.alpha = Math.max(0, fadeObj.alpha);
            if (fadeObj.alpha <= 0) {
                if (fadeObj.line.parent) fadeObj.line.parent.removeChild(fadeObj.line);
                this.fadingTrajectories.splice(i, 1);
            }
        }
    }

    cleanup() {
        this.playerProjectiles.forEach(projectile => {
            if (projectile.parent) projectile.parent.removeChild(projectile);
        });
        this.enemyProjectiles.forEach(projectile => {
            if (projectile.parent) projectile.parent.removeChild(projectile);
        });
        
        this.projectileBodies.forEach(body => {
            if (body) Matter.World.remove(this.game.physicsManager.world, body);
        });
        this.enemyProjectileBodies.forEach(body => {
            if (body) Matter.World.remove(this.game.physicsManager.world, body);
        });
        
        this.playerProjectiles = [];
        this.projectileBodies = [];
        this.enemyProjectiles = [];
        this.enemyProjectileBodies = [];
        this.trajectoryLines.forEach(line => {
            if (line.parent) line.parent.removeChild(line);
        });
        this.trajectoryLines = [];
        this.trajectoryHistories = [];
        // Also clear any fading trajectories
        this.fadingTrajectories.forEach(fadeObj => {
            if (fadeObj.line.parent) fadeObj.line.parent.removeChild(fadeObj.line);
        });
        this.fadingTrajectories = [];
    }
}

window.LaserDefenderProjectileManager = LaserDefenderProjectileManager;