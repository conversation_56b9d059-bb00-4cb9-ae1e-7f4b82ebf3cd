const SHOP_CONFIG = {
    blendsPerTown: 1, // Number of blends to show per town (changeable)
    materials: {
        stone: {
            id: 19822,
            cost: 3,
            image: 'images/ui/shop/stone.png',
            name: 'Stone'
        },
        wood: {
            id: 19821,
            cost: 3,
            image: 'images/ui/shop/wood.png',
            name: 'Wood'
        },
        glass: {
            id: 19820,
            cost: 5,
            image: 'images/ui/shop/glass.png',
            name: 'Glass'
        },
        metal: {
            id: 19819,
            cost: 5,
            image: 'images/ui/shop/metal.png',
            name: 'Metal'
        }
    }
};
  
function initShop() {
    console.log('Initializing shop system...');
    
    // Add click event listeners to all town squares
    $(document).on('click', '.mapsquare.town', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const townId = $(this).attr('id');
        const townName = $(this).text();
        
        console.log('Town clicked:', townId, townName);
        
        // Check if player has a team selected for adventure
        if (typeof teamSelectedForAdventure !== 'undefined' && teamSelectedForAdventure && 
            typeof enableSetAdventure !== 'undefined' && enableSetAdventure) {
            console.log('Team selected for adventure, not showing shop');
            return;
        }
        
        showShopModal(townId, townName);
    });
    
    console.log('Shop system initialized');
}

 
function checkForTownElements() {
    const townElements = $('.mapsquare.town');
    console.log('Found town elements:', townElements.length);
    townElements.each(function(index) {
        const townId = $(this).attr('id');
        const townName = $(this).text();
        console.log(`Town ${index + 1}: ID=${townId}, Name="${townName}"`);
    });
    return townElements.length;
}
 
async function getPlayerCoinBalance() {
    try {
        // Fetch items from the 'coins' schema
        const coins = await fetchAndFilterItems('coins', null);
        
        // Count the number of coins returned
        const coinCount = coins.length;
        
        // Return the count, or 0 if no coins are found
        return coinCount || 0;
    } catch (error) {
        console.error('Error getting player coin balance:', error);
        return 0;
    }
}
 
async function showShopModal(townId, townName) {
    try {
        console.log('Showing shop modal for:', townName);
        
        // Get current coin balance and update playerData.crushiecoins
        playerData.crushiecoins = await getPlayerCoinBalance();
        console.log('Player coin balance:', playerData.crushiecoins);
        
        const shopTitle = `${townName} - Shop`;
        const availableBlend = getRandomBlend();
        console.log('Available blend:', availableBlend);
        
        const modalContent = {
            body: createShopModalBody(availableBlend),
            footer: createShopModalFooter(availableBlend)
        };
        
        // Create and show the modal
        const modal = createModal(shopTitle, modalContent, 'info-modal');
        
        // Show the modal
        $(modal).modal('show');
        
        // Ensure cancel button properly closes the modal and overlay
        const cancelButton = document.getElementById(`${modal.id}-cancel-button`);
        if (cancelButton) {
          cancelButton.addEventListener('click', function() {
            $(modal).modal('hide');
          });
        }
        
        // Add event listener for the single Blend button (smart contract call)
        $(modal).on('click', '.blend-button', async function() {
            const materialId = $(this).data('material-id');
            try {
                // Select random asset IDs from the user's wallet (crushie coin NFTs)
                const assetIds = await selectRandomCrushieCoinAssetIds(availableBlend.cost);
                // Call the blend function in blend.js
                await window.blendWithSmartContract({
                    owner: window.userAccount,
                    count: availableBlend.cost,
                    from: window.userAccount,
                    to: 'blend.nefty',
                    asset_ids: assetIds,
                    memo: 'deposit',
                    claimer: window.userAccount,
                    blend_id: materialId,
                    transferred_assets: assetIds,
                    own_assets: []
                });
                alert('Blend transaction sent!');
            } catch (err) {
                alert('Error blending: ' + err.message);
            }
        });
        
        // Clean up modal when hidden
        $(modal).on('hidden.bs.modal', function() {
            console.log('Shop modal closed');
            $(this).remove();
        });
        
    } catch (error) {
        console.error('Error showing shop modal:', error);
        // Show a simple error message
        alert('Error opening shop. Please try again.');
    }
}

 
function getRandomBlend() {
    const materials = Object.values(SHOP_CONFIG.materials);
    const randomIndex = Math.floor(Math.random() * materials.length);
    return materials[randomIndex];
}
 
function createShopModalBody(blend) {
    return `
        <div class="shop-container">
            <div class="coin-balance-bar">
                <img src="images/ui/coins_icon.png" alt="Coins" class="coin-icon">
                <span class="coin-balance">${playerData.crushiecoins} Crushie Coins</span>
            </div>
            
            <div class="shop-item">
                <div class="item-image">
                    <img src="${blend.image}" alt="${blend.name}" class="material-image">
                </div>
                <div class="item-details">
                    <h4 class="item-name">${blend.name}</h4>
                    <div class="item-cost">
                        <img src="images/ui/coins_icon.png" alt="Coins" class="cost-icon">
                        <span>${blend.cost} Crushie Coins</span>
                    </div>
                </div>
            </div>
            
            <div class="blend-info">
                <p class="blend-description">
                    <small>Blending will burn the crushie coins NFTs in exchange for a new material NFT</small>
                </p>
            </div>
        </div>
    `;
} 
function createShopModalFooter(blend) {
    const canAfford = playerData.crushiecoins >= blend.cost;
    const buttonClass = canAfford ? 'btn-primary blend-button' : 'btn-secondary blend-button disabled';
    const buttonText = canAfford ? 'Blend' : 'Not Enough Coins';
    
    // Only one Blend button remains, using blend-button class and smart contract call
    return `
        <button class="${buttonClass}" data-material-id="${blend.id}" ${!canAfford ? 'disabled' : ''}>
            ${buttonText}
        </button>
    `;
}
 
function updatePlayerCoinBalance(newBalance) {
    playerData.crushiecoins = newBalance; 
    $('.coin-balance').text(playerData.crushiecoins);
} 

// Initialize shop when document is ready
$(document).ready(function() {
    console.log('Document ready, initializing shop...');
    initShop(); 
    window.checkForTownElements = checkForTownElements;
    window.showShopModal = showShopModal;
}); 
 
async function selectRandomCrushieCoinAssetIds(count) {
    const coins = await fetchAndFilterItems('coins', null);
    if (!coins || coins.length === 0) return [];
    // Shuffle the coins array
    const shuffled = coins.sort(() => 0.5 - Math.random());
    // Select up to 'count' asset_ids
    return shuffled.slice(0, count).map(coin => coin.asset_id);
}
 
window.blendWithSmartContract = window.blendWithSmartContract || function() { alert('blend.js not loaded'); };