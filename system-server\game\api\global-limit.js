/**
 * Global Reward Limit API
 *
 * This module provides functions to check and update global reward limits.
 * It interacts with the os_global_cap table to enforce daily reward limits.
 */

const axios = require("axios");
const { app_url } = require("../../config");

/**
 * Check if a reward can be issued based on global limits
 * @param {string} rewardType - The type of reward (GXP, DUST, NFT)
 * @returns {Promise<boolean>} - Whether the reward can be issued
 */
async function checkGlobalRewardLimit(rewardType) {
  try {
    const response = await axios.get(`${app_url}/global-limits/${rewardType}`);
    const limitData = response.data;

    if (!limitData || limitData.length === 0) {
      return true; // Allow if no limit is set
    }

    const { current_count, max_limit, expires_at } = limitData[0];

    // Check if limit has expired and should be reset
    const now = new Date();
    const expiryDate = new Date(expires_at);

    if (now > expiryDate) {
      return true;
    }

    // Check if current count is below max limit
    return current_count < max_limit;

  } catch (error) {
    console.error("Error checking global reward limit:", error);
    return true; // Default to allowing rewards if there's an error
  }
}

/**
 * Update the count for a specific reward type
 * @param {string} rewardType - The type of reward (GXP, DUST, NFT)
 * @param {number} amount - The amount to add to the current count
 * @returns {Promise<void>}
 */
async function updateGlobalRewardCount(rewardType, amount) {
  try {
    // Ensure amount is a number
    const numericAmount = Number(amount);
    if (isNaN(numericAmount)) {
      return;
    }

    await axios.put(
      `${app_url}/global-limits/${rewardType}`,
      { amount: numericAmount },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

  } catch (error) {
    console.error("Error updating global reward count:", error);
  }
}

/**
 * Reset expired limits
 * This function is called by general.js to reset limits that have expired
 * @returns {Promise<boolean>} - Whether at least one limit was reset
 */
async function checkAndResetExpiredLimits() {
  try {
    const response = await axios.get(`${app_url}/global-limits`);
    const allLimits = response.data;
    const now = new Date();
    const expiredLimits = [];

    for (const limit of allLimits) {
      const expiryDate = new Date(limit.expires_at);

      if (now > expiryDate) {
        try {
          await axios.post(
            `${app_url}/global-limits/reset/${limit.reward_type}`,
            {},
            {
              headers: {
                "Content-Type": "application/json",
              },
            }
          );
        } catch (resetError) {
          console.error(`Error resetting limit for ${limit.reward_type}:`, resetError.message);
        }

        // Add to list of expired limits
        expiredLimits.push({
          reward_type: limit.reward_type
        });
      }
    }

    // Process rewards with "Global Limit" status for expired limits
    if (expiredLimits.length > 0) {
      try {
        const rewardsResponse = await axios.get(`${app_url}/rewards`);
        const allRewards = rewardsResponse.data;
        const globalLimitRewards = allRewards.filter(reward => reward.status === 'Global Limit');

        for (const reward of globalLimitRewards) {
          const matchingLimit = expiredLimits.find(limit => limit.reward_type === reward.type);

          if (matchingLimit) {
            try {
              const { updateRewardStatus } = require('../../rewards/adventure-rewards');
              await updateRewardStatus(reward.event_id);
            } catch (importError) {
              console.error(`Error importing or using updateRewardStatus:`, importError.message);
            }
          }
        }
      } catch (rewardsError) {
        console.error("Error processing rewards with Global Limit status:", rewardsError);
      }
      return true; // Indicate that at least one limit was reset
    } else {
      return false; // No limits were reset
    }
  } catch (error) {
    console.error("Error checking and resetting expired global limits:", error);
    return false; // On error, treat as no reset
  }
}

module.exports = {
  checkGlobalRewardLimit,
  updateGlobalRewardCount,
  checkAndResetExpiredLimits
};