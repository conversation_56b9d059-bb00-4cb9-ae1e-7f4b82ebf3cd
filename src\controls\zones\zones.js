const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addZone = (req, res) => {
	const {
		mapgrid_4,
		mapgrid_16,
		zone_name,
		zone_type,
		gxp_paid,
		gxp_required,
		status
	} = req.body;
	//add to db
	pool.query(queries.add.addZone, [mapgrid_4, mapgrid_16, zone_name, zone_type, gxp_paid, gxp_required, status], (error, results) => {
		res.status(201).send("NEW ZONE added successfully!");
		console.log("The NEW ZONE has been added.");
		return;
	});
};

const getMapZones = (req, res) => {
	pool.query(queries.get.getMapZones, (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const updateMapZones = (req, res) => {
	var id = req.params.id;
	var gxp_paid = req.params.gxp_paid;
	var {
		id,
		gxp_paid
	} = req.body;

	// Ensure gxp_paid is treated as a number
	gxp_paid = Number(gxp_paid);

	console.log("Server: Updating zone GXP - ID:", id, "New GXP value:", gxp_paid, "Type:", typeof gxp_paid);

	if (isNaN(gxp_paid)) {
		console.error("Error: gxp_paid is not a valid number:", gxp_paid);
		res.status(400).send("Invalid GXP value");
		return;
	}

	pool.query(queries.up.updateMapZones, [id, gxp_paid], (error, results) => {
		if (error) {
			console.error("Error updating zone GXP:", error);
			res.status(500).send("Error updating zone GXP");
			return;
		}
		console.log("Server: Zone GXP updated successfully for ID:", id, "New value:", gxp_paid);
		res.status(200).send("GXP updated successfully.");
	});
};

const updateMapZoneData = (req, res) => {
	var mapgrid_4 = req.params.mapgrid_4;
	var mapgrid_16 = req.params.mapgrid_16;
	var data = req.params.data;
	var {
		mapgrid_4,
		mapgrid_16,
		data
	} = req.body;
	pool.query(queries.up.updateMapZoneData, [mapgrid_4, mapgrid_16, data], (error, results) => {
		res.status(200).send("Zone updated successfully.");
	});
};

const unlockMapZoneCheck = (req, res) => {
	var id = req.params.id;
	var status = req.params.status;
	var {
		id,
		status
	} = req.body;
	pool.query(queries.up.unlockMapZoneCheck, [id, status], (error, results) => {
		res.status(200).send("Zone status successfully changed.");
	});
};

const updateMapZoneLocale = (req, res) => {
	var mapgrid_4 = req.params.mapgrid_4;
	var mapgrid_16 = req.params.mapgrid_16;
	var data = req.params.data;
	var position = req.params.position;
	var {
		mapgrid_4,
		mapgrid_16,
		position,
		data
	} = req.body;
	pool.query(queries.up.updateMapZoneLocale, [mapgrid_4, mapgrid_16, position, data], (error, results) => {
		res.status(200).send("Locale updated successfully.");
	});
};

module.exports = {
	addZone, getMapZones, updateMapZones, updateMapZoneData, unlockMapZoneCheck, updateMapZoneLocale
};
