class LaserDefender extends BaseGame {
    constructor() {
        super();
        this.gameState = 'waiting';
        this.playerHealth = 100;
        this.maxHealth = 100;
        this.spawnTimer = 0;
        this.lastShotTime = 0;
        this.shotCooldown = 300;
        this.powerupProgress = 0;
        this.powerupTarget = 50;
        this.healthBar = null;
        this.powerupBar = null;
        this.powerupText = null;
        this.levelConfigs = {
            1: { enemyCount: 50, spawnRate: 1400, enemySpeed: 0.7, targetScore: 500, hasShooters: false, hasShielded: false },
            2: { enemyCount: 80, spawnRate: 1400, enemySpeed: 0.84, targetScore: 900, hasShooters: false, hasShielded: false },
            3: { enemyCount: 150, spawnRate: 1400, enemySpeed: 0.98, targetScore: 1800, hasShooters: true, hasShielded: false },
            4: { enemyCount: 180, spawnRate: 1400, enemySpeed: 1.12, targetScore: 2500, hasShooters: true, hasShielded: false },
            5: { enemyCount: 210, spawnRate: 1200, enemySpeed: 1.26, targetScore: 3500, hasShooters: true, hasShielded: true },
            6: { enemyCount: 240, spawnRate: 1000, enemySpeed: 1.4, targetScore: 4800, hasShooters: true, hasShielded: true },
            7: { enemyCount: 270, spawnRate: 900, enemySpeed: 1.54, targetScore: 6500, hasShooters: true, hasShielded: true },
            8: { enemyCount: 300, spawnRate: 800, enemySpeed: 1.68, targetScore: 8500, hasShooters: true, hasShielded: true },
            9: { enemyCount: 300, spawnRate: 700, enemySpeed: 1.82, targetScore: 11000, hasShooters: true, hasShielded: true },
            10: { enemyCount: 300, spawnRate: 600, enemySpeed: 1.96, targetScore: 14000, hasShooters: true, hasShielded: true }
        };
        this.textures = {};
        this.starStreaksFX = null;
        if (this.app && this.app.renderer) {
            this.app.renderer.backgroundColor = 0x00000000;
        }
    }

    async initGame(resetLevel = true) {
        super.initGame(resetLevel);
        this.setGameCursorVisibility(false);
        if (resetLevel) {
            this.resetGame();
        }
        this.physicsManager = new LaserDefenderPhysicsManager(this);
        this.turretManager = new LaserDefenderTurretManager(this);
        this.enemyManager = new LaserDefenderEnemyManager(this);
        this.projectileManager = new LaserDefenderProjectileManager(this);
        this.powerupManager = new LaserDefenderPowerupManager(this);
        this.turretMovement = new window.LaserDefenderTurretMovement(this.turretManager, this.enemyManager, this);
        this.physicsManager.setupPhysics();
        const gameArea = this.getGameAreaDimensions();
        this.turretX = gameArea.width / 2;
        this.turretY = gameArea.height - 60;
        this.turretManager.turretX = this.turretX;
        this.turretManager.turretY = this.turretY;
        this.turretManager.createTurret();
        this.createCustomHUD();
        this.turretManager.setupControls();
        if (this.starStreaksFX) {
            this.starStreaksFX.stop();
            if (this.starStreaksFX.container && this.starStreaksFX.container.parent) {
                this.starStreaksFX.container.parent.removeChild(this.starStreaksFX.container);
            }
            this.starStreaksFX = null;
        }
        this.starStreaksFX = new window.StarStreaksFX(this.app.stage, { speed: 16, rate: 0.18, starfield: true, parallax: true, parallaxStrength: 1.5 });
        this.starStreaksFX.start();
    }

    async loadTextures() {
        const graphics = new PIXI.Graphics();
        this.textures.turret = await PIXI.Texture.from('/images/games/laserdefender/asteroid-blaster.png');
        this.textures.reticle1 = await PIXI.Texture.from('/images/games/laserdefender/laser-reticle1.png');
        this.textures.reticle2 = await PIXI.Texture.from('/images/games/laserdefender/laser-reticle2.png');
        graphics.clear();
        graphics.beginFill(0xFF0000);
        graphics.drawCircle(0, 0, 6);
        graphics.endFill();
        this.textures.laser = this.app.renderer.generateTexture(graphics);
        this.textures.asteroid = await PIXI.Texture.from('/images/games/laserdefender/asteroid.png');
        this.textures.asteroidNpc = await PIXI.Texture.from('/images/games/laserdefender/asteroid-npc.png');
        graphics.clear();
        graphics.beginFill(0xFFFF00);
        graphics.drawCircle(0, 0, 3);
        graphics.endFill();
        this.textures.enemyProjectile = this.app.renderer.generateTexture(graphics);
        graphics.clear();
        graphics.beginFill(0x00FFFF, 0.5);
        graphics.drawCircle(0, 0, 50);
        graphics.endFill();
        this.textures.shield = this.app.renderer.generateTexture(graphics);
    }

    createCustomHUD() {
        this.healthBar = new PIXI.Graphics();
        this.healthBar.x = 10;
        this.healthBar.y = 52;
        this.app.stage.addChild(this.healthBar);
        this.powerupBar = new PIXI.Graphics();
        this.powerupBar.x = 10;
        this.powerupBar.y = 59;
        this.app.stage.addChild(this.powerupBar);
        this.powerupText = new PIXI.Text('Powerup Ready!', {
            fontFamily: 'Press Start 2P',
            fontSize: 12,
            fill: 0x00FFFF
        });
        this.powerupText.x = 10;
        this.powerupText.y = 66;
        this.powerupText.visible = false;
        this.app.stage.addChild(this.powerupText);
        this.updateHUD();
    }

    onCountdownComplete() {
        this.gameState = 'playing';
        this.startGameLogic();
    }

    async startGame() {
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }
        try {
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);
            await this.loadTextures();
            this.initGame(true);
            const startScreen = document.getElementById('start-screen');
            const gameArea = document.getElementById('game-area');
            const gameOverScreen = document.getElementById('game-over-screen');
            if (startScreen) startScreen.style.display = 'none';
            if (gameArea) gameArea.style.display = 'block';
            if (gameOverScreen) gameOverScreen.style.display = 'none';
            this.startCountdown();
        } catch (error) {
            showAlert("Failed to start game. Please try again.");
        }
    }

    startGameLogic() {
        this.gameInterval = setInterval(() => {
            this.time++;
            this.updateHUD();
        }, 1000);
        this.gameLoop = setInterval(() => {
            this.updateGame();
        }, 16);
    }

    updateGame() {
        this.updateHUD();
        if (this.turretMovement) this.turretMovement.update();
        this.projectileManager.updateProjectiles();
        this.enemyManager.updateEnemies();
        this.powerupManager.updatePowerups();
        this.enemyManager.spawnEnemies();
        this.physicsManager.checkCollisions();
        this.checkWinLoseConditions();
    }

    checkWinLoseConditions() {
        const config = this.getLevelConfig();
        if (this.score >= config.targetScore) {
            this.handleWin();
            return;
        }
        if (this.playerHealth <= 0) {
            this.handleLose();
            return;
        }
    }

    getLevelConfig() {
        if (this.level <= 10) {
            return Object.assign({}, this.levelConfigs[this.level]);
        } else {
            const baseConfig = this.levelConfigs[10];
            const levelDiff = this.level - 10;
            return {
                enemyCount: baseConfig.enemyCount + levelDiff * 20,
                spawnRate: Math.max(300, baseConfig.spawnRate - levelDiff * 50),
                enemySpeed: baseConfig.enemySpeed + levelDiff * 0.2,
                targetScore: baseConfig.targetScore + levelDiff * 500,
                hasShooters: true,
                hasShielded: true,
                hasBoss: levelDiff % 3 === 0
            };
        }
    }

    updateHUD() {
        super.updateHUD();
        if (this.healthBar && this.powerupBar && this.powerupText) {
            this.healthBar.clear();
            const healthPercent = this.playerHealth / this.maxHealth;
            this.healthBar.beginFill(0xFF0000);
            this.healthBar.drawRect(0, 0, 50, 5);
            this.healthBar.endFill();
            this.healthBar.beginFill(0x00FF00);
            this.healthBar.drawRect(0, 0, 50 * healthPercent, 5);
            this.healthBar.endFill();
            this.powerupBar.clear();
            const powerupPercent = Math.min(1, this.powerupProgress / this.powerupTarget);
            this.powerupBar.beginFill(0x666666);
            this.powerupBar.drawRect(0, 0, 50, 5);
            this.powerupBar.endFill();
            this.powerupBar.beginFill(0x00FFFF);
            this.powerupBar.drawRect(0, 0, 50 * powerupPercent, 5);
            this.powerupBar.endFill();
            this.powerupText.visible = this.powerupProgress >= this.powerupTarget;
        }
    }

    handleWin() {
        this.score += 100;
        this.level++;
        this.nextLevelOrEnd();
    }

    nextLevelOrEnd() {
        if (this.level > 10) {
            this.victory('Victory!');
            return;
        }
        this.initLevel();
    }

    initLevel() {
        if (this.enemyManager) this.enemyManager.cleanup();
        if (this.projectileManager) this.projectileManager.cleanup();
        if (this.powerupManager) this.powerupManager.cleanup();
        if (this.enemyManager) {
            this.enemyManager.spawnTimer = 0;
            this.enemyManager.asteroidSpawnTimer = 0;
            this.enemyManager.npcSpawnTimer = 0;
        }
        this.updateHUD();
    }

    handleLose() {
        this.endGame('Game Over!');
    }

    endGame(result) {
        super.endGame(result);
        this.cleanup();
    }

    cleanup() {
        if (this.physicsManager) {
            this.physicsManager.cleanup();
        }
        if (this.gameInterval) {
            clearInterval(this.gameInterval);
            this.gameInterval = null;
        }
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
            this.gameLoop = null;
        }
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        if (this.turretManager) {
            this.turretManager.cleanup();
        }
        if (this.enemyManager) {
            this.enemyManager.cleanup();
        }
        if (this.projectileManager) {
            this.projectileManager.cleanup();
        }
        if (this.powerupManager) {
            this.powerupManager.cleanup();
        }
        if (this.starStreaksFX) {
            this.starStreaksFX.stop();
        }
        this.gameState = 'waiting';
        this.playerHealth = this.maxHealth;
        this.spawnTimer = 0;
        this.lastShotTime = 0;
        this.powerupProgress = 0;
    }

    resetGame() {
        this.gameState = 'waiting';
        this.playerHealth = this.maxHealth;
        this.spawnTimer = 0;
        this.lastShotTime = 0;
        this.powerupProgress = 0;
        this.time = 0;
        this.score = 0;
        this.level = 1;
        if (this.gameInterval) {
            clearInterval(this.gameInterval);
            this.gameInterval = null;
        }
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
            this.gameLoop = null;
        }
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        this.updateHUD();
        if (this.starStreaksFX) {
            this.starStreaksFX.stop();
            this.starStreaksFX.start();
        }
    }

    showPauseOverlay() {
        let pauseElement = document.getElementById('pause-overlay');
        if (!pauseElement) {
            pauseElement = document.createElement('div');
            pauseElement.id = 'pause-overlay';
            pauseElement.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                font-size: 36px;
                font-weight: bold;
                padding: 30px 60px;
                border-radius: 12px;
                z-index: 2000;
                text-align: center;
            `;
            pauseElement.textContent = 'Paused\nClick to Resume';
            document.getElementById('game-area').appendChild(pauseElement);
        } else {
            pauseElement.style.display = 'block';
        }
    }

    hidePauseOverlay() {
        const pauseElement = document.getElementById('pause-overlay');
        if (pauseElement) {
            pauseElement.style.display = 'none';
        }
    }

    _pauseGame() {
        super._pauseGame();
        this.showPauseOverlay();
    }

    _resumeGame() {
        super._resumeGame();
        this.hidePauseOverlay();
    }
}

window.LaserDefender = LaserDefender;