var Data = (function() {
    'use strict';
    
    function loadPlayerData(userAccount) {
        return new Promise(function(resolve, reject) {
            if (!userAccount) {
                reject(new Error('No user account provided'));
                return;
            }
            var teams = loadTeams();
            var creatures = loadCreatures();
            var vehicles = loadVehicles();
            var settings = loadSettings(userAccount);
            resolve({
                teams: teams,
                creatures: creatures,
                vehicles: vehicles,
                settings: settings
            });
        });
    }
    
    function loadTeams() {
        return (window.myTeams && window.myTeams.length > 0) ? window.myTeams : [];
    }
    
    function loadCreatures() {
        return (window.creaturesData && window.creaturesData.length > 0) ? window.creaturesData : [];
    }
    
    function loadVehicles() {
        return (window.vehiclesData && window.vehiclesData.length > 0) ? window.vehiclesData : [];
    }
    
    function loadSettings(userAccount) {
        var settings = localStorage.getItem('battle_settings_' + userAccount);
        return settings ? JSON.parse(settings) : getDefaultSettings();
    }
    
    function getDefaultSettings() {
        return {
            autoBattle: false,
            soundEnabled: true,
            animationsEnabled: true,
            difficulty: 'normal'
        };
    }
    
    function getReadyTeams() {
        var teams = window.myTeams || [];
        return teams.filter(function(team) {
            return team.status === 'ready' && team.data && team.data.creatures && team.data.creatures.length > 0;
        });
    }
    
    function getAssetImage(assetId, assetData) {
        if (!assetData || !assetId) return null;
        var asset = assetData.find(function(item) {
            return item.asset_id === assetId;
        });
        return asset && asset.image_url ? asset.image_url : '../images/ui/default_asset.png';
    }
    
    function getAssetInfo(assetId, field, assetData) {
        if (!assetData || !assetId) return null;
        var asset = assetData.find(function(item) {
            return item.asset_id === assetId;
        });
        return asset ? asset[field] : null;
    }
    
    function getImmutableData(assetId, field, assetData) {
        if (!assetData || !assetId) return null;
        var asset = assetData.find(function(item) {
            return item.asset_id === assetId;
        });
        return asset ? asset[field] : null;
    }
    
    function getCreatureClass(species) {
        var classMap = {
            'dragon': 'Tank',
            'phoenix': 'Healer',
            'wolf': 'DPS',
            'golem': 'Tank',
            'serpent': 'Support'
        };
        return classMap[species] || 'Unknown';
    }
    
    function saveBattleResult(userAccount, result) {
        var results = JSON.parse(localStorage.getItem('battle_results_' + userAccount) || '[]');
        results.push({
            timestamp: new Date().toISOString(),
            result: result
        });
        localStorage.setItem('battle_results_' + userAccount, JSON.stringify(results));
    }
    
    function getBattleHistory(userAccount) {
        return JSON.parse(localStorage.getItem('battle_results_' + userAccount) || '[]');
    }
    
    return {
        loadPlayerData: loadPlayerData,
        getReadyTeams: getReadyTeams,
        getAssetImage: getAssetImage,
        getAssetInfo: getAssetInfo,
        getImmutableData: getImmutableData,
        getCreatureClass: getCreatureClass,
        saveBattleResult: saveBattleResult,
        getBattleHistory: getBattleHistory
    };
})(); 