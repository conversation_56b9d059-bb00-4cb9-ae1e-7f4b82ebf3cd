// zoneGenerator.js
// Handles zone and locale creation logic

// Assumes global variables: nav, naturalTiles, tropicTiles, desertTiles, spaceTiles, updateMapZoneData, addZone
// Assumes functions: randomIntFloor, createNewLocaleName, displayZones

function createZones(array) {
  for (const zone of array) {
    addZone(zone[0], zone[1], zone[2], zone[3], zone[4], zone[5], zone[6], zone[7]);
  }
}

function generateLocale(zone) {
  let tileType;
  const world = nav.world;
  if (world === 0) {
    tileType = naturalTiles;
  } else if (world === 1) {
    tileType = tropicTiles;
  } else if (world === 2) {
    tileType = desertTiles;
  } else if (world === 3) {
    tileType = spaceTiles;
  }
  createLocale(world, zone, tileType);
}

function createNewZone() {
  let newJSONB = [];
  let terrainType;
  let tileType;
  let localeName;
  if (nav.world === 3) {
    terrainType = 'space';
    tileType = 'sp_normal';
    localeName = 'Space';
  } else if (nav.world === 2) {
    terrainType = 'land';
    tileType = 'ds_dirt';
    localeName = 'Desert';
  } else if (nav.world === 1) {
    terrainType = 'water';
    tileType = 'tr_water';
    localeName = 'Water';
  } else if (nav.world === 0) {
    terrainType = 'land';
    tileType = 'grassplains';
    localeName = 'Grass Plains';
  }
  for (let i = 0; i < 256; i++) {
    newJSONB.push({
      Tile: tileType,
      Locale: i,
      Terrain: terrainType,
      Locale_Name: localeName,
    });
  }
  const jsonObject = {
    locales: newJSONB,
  };
  updateMapZoneData(nav.world, nav.zone, jsonObject);
  setTimeout(() => {
    displayZones(nav.zone); // Assumes displayZones is defined
  }, 10000);
}

async function createLocale(mapgrid_4, mapgrid_16, tileType) {
  const newJSONB = [];
  for (let i = 0; i < 256; i++) {
    const mapgrid_256 = i;
    const c = randomIntFloor(0, 100); // Assumes randomIntFloor is defined
    let tile;
    if (c >= 0 && c < 66) {
      tile = tileType[0]; // grassplains, tr_water, desert plains, sp_normal
    } else if (c >= 66 && c < 90) {
      tile = tileType[1]; // forest, tr_water, mountain, sp_normal
    } else if (c >= 90 && c < 94) {
      tile = tileType[2]; // ruins
    } else if (c >= 94 && c < 97) {
      tile = tileType[3]; // town
    } else if (c >= 97 && c < 100) {
      tile = tileType[4]; // castle
    } else {
      tile = tileType[5];
    }

    let terrain = 'land';
    if (tile === 'water') {
      terrain = 'water';
    }
    if (tile === 'tr_water') {
      terrain = 'tr_water';
    }
    if (tile === 'sp_normal') {
      terrain = 'space';
    }
    const locale_name = createNewLocaleName(tile); // Assumes createNewLocaleName is defined
    newJSONB.push({
      Tile: tile,
      Locale: i,
      Terrain: terrain,
      Locale_Name: locale_name,
    });
  }
  const jsonObject = {
    locales: newJSONB,
  };
  alert(`UPDATED ZONE: ${nav.zone} UPDATED: ${jsonObject.locales.length} tiles`);
  await updateMapZoneData(mapgrid_4, mapgrid_16, jsonObject);
  console.log(jsonObject);
}

function createZone(mapgrid_4, tiles) {
  for (let m = 0; m < 16; m++) {
    createLocale(mapgrid_4, m, tiles);
  }
}

// Expose functions to global scope
window.createZones = createZones;
window.generateLocale = generateLocale;
window.createNewZone = createNewZone;
window.createLocale = createLocale;
window.createZone = createZone;