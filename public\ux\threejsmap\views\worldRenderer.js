async function renderWorlds() {
  resetCameraForView('worlds');
  if (typeof disposeCloudLayer === 'function') disposeCloudLayer();
  clearScene();
  const { size, gap, gridSize } = GRID_CONFIG.worlds;
  
  console.log('=== DEBUG: Rendering Worlds ===');
  console.log('Selected world:', nav.world);
  console.log('Grid size:', gridSize);
  
  // Define world order to match desired system: top-left=0, top-right=1, bottom-left=2, bottom-right=3
  const worldOrder = [
    [0, 1],  // Row 0: World 1, World 2 (top row)
    [2, 3]   // Row 1: World 3, World 4 (bottom row)
  ];
  
  for (let row = 0; row < gridSize; row++) {
    for (let col = 0; col < gridSize; col++) {
      const worldId = worldOrder[gridSize - 1 - row][col];
      const x = (col - 0.5) * (size + gap);
      const y = (row - 0.5) * (size + gap);
      

      
      const isSelected = nav.world === worldId;
      const worldBgImage = isSelected ? IMAGES.world.on : IMAGES.world.off;
      
      try {
        console.log('Creating world mesh with worldId:', worldId, 'at position (', x, ',', y, ')');
        await createClickableMesh(worldBgImage, x, y, size, 10, { 
          type: 'world', 
          id: worldId 
        });
      } catch (error) {
        console.warn(`Failed to load world image for world ${worldId}:`, error);
        createFallbackMesh(x, y, size, 10, { 
          type: 'world', 
          id: worldId 
        }, isSelected ? 0x4CAF50 : 0x9E9E9E);
      } 
      let displayWorldNumber;
      if (row === 0) {
        displayWorldNumber = col + 1; // Top row: World 1, 2
      } else {
        displayWorldNumber = col + 3; // Bottom row: World 3, 4
      }
      addLabel(`WORLD ${displayWorldNumber}`, x, y - 8, size, '#00fff0', 8, {
        textShadow: 'black 1px 2px'
      });

      // Zones unlocked (centered, above bottom info row)
      const zoneCounts = getWorldZoneCounts(worldId);
      const unlockedTextX = x + 12;
      const unlockedTextY = y + size / 2 - 32;
      addLabel(`${zoneCounts.unlocked}/16 UNLOCKED`, unlockedTextX, unlockedTextY, size - 24, '#fff', 9, {
        background: 'rgba(0,0,0,0.5)',
        borderRadius: '4px',
        padding: '2px 6px'
      });
      // Lock icon (left of zones unlocked)
      addOverlayImage(IMAGES.lock, x - size / 2 + 24, unlockedTextY + 6, 18, 13);

      // World info button (now a scroll icon in the top right corner)
      const scrollIconSize = 22;
      const scrollIconX = x + size / 2 - scrollIconSize / 2 - 6;
      const scrollIconY = y - size / 2 + scrollIconSize / 2 + 6;
      addOverlayImage(IMAGES.world.scroll, scrollIconX, scrollIconY, scrollIconSize, 20, () => {
        if (typeof displayWorldDescriptionModal === 'function' && typeof world_descriptions !== 'undefined') {
          // Convert world_descriptions object to array format for legacy compatibility
          const worldDescriptionsArray = Object.values(world_descriptions);
          // worldId is 0-based, so it should correctly map to the array index
          displayWorldDescriptionModal(worldDescriptionsArray, worldId);
        } else {
          // Fallback: show basic world info
          const worldName = `World ${worldId + 1}`;
          alert(`${worldName}\n\nWorld description not available.`);
        }
      });

      const adventureCount = getWorldAdventureCount(worldId); 
      
      if (adventureCount > 0) {
        const adventureTextX = unlockedTextX;
        const adventureTextY = unlockedTextY + 16;
        addLeftAlignedLabel(`Adventures: ${adventureCount}`, adventureTextX, adventureTextY, size - 40, '#4caf50', 10);
      }
    }
  } 
  
  if (typeof updateThreeJsNavButtons === 'function') {
    updateThreeJsNavButtons('world');
  }
  if (typeof addCanvasNavButtons === 'function') {
    addCanvasNavButtons();
  }
}