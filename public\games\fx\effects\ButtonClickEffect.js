// Button Click Effect
class ButtonClickEffect {
    constructor(visualEffects) {
        this.visualEffects = visualEffects;
    }

    // Create button click effect
    create(x, y, stage) {
        const clickEffect = this.visualEffects.getParticle();
        clickEffect.beginFill(0xFFFFFF, 0.5);
        clickEffect.drawCircle(0, 0, 20);
        clickEffect.endFill();
        
        clickEffect.x = x;
        clickEffect.y = y;
        clickEffect.alpha = 0.5;
        clickEffect.scale.set(0.5);
        
        stage.addChild(clickEffect);
        
        // Animate click effect
        let scale = 0.5;
        let alpha = 0.5;
        
        const animate = () => {
            scale += 0.2;
            alpha -= 0.1;
            
            clickEffect.scale.set(scale);
            clickEffect.alpha = alpha;
            
            if (alpha <= 0) {
                stage.removeChild(clickEffect);
                this.visualEffects.returnParticle(clickEffect);
            } else {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
} 