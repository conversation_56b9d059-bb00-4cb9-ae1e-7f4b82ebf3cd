// Declare all variables at the top level so they're accessible in module.exports
let addSessionToken, addPlayer, addGameLog, addTeam, addAdventure, addReward, addEscrow, addZone, addHouse, addBorrowedItem;
let getUserSessionById, getUserSessionByToken, getPlayers, getPlayerById, getPlayerStatsById, getPlayerLevelById, getPlayerSettingsById;
let getMapZones, getTeams, getTeamsByOwnerId, getTeamByTeamId, getGameStats, getGameLog;
let getAdventures, getAdventuresByOwnerId, getAdventuresByTeamId, getRewards, getRewardsByOwnerId, getRewardsByEventId;
let getHouses, getHousesByOwnerId, getHousesByRenterId, getHousesByAssetId, getHousesByStatus, getHousesByOwnerIdAndStatus;
let getEscrow<PERSON><PERSON><PERSON>setId, getEscrow;
let removeUserSession, removeB<PERSON><PERSON>edI<PERSON>, removePlayer, removeTeam, removeAdventure, removeHouse, removeReward, remove<PERSON><PERSON>, removeGameLog;
let updatePlayer, updatePlayerLevel, updatePlayerXP, updatePlayerStats, updatePlayerCredits, updatePlayerNectar, updatePlayerGXP;
let assignReward, updateReward, updateRewardBySystem, updateTeam, updateTeamHouse, updateTeamLocation, updateTeamStatus, updateTeamNap, updateTeamNapBatch;
let updateAdventure, updateHouse, updateHouseStatus, updateEscrow, updateMapZones, updateMapZoneData, unlockMapZoneCheck, updateMapZoneLocale;

// First block - add functions
try {
  const sessionsModule = require("./sessions/sessions");
  addSessionToken = sessionsModule.addSessionToken;

  const playerModule = require("./player/player");
  addPlayer = playerModule.addPlayer;

  const statisticsModule = require("./statistics/statistics");
  addGameLog = statisticsModule.addGameLog;

  const teamsModule = require("./teams/teams");
  addTeam = teamsModule.addTeam;

  const adventuresModule = require("./adventures/adventures");
  addAdventure = adventuresModule.addAdventure;

  const rewardsModule = require("./rewards/rewards");
  addReward = rewardsModule.addReward;

  const rentalsModule = require("./rentals/rentals");
  addEscrow = rentalsModule.addEscrow;
  addBorrowedItem = rentalsModule.addBorrowedItem;

  const zonesModule = require("./zones/zones");
  addZone = zonesModule.addZone;

  const housesModule = require("./houses/houses");
  addHouse = housesModule.addHouse;
} catch (error) {
  throw error;
}

// Second block - get functions
try {
  const sessionsModule = require("./sessions/sessions");
  getUserSessionById = sessionsModule.getUserSessionById;
  getUserSessionByToken = sessionsModule.getUserSessionByToken;

  const playerModule = require("./player/player");
  getPlayers = playerModule.getPlayers;
  getPlayerById = playerModule.getPlayerById;
  getPlayerStatsById = playerModule.getPlayerStatsById;
  getPlayerLevelById = playerModule.getPlayerLevelById;
  getPlayerSettingsById = playerModule.getPlayerSettingsById;

  const zonesModule = require("./zones/zones");
  getMapZones = zonesModule.getMapZones;

  const teamsModule = require("./teams/teams");
  getTeams = teamsModule.getTeams;
  getTeamsByOwnerId = teamsModule.getTeamsByOwnerId;
  getTeamByTeamId = teamsModule.getTeamByTeamId;

  const statisticsModule = require("./statistics/statistics");
  getGameStats = statisticsModule.getGameStats;
  getGameLog = statisticsModule.getGameLog;

  const adventuresModule = require("./adventures/adventures");
  getAdventures = adventuresModule.getAdventures;
  getAdventuresByOwnerId = adventuresModule.getAdventuresByOwnerId;
  getAdventuresByTeamId = adventuresModule.getAdventuresByTeamId;

  const rewardsModule = require("./rewards/rewards");
  getRewards = rewardsModule.getRewards;
  getRewardsByOwnerId = rewardsModule.getRewardsByOwnerId;
  getRewardsByEventId = rewardsModule.getRewardsByEventId;

  const housesModule = require("./houses/houses");
  getHouses = housesModule.getHouses;
  getHousesByOwnerId = housesModule.getHousesByOwnerId;
  getHousesByRenterId = housesModule.getHousesByRenterId;
  getHousesByAssetId = housesModule.getHousesByAssetId;
  getHousesByStatus = housesModule.getHousesByStatus;
  getHousesByOwnerIdAndStatus = housesModule.getHousesByOwnerIdAndStatus;

  const rentalsModule = require("./rentals/rentals");
  getEscrowByAssetId = rentalsModule.getEscrowByAssetId;
  getEscrow = rentalsModule.getEscrow;
} catch (error) {
  throw error;
}

// Third block - remove functions
try {
  const sessionsModule = require("./sessions/sessions");
  removeUserSession = sessionsModule.removeUserSession;

  const rentalsModule = require("./rentals/rentals");
  removeBorrowedItem = rentalsModule.removeBorrowedItem;

  const playerModule = require("./player/player");
  removePlayer = playerModule.removePlayer;

  const teamsModule = require("./teams/teams");
  removeTeam = teamsModule.removeTeam;

  const adventuresModule = require("./adventures/adventures");
  removeAdventure = adventuresModule.removeAdventure;

  const housesModule = require("./houses/houses");
  removeHouse = housesModule.removeHouse;

  const rewardsModule = require("./rewards/rewards");
  removeReward = rewardsModule.removeReward;

  const rentalsModule2 = require("./rentals/rentals");
  removeEscrow = rentalsModule2.removeEscrow;

  const statisticsModule = require("./statistics/statistics");
  removeGameLog = statisticsModule.removeGameLog;
} catch (error) {
  throw error;
}

// Fourth block - update functions
try {
  const playerModule = require("./player/player");
  updatePlayer = playerModule.updatePlayer;

  const playerLevelsModule = require("./player/player-levels");
  updatePlayerLevel = playerLevelsModule.updatePlayerLevel;
  updatePlayerXP = playerLevelsModule.updatePlayerXP;
  updatePlayerStats = playerLevelsModule.updatePlayerStats;

  const updateBalancesModule = require("./update-balances");
  updatePlayerCredits = updateBalancesModule.updatePlayerCredits;
  updatePlayerNectar = updateBalancesModule.updatePlayerNectar;
  updatePlayerGXP = updateBalancesModule.updatePlayerGXP;

  const rewardsModule = require("./rewards/rewards");
  assignReward = rewardsModule.assignReward;
  updateReward = rewardsModule.updateReward;
  updateRewardBySystem = rewardsModule.updateRewardBySystem;

  const teamsModule = require("./teams/teams");
  updateTeam = teamsModule.updateTeam;
  updateTeamHouse = teamsModule.updateTeamHouse;
  updateTeamLocation = teamsModule.updateTeamLocation;
  updateTeamStatus = teamsModule.updateTeamStatus;
  updateTeamNap = teamsModule.updateTeamNap;
  updateTeamNapBatch = teamsModule.updateTeamNapBatch;

  const adventuresModule = require("./adventures/adventures");
  updateAdventure = adventuresModule.updateAdventure;

  const housesModule = require("./houses/houses");
  updateHouse = housesModule.updateHouse;
  updateHouseStatus = housesModule.updateHouseStatus;

  const rentalsModule = require("./rentals/rentals");
  updateEscrow = rentalsModule.updateEscrow;

  const zonesModule = require("./zones/zones");
  updateMapZones = zonesModule.updateMapZones;
  updateMapZoneData = zonesModule.updateMapZoneData;
  unlockMapZoneCheck = zonesModule.unlockMapZoneCheck;
  updateMapZoneLocale = zonesModule.updateMapZoneLocale;
} catch (error) {
  throw error;
}

module.exports = {
  addPlayer,
  addTeam,
  addAdventure,
  addReward,
  addEscrow,
  addZone,
  addBorrowedItem,
  addGameLog,
  addSessionToken,
  addHouse,

  getMapZones,
  getGameStats,
  getPlayers,
  getTeams,
  getAdventures,
  getHouses,
  getRewards,
  getEscrow,
  getPlayerById,
  getPlayerStatsById,
  getTeamsByOwnerId,
  getTeamByTeamId,
  getAdventuresByTeamId,
  getAdventuresByOwnerId,
  getRewardsByOwnerId,
  getRewardsByEventId,
  getHousesByOwnerId,
  getHousesByRenterId,
  getHousesByAssetId,
  getHousesByStatus,
  getHousesByOwnerIdAndStatus,
  getEscrowByAssetId,
  getGameLog,
  getUserSessionById,
  getPlayerLevelById,
  getPlayerSettingsById,
  getUserSessionByToken,

  removePlayer,
  removeTeam,
  removeAdventure,
  removeHouse,
  removeReward,
  removeEscrow,
  removeBorrowedItem,
  removeUserSession,
  removeGameLog,

  updatePlayer,
  updatePlayerCredits,
  updatePlayerNectar,
  updatePlayerXP,
  updatePlayerGXP,
  updatePlayerLevel,
  updateTeam,
  updateTeamHouse,
  updateTeamLocation,
  updateTeamStatus,
  updateTeamNap,
  updateTeamNapBatch,
  updateAdventure,
  updateHouse,
  updateHouseStatus,
  updateReward,
  updateRewardBySystem,
  updateEscrow,
  updateMapZones,
  updateMapZoneData,
  unlockMapZoneCheck,
  updateMapZoneLocale
}