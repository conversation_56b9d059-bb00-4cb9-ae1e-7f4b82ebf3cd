// Ripple Effect
class RippleEffect {
    constructor(visualEffects) {
        this.visualEffects = visualEffects;
    }

    // Create ripple effect
    create(x, y, stage, color = 0x00FFFF) {
        const ripple = this.visualEffects.getParticle();
        ripple.lineStyle(2, color, 1);
        ripple.drawCircle(0, 0, 10);
        
        ripple.x = x;
        ripple.y = y;
        ripple.alpha = 1;
        
        stage.addChild(ripple);
        
        // Animate ripple
        let radius = 10;
        let alpha = 1;
        
        const animate = () => {
            radius += 3;
            alpha -= 0.02;
            
            ripple.clear();
            ripple.lineStyle(2, color, alpha);
            ripple.drawCircle(0, 0, radius);
            
            if (alpha <= 0) {
                stage.removeChild(ripple);
                this.visualEffects.returnParticle(ripple);
            } else {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
} 