var UI = (function() {
    'use strict';
    
    function loadTeamData(team) {
        console.log('Loading team data:', team);
        
        $('#team-display').text((team.team_name) + ' (ID: ' + (team.team_id) + ')');
        
        var vehicleId = team.data && team.data.vehicles ? team.data.vehicles[0] : null;
        if (vehicleId) {
            var vehicleImg = Data.getAssetImage(vehicleId, window.vehiclesData);
            var vehicleName = Data.getAssetInfo(vehicleId, 'name', window.vehiclesData);
            var vehicleTerrain = Data.getImmutableData(vehicleId, 'terrain', window.vehiclesData) || 'land';
            
            console.log('Vehicle data:', { vehicleId: vehicleId, vehicleImg: vehicleImg, vehicleName: vehicleName, vehicleTerrain: vehicleTerrain });
            
            $('#vehicle-img').attr('src', vehicleImg || '../images/ui/team_icon.png');
            $('#vehicle-name').text(vehicleName);
            $('#vehicle-terrain').text('Terrain: ' + vehicleTerrain);
        } else {
            $('#vehicle-img').attr('src', '../images/ui/team_icon.png');
            $('#vehicle-name').text('No Vehicle');
            $('#vehicle-terrain').text('Terrain: Unknown');
        }
        
        var creaturesContainer = $('#team-creatures');
        creaturesContainer.empty();
        
        var creatures = team.data && team.data.creatures ? team.data.creatures : [];
        if (creatures.length === 0) {
            creaturesContainer.html('<div class="loading">No creatures in team</div>');
            updateTeamSummary([]);
        } else {
            var creatureData = [];
            creatures.forEach(function(creatureId, index) {
                var creatureImg = Data.getAssetImage(creatureId, window.creaturesData);
                var creatureName = Data.getAssetInfo(creatureId, 'name', window.creaturesData);
                var creatureSpecies = Data.getImmutableData(creatureId, 'species', window.creaturesData);
                var creatureClass = Data.getCreatureClass(creatureSpecies);
                
                console.log('Creature data:', { creatureId: creatureId, creatureImg: creatureImg, creatureName: creatureName, creatureSpecies: creatureSpecies, creatureClass: creatureClass });
                
                creatureData.push({
                    id: creatureId,
                    name: creatureName,
                    species: creatureSpecies,
                    class: creatureClass,
                    hp: 100 + Math.floor(Math.random() * 50),
                    power: 50 + Math.floor(Math.random() * 50)
                });
                
                var creatureCard = createCreatureCard(creatureId, creatureImg, creatureName, creatureSpecies, creatureClass, index);
                creaturesContainer.append(creatureCard);
            });
            
            updateTeamSummary(creatureData);
        }
        
        loadAvailableCreatures();
        
        var terrain = Data.getImmutableData(vehicleId, 'terrain', window.vehiclesData) || 'land';
        loadVehicleSkills(vehicleId, terrain);
    }
    
    function createCreatureCard(creatureId, img, name, species, creatureClass, index, hp, power) {
        var speed = Math.floor(Math.random() * 3) + 2;
        var maxHP = hp || (100 + Math.floor(Math.random() * 50));
        var currentHP = maxHP;
        var creaturePower = power || Math.floor(Math.random() * 50) + 50;
        
        return '<div class="creature-card" data-creature-id="' + creatureId + '" data-type="' + species.toLowerCase() + '" data-index="' + index + '" onclick="Actions.selectCreature(' + index + ')">' +
            '<img src="' + (img || '../images/ui/creatures_icon.png') + '" alt="' + (name) + '" onerror="this.src=\'../images/ui/creatures_icon.png\'">' +
            '<div class="creature-info">' +
            '<div class="creature-name">' + (name) + '</div>' +
            '<div class="creature-type">' + species + ' - ' + creatureClass + '</div>' +
            '<div class="creature-stats">' +
            '<span class="stat-hp">HP: ' + currentHP + '/' + maxHP + '</span>' +
            '<span class="stat-power">Power: ' + creaturePower + '</span>' +
            '</div>' +
            '</div>' +
            '<div class="atb-container">' +
            '<div class="atb-bar">' +
            '<div class="atb-fill" data-speed="' + speed + '" style="width: ' + Math.floor(Math.random() * 100) + '%"></div>' +
            '</div>' +
            '</div>' +
            '<div class="creature-overlay">' +
            '<div class="overlay-content">' +
            '<i class="fas fa-mouse-pointer"></i>' +
            '</div>' +
            '</div>' +
            '</div>';
    }
    
    function loadAvailableCreatures() {
        if (!window.creaturesData || !window.creaturesData.length) {
            $('#available-creatures').html('<div class="loading">No creatures available</div>');
            return;
        }
        
        var currentTeam = Battle.getCurrentTeam();
        var availableCreatures = window.creaturesData.filter(function(creature) {
            return !currentTeam.data || !currentTeam.data.creatures || !currentTeam.data.creatures.includes(creature.asset_id);
        });
        
        console.log('Available creatures for swapping:', availableCreatures.length);
        
        var container = $('#available-creatures');
        container.empty();
        
        if (availableCreatures.length === 0) {
            container.html('<div class="loading">No available creatures for swapping</div>');
            return;
        }
        
        availableCreatures.slice(0, 6).forEach(function(creature) {
            var img = Data.getAssetImage(creature.asset_id, window.creaturesData);
            var name = Data.getAssetInfo(creature.asset_id, 'name', window.creaturesData);
            var species = Data.getImmutableData(creature.asset_id, 'species', window.creaturesData);
            var creatureClass = Data.getCreatureClass(species);
            
            var creatureDiv = $('<div class="creature-card" onclick="Actions.swapCreature(\'' + creature.asset_id + '\')">' +
                '<img src="' + (img || '../images/ui/creatures_icon.png') + '" alt="' + (name) + '" onerror="this.src=\'../images/ui/creatures_icon.png\'">' +
                '<div class="creature-info">' +
                '<div class="creature-name">' + (name) + '</div>' +
                '<div class="creature-type">' + species + ' - ' + creatureClass + '</div>' +
                '<div class="creature-hp">ATB Speed: ' + (Math.floor(Math.random() * 3) + 2) + '</div>' +
                '</div>' +
                '</div>');
            container.append(creatureDiv);
        });
    }
    
    function loadVehicleSkills(vehicleId, terrain) {
        var container = $('#vehicle-skills');
        container.empty();
        
        var terrainSkills = {
            'land': [
                { name: 'Ground Assault', icon: '../images/ui/boost_icon.png', description: 'Deal 50 damage to all enemies' },
                { name: 'Terrain Mastery', icon: '../images/ui/boost_icon.png', description: 'ATB speed +50% for 3 turns' }
            ],
            'water': [
                { name: 'Tidal Wave', icon: '../images/ui/boost_icon.png', description: 'Deal 60 damage and slow enemies' },
                { name: 'Aqua Boost', icon: '../images/ui/boost_icon.png', description: 'Heal team for 40 HP' }
            ],
            'space': [
                { name: 'Cosmic Blast', icon: '../images/ui/boost_icon.png', description: 'Deal 80 damage to single target' },
                { name: 'Gravity Well', icon: '../images/ui/boost_icon.png', description: 'Reduce enemy ATB by 30%' }
            ]
        };
        
        var skills = terrainSkills[terrain] || terrainSkills['land'];
        
        skills.forEach(function(skill) {
            var skillDiv = $('<button class="action-btn" onclick="Actions.useVehicleSkill(\'' + skill.name.toLowerCase().replace(' ', '_') + '\')">' +
                '<img src="' + skill.icon + '" alt="' + skill.name + '">' +
                skill.name +
                '</button>');
            container.append(skillDiv);
        });
    }
    
    function updateBattleUI() {
        var battleState = Battle.getBattleState();
        $('#turn-number').text(battleState.turn);
        $('#phase-number').text(battleState.phase);
        $('#action-points').text(battleState.actionPoints);
        $('#boss-hp-text').text(battleState.bossHP + '%');
        
        $('.action-btn').each(function() {
            var cost = parseInt($(this).data('cost')) || 0;
            if (cost > battleState.actionPoints) {
                $(this).addClass('disabled');
            } else {
                $(this).removeClass('disabled');
            }
        });
    }
    
    function updateTeamSummary(creatures) {
        var teamSize = creatures.length;
        var totalHP = creatures.reduce(function(sum, c) {
            return sum + (c.hp || 100);
        }, 0);
        var totalPower = creatures.reduce(function(sum, c) {
            return sum + (c.power || 50);
        }, 0);
        
        $('#team-size').text(teamSize);
        $('#team-hp').text(totalHP);
        $('#team-power').text(totalPower);
    }
    
    function addBattleEffect(effect) {
        var effectDiv = $('<div class="battle-effect ' + effect.type + '">' + effect.message + '</div>');
        $('#battle-effects-overlay').append(effectDiv);
        
        setTimeout(function() {
            effectDiv.remove();
        }, 3000);
    }
    
    function showNotification(message, type) {
        type = type || 'info';
        var notification = $('<div class="notification ' + type + '">' +
            '<i class="fas fa-' + (type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle') + '"></i>' +
            '<span>' + message + '</span>' +
            '</div>');
        
        $('#notification-container').append(notification);
        
        setTimeout(function() {
            notification.fadeOut(function() {
                notification.remove();
            });
        }, 3000);
    }
    
    function addLogEntry(message, type) {
        type = type || 'system';
        var logEntry = $('<div class="log-entry ' + type + '">' + message + '</div>');
        $('#battle-log').append(logEntry);
        $('#battle-log').scrollTop($('#battle-log')[0].scrollHeight);
    }
    
    function showTooltip(element, content) {
        $('#tooltip .tooltip-content').text(content);
        $('#tooltip').css({
            display: 'block',
            left: element.offset().left + element.width() / 2,
            top: element.offset().top - 40
        });
    }
    
    function hideTooltip() {
        $('#tooltip').css('display', 'none');
    }
    
    function showNoDataMessage(message) {
        $('#team-creatures').html('<div class="loading">' + message + '</div>');
        $('#available-creatures').html('<div class="loading">' + message + '</div>');
        $('#vehicle-skills').html('<div class="loading">' + message + '</div>');
    }
    
    return {
        loadTeamData: loadTeamData,
        createCreatureCard: createCreatureCard,
        loadAvailableCreatures: loadAvailableCreatures,
        loadVehicleSkills: loadVehicleSkills,
        updateBattleUI: updateBattleUI,
        updateTeamSummary: updateTeamSummary,
        addBattleEffect: addBattleEffect,
        showNotification: showNotification,
        addLogEntry: addLogEntry,
        showTooltip: showTooltip,
        hideTooltip: hideTooltip,
        showNoDataMessage: showNoDataMessage
    };
})(); 