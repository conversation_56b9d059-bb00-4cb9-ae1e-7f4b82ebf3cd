function displayNewTeamContainer() {
  $('#new-team-inventory-header').html('<img src="../images/ui/hud/hud_teams.png" style="width:12px"> New Team Capacity: ' + newTeamObject.creatures.length + ' / ' + newTeamObject.capacity);
  $('#new-team-selected-assets').empty();
  for (var j = 0; j < newTeamObject.vehicles.length; j++) {
    var vehicleId = newTeamObject.vehicles[j];
    var img = getAssetImage(vehicleId, vehiclesData);
    var name =  getAssetInfo(vehicleId, 'name', vehiclesData);
    if (img) {
      appendAssetImageAndId('#new-team-selected-assets', 'grid-item hover-bg-light', 'vehicle', vehicleId, img, name);
    }
  }
  for (var i = 0; i < newTeamObject.creatures.length; i++) {
    var creatureId = newTeamObject.creatures[i];
    var img = getAssetImage(creatureId, creaturesData);
    var name =  getAssetInfo(creatureId, 'name', creaturesData);
    if (img) {
      appendAssetImageAndId('#new-team-selected-assets', 'grid-item hover-bg-light', 'creature', creatureId, img, name);

      // Add click handler to allow unselecting creatures
      $('#new-team-selected-assets').find(`div[title='${creatureId}']`).last().css('cursor', 'pointer').on('click', function() {
        const assetId = $(this).attr('title');
        removeCreatureFromTeam(assetId);
      });
    }
  }
}

function removeCreatureFromTeam(creatureId) {
  const index = newTeamObject.creatures.indexOf(creatureId);
  if (index !== -1) {
    newTeamObject.creatures.splice(index, 1);
    $('#' + creatureId).removeClass('selected-inventory-item');
    displayNewTeamContainer();
    showAlert('Creature removed from team');
  }
}

function addVehicleToTeam(item, id) {
  if (newTeamObject.vehicles.length > 0) {
    $('#' + newTeamObject.vehicles[0]).removeClass('selected-team-member');
    newTeamObject.vehicles = [];
  }
  $('#' + id).addClass('selected-team-member');
  var inNewTeam = newTeamObject.vehicles.includes(item.asset_id);
  if (inNewTeam) {
    showAlert('Already in a team!');
    return false;
  }
  newTeamObject.vehicles.push(item.asset_id);
  newTeamObject.capacity = getMaxCapacity(item.asset_id, vehiclesData);
  newTeamObject.terrain = item.immutable_data.terrain;

  showAlert(`You selected a ${item.template.immutable_data.terrain} vehicle for your new team. Now let's add some friends to our team!`);
  displayCreateTeamModal(displayInventory, notInTeamFilter);
  return true;
}

function addCreatureToTeam(item, id) {
  var inNewTeam = newTeamObject.creatures.includes(item.asset_id);
  if (inNewTeam) {
    showAlert('This creature is already in a team!');
    return false;
  }
  if (isTeamFull(newTeamObject)) {
    showAlert('There is no room in the vehicle or no team vehicle selected.');
    return false;
  }
  newTeamObject.creatures.push(item.asset_id);
  $('#' + id).addClass('selected-inventory-item');
  return true;
}

function addToTeam(schema, item, id) {
  var inTeam = isInTeam(schema, item, myTeams);
  if (inTeam.inTeam) {
    showAlert(`The asset ID#${item.asset_id} is already in team ${inTeam.teamId}!`);
    return;
  }

  let success = false;
  if (schema === "vehicles") {
    success = addVehicleToTeam(item, id);
  } else if (schema === "creature") {
    success = addCreatureToTeam(item, id);
  }

  if (success) {
    displayNewTeamContainer();
  }
}

async function confirmTeam() {
  if (newTeamObject.vehicles.length > 0 && newTeamObject.creatures.length > 0) {
    var newTeamName = generateTeamName();
    await addTeam(newTeamName, newTeamObject.vehicles, newTeamObject.creatures);
    await reloadPlayerData.all();
    await displayInventory(currentInventoryView, 'general-inventory');
    showAlert(`Team ${newTeamName} Created Successfully!`);

    newTeamObject.creatures = [];
    newTeamObject.vehicles = [];
    newTeamObject.capacity = 0;

    $('#new-team-inventory-header').empty();
    $('#new-team-selected-assets').empty();

    displayNewTeamContainer();

    $('#create-team-modal').css('display', 'none');
  } else {
    showAlert('You must add at least 1x vehicle and 1x creature to confirm team.');
  }
}

async function displayTeamConfirmModal() {
  var body = 'New Team: \n';
  for (i in newTeamObject) {
    var assets = newTeamObject[i].vehicles;
    body += assets;
  }
  for (i in newTeamObject) {
    var assets = newTeamObject[i].creatures;
    body += assets;
  }
  $('#map-unlock').css('display', 'block');
  $("#map-unlock-title").text("Confirm Team");
  $("#map-unlock-body").text(body);
  var newTeamName = generateTeamName();
  await addTeam(newTeamName, newTeamObject.vehicles, newTeamObject.creatures);
  await reloadPlayerData.all();
  await displayInventory(currentInventoryView, 'general-inventory');
}

async function displayTeamViewModal(teamid) {
  await ensureDataLoaded();
  clearTeamViewBody();

  const team = findTeamById(myTeams, teamid);
  if (!team) return;

  // Validate assets for this team
  const validation = window.validateAssets ? window.validateAssets([team], vehiclesData, creaturesData, housesData)[team.team_id] : null;
  displayTeamInfo(team, validation);
  $('#team-view-title').text(team.team_name);
  $('#team-view').css('display', 'block');
}

async function ensureDataLoaded() {
  if (Object.keys(vehiclesData).length === 0 || Object.keys(creaturesData).length === 0) {
    await reloadPlayerData.all();
  }
}
window.displayTeamViewModal = displayTeamViewModal;

function clearTeamViewBody() {
  $('#team-view-body').empty();
}

function displayTeamInfo(team, validation) {
  displayHouseInfo(team, validation);
  displayTeamVehicles(team.data.vehicles, validation);
  displayTeamCreatures(team.data.creatures, validation);
}

function createCopyIdIcon(assetId) {
  const icon = $('<img>', {
    src: 'images/ui/asset-info.png',
    width: 12,
    height: 12,
    title: 'Click to copy Asset ID!',
    css: { cursor: 'pointer', 'vertical-align': 'middle', 'margin-left': '6px', 'margin-right': '2px' }
  });
  const text = $('<span>', {
    text: 'Copy ID',
    css: { 'font-size': '11px', 'color': '#007bff', 'cursor': 'pointer', 'vertical-align': 'middle' }
  });
  const wrapper = $('<span>', { style: 'margin-left: 4px;' });
  wrapper.append(icon).append(text);
  wrapper.on('click', function(e) {
    e.stopPropagation();
    if (navigator.clipboard) {
      navigator.clipboard.writeText(assetId);
    } else {
      // fallback for older browsers
      const tempInput = $('<input>');
      $('body').append(tempInput);
      tempInput.val(assetId).select();
      document.execCommand('copy');
      tempInput.remove();
    }
  });
  return wrapper;
}

function displayHouseInfo(team, validation) {
  let houseHtml;
  const onlyHouseMissing = validation && validation.onlyHouseMissing;
  if (team.data.house) {
    const houseId = team.data.house;
    const houseName = getAssetInfo(houseId, 'name', housesData);
    const houseImg = getAssetImage(houseId, housesData);
    houseHtml = $('<div class="team-item"></div>');
    if (houseImg) {
      houseHtml.append($('<img>').attr('src', houseImg).width(80));
    }
    houseHtml.append(' House: ' + (houseName || 'No House Assigned'));
    if (houseId && houseId !== 'None') {
      houseHtml.append(createCopyIdIcon(houseId));
    }
    if (onlyHouseMissing) {
      houseHtml.css('border', '2px solid red');
    }
  } else {
    houseHtml = $('<div class="team-item">No house assigned.</div>');
    if (onlyHouseMissing) {
      houseHtml.css('border', '2px solid red');
    }
  }
  $('#team-view-body').append(houseHtml);
}

function displayTeamVehicles(vehicleIds, validation) {
  const missingVehicles = validation && validation.missing.vehicle ? validation.missing.vehicle : [];
  vehicleIds.forEach(vehicleId => {
    const img = getAssetImage(vehicleId, vehiclesData);
    const name = getAssetInfo(vehicleId, 'name', vehiclesData);
    const isMissing = missingVehicles.includes(vehicleId);
    if (img) {
      const el = $(`<div class="team-item"></div>`);
      el.append($('<img>').attr('src', img).width(80));
      el.append(' ' + name);
      el.append(createCopyIdIcon(vehicleId));
      if (isMissing) {
        el.css('border', '2px solid red');
      }
      $('#team-view-body').append(el);
    }
  });
}

function displayTeamCreatures(creatureIds, validation) {
  const missingCreatures = validation && validation.missing.creatures ? validation.missing.creatures : [];
  creatureIds.forEach((creatureId, index) => {
    const img = getAssetImage(creatureId, creaturesData);
    const name = getAssetInfo(creatureId, 'name', creaturesData);
    const isLeader = (index === 0);
    const leaderIcon = isLeader ? '<span class="icon leader-icon" title="Team Leader"></span> ' : '';
    const isMissing = missingCreatures.includes(creatureId);
    if (img) {
      const el = $(`<div class="team-item"></div>`);
      el.append($('<img>').attr('src', img).width(80));
      el.append(' ' + leaderIcon + name);
      el.append(createCopyIdIcon(creatureId));
      if (isMissing) {
        el.css('border', '2px solid red');
      }
      $('#team-view-body').append(el);
    }
  });
}

function handleTeamSelected(teamId, button) {
  teamSelectedForAdventure = teamId;
  if (!enableSetAdventure) {
    enableSetAdventure = true;
    $(button).find('.button-text').text('Team Selected!');
    $(button).removeClass('btn-success').addClass('btn-danger');
    $('button[data-start-btn]').not(button)
      .removeClass('btn-danger')
      .addClass('btn-success');
  } else {
    enableSetAdventure = false;
  }
}

async function addTeam(teamName, vehicles, creatures) {
  if (teamName == null) {
    teamName = "Unnamed Team";
  }

  let newTeam = {
    "owner_id": wax.userAccount,
    "team_name": teamName,
    "mapgrid_4": 0,
    "mapgrid_16": 0,
    "mapgrid_256": 0,
    "nap_current": 0,
    "nap_total": 100,
    "status": "Napping",
    "data": {
      "vehicles": vehicles,
      "creatures": creatures,
      "house": "None",
      "terrain": newTeamObject.terrain
    }
  };

  try {
    const response = await postTeamData(newTeam);
    if (response.status === 201) {
      // No console.log here
    }
  } catch (error) {
    // No console.log here
  }
}

async function deleteTeam(teamid) {
  try {
    const result = await deleteTeamApi(teamid);

    if (result.success) {
      showAlert(`Successfully deleted team ID: ${teamid}`);
      AudioManager.playUISound('cancel');
      await reloadPlayerTeamData('Ready');
      await updatePlayerCounters(playerCounter, myTeams, playerAdventures);
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    showAlert(`Unable to disband team: ${error.message}`);
  }
}

function countAndCategorizeTeams(allAdventures, playerAdventures) {
  const teamCounts = { playerTeams: {}, otherTeams: {} };

  for (let worldId = 0; worldId < 4; worldId++) {
    teamCounts.playerTeams[worldId] = {};
    teamCounts.otherTeams[worldId] = {};
    for (let zoneId = 0; zoneId < 16; zoneId++) {
      teamCounts.playerTeams[worldId][zoneId] = 0;
      teamCounts.otherTeams[worldId][zoneId] = 0;
    }
  }

  if (!allAdventures || allAdventures.length === 0) {
    return teamCounts;
  }

  for (const team of allAdventures) {
    if (!team.mapgrid_4 && team.mapgrid_4 !== 0) {
      continue;
    }

    const isPlayer = team.owner_id === wax.userAccount;
    const world = Number(team.mapgrid_4);
    const zone = Number(team.mapgrid_16);

    if (world < 0 || world > 3 || zone < 0 || zone > 15) {
      continue;
    }

    if (isPlayer) {
      teamCounts.playerTeams[world][zone]++;
    } else {
      teamCounts.otherTeams[world][zone]++;
    }
  }

  return teamCounts;
}

window.countAndCategorizeTeams = countAndCategorizeTeams; 
