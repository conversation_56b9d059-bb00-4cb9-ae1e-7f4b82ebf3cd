// DrawingCanvas.js - Reusable drawing canvas for mini games
class DrawingCanvas {
    constructor(width, height, options = {}) {
        this.width = width;
        this.height = height;
        this.background = options.background || null;
        this.cursor = options.cursor || null;
        this.hasDrawnAnything = false;
        this.container = new PIXI.Container();
        this.currentColor = options.defaultColor || 0x222222;
        this.currentBrushSize = options.defaultBrushSize || 24;
        this.smoothing = options.defaultSmoothing || 0.5;
        this.points = [];
        this.maxPoints = 20;
        this.strokes = [];
        this.currentStroke = null;
        this._renderer = options.renderer || (PIXI.Application && PIXI.Application.shared && PIXI.Application.shared.renderer) || (PIXI.Renderer && PIXI.Renderer.shared) || (PIXI.autoDetectRenderer && PIXI.autoDetectRenderer());
        this._initBrushSystem();
        this.createBackground();
        this.setupInteraction();
        if (options.enableHUD !== false) {
            this.hud = new DrawingHUD(this, options.hudOptions || {});
        }
        if (typeof Matter !== 'undefined' && options.enablePhysics !== false) {
            this.brushPhysics = new BrushPhysics({
                flowRate: 0.1,
                maxParticles: 100
            });
            this.brushPhysics.setBrushType('small');
            this.physicsLayer = new PIXI.Container();
            this.container.addChild(this.physicsLayer);
        }
    }
    _initBrushSystem() {
        this.renderTexture = PIXI.RenderTexture.create({ width: this.width, height: this.height });
        this.drawBuffer = new PIXI.Container();
        this.spritePool = new window.SpritePoolGlobal();
        this.brushGenerator = new window.BrushGeneratorGlobal(this._renderer);
        this.brushTexture = null;
        this._updateBrush();
        this.renderSprite = new PIXI.Sprite(this.renderTexture);
        this.renderSprite.width = this.width;
        this.renderSprite.height = this.height;
        this.renderSprite.x = 0;
        this.renderSprite.y = 0;
        this.renderSprite.alpha = 1;
        this.renderSprite.blendMode = PIXI.BLEND_MODES.NORMAL;
        // Don't add renderSprite yet - wait until after background is created
    }
    createBackground() {
        if (this.background) {
            const texture = PIXI.Texture.from(this.background);
            const tilingSprite = new PIXI.TilingSprite(texture, this.width, this.height);
            this.bgSprite = tilingSprite;
            this.container.addChildAt(tilingSprite, 0);
        }
        // Add renderSprite after background to ensure proper z-index
        this.container.addChild(this.renderSprite);
    }
    setupInteraction() {
        this.isDrawing = false;
        this.lastPoint = null;
        this.container.interactive = true;
        this.container.cursor = this.cursor ? `url(${this.cursor}), auto` : 'crosshair';
        this.container.on('pointerdown', this.onPointerDown.bind(this));
        this.container.on('pointerup', this.onPointerUp.bind(this));
        this.container.on('pointerupoutside', this.onPointerUp.bind(this));
        this.container.on('pointermove', this.onPointerMove.bind(this));
        this._ticker = new PIXI.Ticker();
        this._ticker.add(() => this._renderPoints());
        this._ticker.start();
    }
    onPointerDown(event) {
        if (this.isOverHUD) return;
        this.isDrawing = true;
        let pos = event.data.getLocalPosition(this.renderSprite);
        this._drawPoint(pos.x, pos.y);
        this.lastPoint = pos;
        this.points = [pos];
        this.currentStroke = {
            color: this.currentColor,
            size: this.currentBrushSize,
            points: [pos]
        };
        if (this.brushPhysics) {
            this.brushPhysics.startFlow(pos.x, pos.y, this.currentColor);
        }
    }
    onPointerUp(event) {
        this.isDrawing = false;
        this.lastPoint = null;
        if (this.brushPhysics) {
            this.brushPhysics.stopFlow();
        }
        if (this.currentStroke && this.currentStroke.points.length > 1) {
            this.strokes.push(this.currentStroke);
        }
        this.currentStroke = null;
        if (this.hasDrawnAnything) {
            this.container.emit('strokeend', event);
        }
    }
    onPointerMove(event) {
        if (!this.isDrawing || this.isOverHUD) return;
        let pos = event.data.getLocalPosition(this.renderSprite);
        if (this.shouldAddPoint(this.lastPoint, pos)) {
            this._drawPointLine(this.lastPoint, pos);
            this.hasDrawnAnything = true;
            this.points.push(pos);
            if (this.points.length > this.maxPoints) {
                this.points.shift();
            }
            if (this.currentStroke) {
                this.currentStroke.points.push(pos);
            }
            if (this.brushPhysics) {
                this.brushPhysics.updateFlow(pos.x, pos.y, Date.now());
            }
            this.lastPoint = pos;
        }
    }
    clear() {
        this.spritePool.reset();
        this.drawBuffer.removeChildren();
        this.brushTexture = null;
        this._updateBrush();
        this._clearRenderTexture();
        this.hasDrawnAnything = false;
        this.points = [];
        this.strokes = [];
        this.currentStroke = null;
        if (this.brushPhysics) {
            this.brushPhysics.clearParticles();
        }
    }
    hasDrawn() {
        return this.hasDrawnAnything;
    }
    shouldAddPoint(lastPoint, currentPoint) {
        if (!lastPoint) return true;
        if (this.hud && this.hud.tools.smoothingSlider) {
            return this.hud.tools.smoothingSlider.shouldAddPoint(lastPoint, currentPoint);
        }
        const distance = Math.sqrt(
            Math.pow(currentPoint.x - lastPoint.x, 2) + 
            Math.pow(currentPoint.y - lastPoint.y, 2)
        );
        return distance >= 2;
    }
    update(deltaTime) {
        if (this.hud) {
            this.hud.update(deltaTime);
        }
        if (this.brushPhysics) {
            this.brushPhysics.update(deltaTime);
            if (this.physicsLayer) {
                this.physicsLayer.removeChildren();
                const particleGraphics = this.brushPhysics.getParticleGraphics();
                particleGraphics.forEach(graphics => {
                    this.physicsLayer.addChild(graphics);
                });
            }
        }
    }
    setColor(color) {
        this.currentColor = color;
        this._updateBrush();
    }
    setBrushSize(size) {
        this.currentBrushSize = size;
        this._updateBrush();
    }
    setSmoothing(smoothing) {
        this.smoothing = smoothing;
        this._updateBrush();
    }
    resize(width, height) {
        this.width = width;
        this.height = height;
        if (this.hud) {
            this.hud.resize(width, height);
        }
        this.renderTexture.resize(width, height);
        this.renderSprite.width = width;
        this.renderSprite.height = height;
    }
    undo() {
        if (this.strokes.length > 0) {
            this.strokes.pop();
            this.redrawStrokes();
        }
    }
    redrawStrokes() {
        // Clear the render texture and buffers without resetting strokes array
        this.spritePool.reset();
        this.drawBuffer.removeChildren();
        this._clearRenderTexture();
        
        // Redraw all remaining strokes
        for (const stroke of this.strokes) {
            if (stroke.points.length < 2) continue;
            for (let i = 1; i < stroke.points.length; i++) {
                this._drawPointLine(stroke.points[i-1], stroke.points[i]);
            }
        }
        
        // Update hasDrawnAnything flag based on remaining strokes
        this.hasDrawnAnything = this.strokes.length > 0;
    }
    _drawPoint(x, y) {
        const sprite = this.spritePool.get();
        sprite.x = x;
        sprite.y = y;
        sprite.texture = this.brushTexture;
        sprite.blendMode = PIXI.BLEND_MODES.NORMAL;
        this.drawBuffer.addChild(sprite);
    }
    _drawPointLine(oldPos, newPos) {
        const delta = { x: oldPos.x - newPos.x, y: oldPos.y - newPos.y };
        const deltaLength = Math.sqrt(delta.x * delta.x + delta.y * delta.y);
        this._drawPoint(newPos.x, newPos.y);
        if (deltaLength >= this.currentBrushSize / 8) {
            const additionalPoints = Math.ceil(deltaLength / (this.currentBrushSize / 8));
            for (let i = 1; i < additionalPoints; i++) {
                const pos = {
                    x: newPos.x + delta.x * (i / additionalPoints),
                    y: newPos.y + delta.y * (i / additionalPoints)
                };
                this._drawPoint(pos.x, pos.y);
            }
        }
    }
    _renderPoints() {
        if (this._renderer && this.drawBuffer.children.length > 0) {
            this._renderer.render(this.drawBuffer, this.renderTexture, false);
        }
        this.drawBuffer.removeChildren();
        this.spritePool.reset();
    }
    _updateBrush() {
        if (!this.brushGenerator) {
            console.warn('BrushGenerator not available');
            return;
        }
        this.brushTexture = this.brushGenerator.get(
            this.currentBrushSize,
            this.currentColor,
            this.smoothing,
            false // eraser not implemented yet
        );
        if (!this.brushTexture) {
            console.warn('Failed to generate brush texture');
        }
    }
    _clearRenderTexture() {
        if (this.renderTexture && this._renderer) {
            this._renderer.render(new PIXI.Graphics(), this.renderTexture, true);
        }
    }
}

window.DrawingCanvas = DrawingCanvas; 