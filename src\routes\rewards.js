// where the express routes are stored
const { Router } = require('express');
const allQuery = require("../controls/controller");

const router = Router();

router.get("/", allQuery.getRewards);
router.get("/owner/:owner_id", allQuery.getRewardsByOwnerId);
router.get("/eventid/:event_id", allQuery.getRewardsByEventId);
router.post("/", allQuery.addReward);
router.delete("/:event_id", allQuery.removeReward);

// player controlled
router.put("/:event_id", allQuery.updateReward);

// sys controlled
router.put("/sys/:event_id", allQuery.updateRewardBySystem);

module.exports = router;