// mapData.js - Data management and API calls for the map editor

// Global state
let mapData = [];
let pendingChanges = [];
let editedTiles = new Set();
let allZones = [];

// Navigation state
let nav = {
  view: 'worlds',
  world: 0,
  zone: 0
};

// Getter functions
const getMapData = function() { return mapData; };
const getPendingChanges = function() { return pendingChanges; };
const getEditedTiles = function() { return editedTiles; };
const getAllZones = function() { return allZones; };
const getNavigation = function() { return nav; };

// Setter functions
const setMapData = function(data) { mapData = data; };
const setNavigation = function(navigation) { nav = navigation; };

const addPendingChange = function(change) {
  pendingChanges.push(change);
};

const removePendingChange = function(index) {
  if (index >= 0 && index < pendingChanges.length) {
    pendingChanges.splice(index, 1);
  }
};

const clearPendingChanges = function() {
  pendingChanges = [];
};

const addEditedTile = function(tileIndex) {
  editedTiles.add(tileIndex);
};

const removeEditedTile = function(tileIndex) {
  editedTiles.delete(tileIndex);
};

const clearEditedTiles = function() {
  editedTiles.clear();
};

const loadMapDataAndInit = async function() {
  if (typeof axios === 'undefined') {
    console.error('Axios library not loaded');
    if (window.showLoading) {
      window.showLoading('Error: Axios library not loaded');
    }
    return;
  }
  
  if (window.showLoading) {
    window.showLoading();
  }
  
  try {
    const domainUrl = window.getDomainUrl ? window.getDomainUrl() : 'https://express-crushie.herokuapp.com';
    const response = await axios.get(domainUrl + '/players/zones');
    allZones = response.data;
    console.log('Loaded ' + allZones.length + ' zones');
    
    // Initialize Three.js first
    if (window.initThreeJsMapEditor) {
      window.initThreeJsMapEditor();
    }
    
    // Small delay to ensure Three.js is fully initialized
    setTimeout(function() {
      // Start with worlds view
      if (window.renderWorlds) {
        window.renderWorlds();
      }
    }, 100);
  } catch (err) {
    if (window.showLoading) {
      window.showLoading('Error loading map data.');
    }
    console.error(err);
  }
};

const updateTileData = function(tileIndex, newData) {
  if (tileIndex >= 0 && tileIndex < mapData.length) {
    const oldData = {};
    for (let key in mapData[tileIndex]) {
      oldData[key] = mapData[tileIndex][key];
    }
    
    // Update the tile data
    for (let key in newData) {
      mapData[tileIndex][key] = newData[key];
    }
    
    // Add to pending changes
    pendingChanges.push({
      action: 'update',
      tileIndex: tileIndex,
      oldData: oldData,
      newData: mapData[tileIndex]
    });
    
    // Mark tile as edited
    editedTiles.add(tileIndex);
    
    return { oldData: oldData, newData: mapData[tileIndex] };
  }
  return null;
};

const revertTileData = function(tileIndex, oldData) {
  if (tileIndex >= 0 && tileIndex < mapData.length) {
    mapData[tileIndex] = oldData;
    return true;
  }
  return false;
};

const getWorldZones = function(worldId) {
  return allZones.filter(function(zone) { return zone.mapgrid_4 === worldId; });
};

const getZoneData = function(worldId, zoneId) {
  return allZones.find(function(z) { return z.mapgrid_4 === worldId && z.mapgrid_16 === zoneId; });
};

const loadZoneData = function(worldId, zoneId) {
  const zone = getZoneData(worldId, zoneId);
  if (!zone || !zone.data || !zone.data.locales) {
    return false;
  }
  
  mapData = zone.data.locales.map(function(locale, idx) {
    return {
      x: idx % 16,
      y: Math.floor(idx / 16),
      type: locale.Tile || 'grassplains',
      name: locale.Locale_Name || '',
      variant: locale.Terrain || 'land',
    };
  });
  
  return true;
};

const saveChangesToDatabase = async function() {
  if (pendingChanges.length === 0) {
    throw new Error('No changes to save.');
  }
  
  try {
    // Here you would implement the actual database save logic
    // For now, we'll just simulate a successful save
    console.log('Saving changes to database:', pendingChanges);
    
    // Clear pending changes and edited tiles
    pendingChanges = [];
    clearEditedTiles();
    
    return true;
  } catch (error) {
    console.error('Error saving changes:', error);
    throw error;
  }
};

const saveMapAsJson = function() {
  // Convert current map data back to the exact database format
  const databaseFormat = mapData.map(function(tile, idx) {
    return {
      Tile: tile.type || 'grassplains',
      Locale_Name: tile.name || '',
      Terrain: tile.variant || 'land'
    };
  });
  
  const dataStr = JSON.stringify(databaseFormat, null, 2);
  const blob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'map-backup.json';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Make functions globally accessible
window.getMapData = getMapData;
window.getPendingChanges = getPendingChanges;
window.getEditedTiles = getEditedTiles;
window.getAllZones = getAllZones;
window.getNavigation = getNavigation;
window.setMapData = setMapData;
window.setNavigation = setNavigation;
window.addPendingChange = addPendingChange;
window.removePendingChange = removePendingChange;
window.clearPendingChanges = clearPendingChanges;
window.addEditedTile = addEditedTile;
window.removeEditedTile = removeEditedTile;
window.clearEditedTiles = clearEditedTiles;
window.loadMapDataAndInit = loadMapDataAndInit;
window.updateTileData = updateTileData;
window.revertTileData = revertTileData;
window.getWorldZones = getWorldZones;
window.getZoneData = getZoneData;
window.loadZoneData = loadZoneData;
window.saveChangesToDatabase = saveChangesToDatabase;
window.saveMapAsJson = saveMapAsJson; 

// --- Moved from mapUI.js ---
// must match the formatting of the row for map_zones table
function formatMapZonesRow(change) {
  // This should match the legacy/DB row format for map_zones.locales
  return {
    Tile: change.newData.type || 'grassplains',
    Locale_Name: change.newData.name || '',
    Terrain: change.newData.variant || 'land'
  };
}
window.formatMapZonesRow = formatMapZonesRow; 