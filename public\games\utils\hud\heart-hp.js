// PIXI-based HeartMeter for hearts/powerups
// Usage: new HeartMeter({ ... })
(function() {
    window.HeartMeter = function(options) {
        PIXI.Container.call(this);
        options = options || {};
        this.count = options.count || 3;
        this.value = options.value != null ? options.value : this.count;
        this.fullIcon = options.fullIcon || 'assets/@heart.png';
        this.emptyIcon = options.emptyIcon || 'assets/@heart-empty.png';
        this.iconWidth = options.iconWidth || 32;
        this.iconHeight = options.iconHeight || 32;
        this.spacing = options.spacing != null ? options.spacing : 8;
        this._sprites = [];
        this._init();
    };
    HeartMeter.prototype = Object.create(PIXI.Container.prototype);
    HeartMeter.prototype.constructor = HeartMeter;

    HeartMeter.prototype._init = function() {
        this._draw();
    };

    HeartMeter.prototype._draw = function() {
        // Remove old sprites
        for (var i = 0; i < this._sprites.length; i++) {
            this.removeChild(this._sprites[i]);
        }
        this._sprites = [];
        // Draw hearts/powerups
        for (var i = 0; i < this.count; i++) {
            var icon = i < this.value ? this.fullIcon : this.emptyIcon;
            var sprite = PIXI.Sprite.from(icon);
            sprite.width = this.iconWidth;
            sprite.height = this.iconHeight;
            sprite.x = i * (this.iconWidth + this.spacing);
            this.addChild(sprite);
            this._sprites.push(sprite);
        }
    };

    HeartMeter.prototype.setValue = function(newValue) {
        this.value = Math.max(0, Math.min(this.count, newValue));
        this._draw();
    };
    HeartMeter.prototype.setCount = function(newCount) {
        this.count = newCount;
        if (this.value > this.count) this.value = this.count;
        this._draw();
    };
    HeartMeter.prototype.setIcons = function(fullIcon, emptyIcon) {
        this.fullIcon = fullIcon;
        this.emptyIcon = emptyIcon;
        this._draw();
    };
})(); 