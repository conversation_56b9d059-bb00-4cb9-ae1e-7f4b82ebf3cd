# Dialog System Documentation

A customizable dialog system for creating interactive character conversations in your web application. Perfect for games, tutorials, and interactive experiences.

## Features

- **Typing Effect**: Text types out letter by letter or word by word
- **Multiple Text Segments**: Support for multi-part conversations
- **Character Images**: Display character portraits with names
- **Flexible Positioning**: Center, corners, or custom positions
- **Entry Animations**: Slide in from any direction with bouncy effects
- **Auto-close Timeout**: Automatic closing after specified time
- **Dialog Queuing**: Multiple dialogs wait in line
- **Multiple Dialogs**: Show multiple dialogs simultaneously
- **SNES RPG Styling**: Retro game aesthetic with custom fonts
- **Responsive Design**: Works on desktop and mobile

## Quick Start

### 1. Include the Files

```html
<!-- Load the typing effect utility first -->
<script src="/utils/typingfx.js"></script>

<!-- Load the dialog system -->
<script src="/ux/components/dialog.js"></script>

<!-- The CSS is automatically loaded by the dialog system -->
```

### 2. Basic Usage

```javascript
// Show a simple dialog
dialogSystem.show({
    text: "Hello! This is a test dialog.",
    position: "center",
    entryDirection: "bottom",
    timeout: 5
});
```

### 3. Advanced Usage

```javascript
// Show a character dialog with multiple segments
dialogSystem.show({
    text: [
        "Hello! I'm a Crushie character!",
        "This is my second line of dialogue.",
        "And this is my final message!"
    ],
    characterName: "Fluffy Crushie",
    characterImage: "/images/creatures/fluffy-crushie.png",
    position: "center",
    entryDirection: "bottom",
    timeout: 5,
    typingSpeed: 50,
    typingMode: "letter",
    onComplete: () => {
        console.log("Dialog completed!");
    },
    onClose: () => {
        console.log("Dialog closed!");
    }
});
```

## Configuration Options

### Required Options

- `text` (string|array): The text to display. Can be a single string or array of strings for multiple segments.

### Optional Options

- `characterName` (string): Name of the character speaking
- `characterImage` (string): URL of character's portrait image
- `position` (string): Where to position the dialog
  - `"center"` (default)
  - `"top-left"`
  - `"top-right"`
  - `"bottom-left"`
  - `"bottom-right"`
- `entryDirection` (string): Animation entry direction
  - `"top"`, `"bottom"`, `"left"`, `"right"`
- `timeout` (number): Auto-close timeout in seconds (default: 5)
- `allowMultiple` (boolean): Allow multiple dialogs simultaneously (default: false)
- `typingSpeed` (number): Typing speed in milliseconds (default: 50)
- `typingMode` (string): `"letter"` or `"word"` (default: "letter")
- `onComplete` (function): Callback when all text segments are shown
- `onClose` (function): Callback when dialog is closed

## Examples

### Creature Inventory Integration

```javascript
// Handle creature click in inventory
function handleCreatureClick(creatureId) {
    const creature = getCreatureData(creatureId);
    
    dialogSystem.show({
        text: creature.dialog,
        characterName: creature.name,
        characterImage: creature.image,
        position: "center",
        entryDirection: "bottom",
        timeout: 5,
        typingSpeed: 50
    });
}

// Add click handlers to inventory items
document.querySelectorAll('.creature-item').forEach(item => {
    item.addEventListener('click', () => {
        const creatureId = item.dataset.creatureId;
        handleCreatureClick(creatureId);
    });
});
```

### Tutorial System

```javascript
function showTutorial() {
    dialogSystem.show({
        text: [
            "Welcome to the game! Let me show you around.",
            "Click on creatures to interact with them.",
            "You can also use the menu to access your inventory."
        ],
        characterName: "Tutorial Guide",
        characterImage: "/images/ui/tutorial-guide.png",
        position: "bottom-right",
        entryDirection: "right",
        timeout: 6,
        typingSpeed: 40
    });
}
```

### Multiple Characters Talking

```javascript
// Show multiple characters in conversation
dialogSystem.show({
    text: "Hey everyone! Let's have a party!",
    characterName: "Fluffy Crushie",
    characterImage: "/images/fluffy.png",
    position: "top-left",
    entryDirection: "left",
    timeout: 3,
    allowMultiple: true
});

setTimeout(() => {
    dialogSystem.show({
        text: "Yay! I love parties!",
        characterName: "Spiky Crushie",
        characterImage: "/images/spiky.png",
        position: "top-right",
        entryDirection: "right",
        timeout: 3,
        allowMultiple: true
    });
}, 1000);
```

## API Reference

### DialogSystem Methods

#### `dialogSystem.show(options)`
Shows a new dialog with the specified options.

#### `dialogSystem.closeAll()`
Closes all active dialogs and clears the queue.

#### `dialogSystem.test()`
Shows a test dialog to verify the system is working.

### TypingEffect Methods

The typing effect utility provides these methods:

#### `new TypingEffect(element, options)`
Creates a new typing effect instance.

#### `typingEffect.type(text)`
Starts typing out the specified text.

#### `typingEffect.skip()`
Skips to the end and shows full text immediately.

#### `typingEffect.stop()`
Stops the typing effect.
 

### Custom Font

The system uses a custom pixel font (`crushie-font.ttf`) for the SNES RPG aesthetic. Make sure the font file is available at `/data/fonts/crushie-font.ttf`.

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- ES6+ features required
- CSS Grid and Flexbox support needed

## Testing

Use the test file to verify the dialog system is working:

```html
<!-- Open test-dialog.html in your browser -->
<a href="/test-dialog.html">Test Dialog System</a>
```

## Troubleshooting

### Dialog not appearing
- Check that both JavaScript files are loaded
- Verify the CSS file path is correct
- Check browser console for errors

### Typing effect not working
- Ensure `TypingEffect` class is available
- Check that the text element exists in the DOM

### Styling issues
- Verify the custom font file exists
- Check that CSS is properly loaded
- Inspect element for conflicting styles

## License

This dialog system is part of the Crushie project and follows the same license terms. 