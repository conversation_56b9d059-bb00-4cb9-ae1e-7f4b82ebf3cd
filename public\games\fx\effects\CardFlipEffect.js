// Card Flip Effect - 3D-like card flipping animation
class CardFlipEffect {
    constructor(visualEffects) {
        this.visualEffects = visualEffects;
    }

    // Create 3D-like card flip animation
    create(card, onComplete) {
        const duration = 300; // Animation duration in milliseconds
        const startTime = Date.now();
        
        // Store original properties
        const originalScale = { x: card.scale.x, y: card.scale.y };
        const originalRotation = card.rotation;
        const originalX = card.x;
        const originalY = card.y;
        
        // Set pivot point to center of card for proper rotation
        // Adjust position to compensate for pivot change
        card.pivot.set(40, 50); // Half of card width (80) and height (100)
        card.x = originalX + 40; // Adjust X position to compensate for pivot
        card.y = originalY + 50; // Adjust Y position to compensate for pivot
        
        // Animation function
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function for smooth animation
            const easeInOut = this.easeInOutCubic(progress);
            
            if (progress < 0.5) {
                // First half: scale horizontally to simulate 3D flip
                const firstHalfProgress = progress * 2;
                const horizontalScale = 1 - (firstHalfProgress * 0.8); // Scale down to 20% width
                const verticalScale = originalScale.y; // Keep height the same
                
                card.scale.set(horizontalScale, verticalScale);
                
                // Hide front, show back at midpoint
                if (firstHalfProgress >= 0.5) {
                    if (card.front) card.front.visible = false;
                    if (card.back) card.back.visible = true;
                    if (card.fruit) card.fruit.visible = true;
                }
            } else {
                // Second half: scale back to full width
                const secondHalfProgress = (progress - 0.5) * 2;
                const horizontalScale = 0.2 + (secondHalfProgress * 0.8); // Scale back to 100% width
                const verticalScale = originalScale.y; // Keep height the same
                
                card.scale.set(horizontalScale, verticalScale);
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Reset to original state
                card.scale.set(originalScale.x, originalScale.y);
                card.pivot.set(0, 0); // Reset pivot
                card.x = originalX; // Reset to original position
                card.y = originalY; // Reset to original position
                
                // Ensure final state is correct
                if (card.front) card.front.visible = false;
                if (card.back) card.back.visible = true;
                if (card.fruit) card.fruit.visible = true;
                
                // Add a small bounce effect
                this.addBounceEffect(card);
                
                if (onComplete) onComplete();
            }
        };
        
        animate();
    }

    // Easing function for smooth animation
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    // Add a subtle bounce effect after flip
    addBounceEffect(card) {
        const bounceDuration = 200;
        const startTime = Date.now();
        const originalScale = { x: card.scale.x, y: card.scale.y };
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / bounceDuration, 1);
            
            // Bounce easing
            const bounce = Math.sin(progress * Math.PI * 4) * (1 - progress) * 0.1;
            const scale = 1 + bounce;
            
            card.scale.set(scale, originalScale.y);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                card.scale.set(originalScale.x, originalScale.y);
            }
        };
        
        animate();
    }

    // Create flip back animation (for unmatched cards)
    createFlipBack(card, onComplete) {
        const duration = 250; // Slightly faster for flip back
        const startTime = Date.now();
        
        // Store original properties
        const originalScale = { x: card.scale.x, y: card.scale.y };
        const originalRotation = card.rotation;
        const originalX = card.x;
        const originalY = card.y;
        
        // Set pivot point to center of card for proper rotation
        // Adjust position to compensate for pivot change
        card.pivot.set(40, 50); // Half of card width (80) and height (100)
        card.x = originalX + 40; // Adjust X position to compensate for pivot
        card.y = originalY + 50; // Adjust Y position to compensate for pivot
        
        // Animation function
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function for smooth animation
            const easeInOut = this.easeInOutCubic(progress);
            
            if (progress < 0.5) {
                // First half: scale horizontally to simulate 3D flip
                const firstHalfProgress = progress * 2;
                const horizontalScale = 1 - (firstHalfProgress * 0.8); // Scale down to 20% width
                const verticalScale = originalScale.y; // Keep height the same
                
                card.scale.set(horizontalScale, verticalScale);
                
                // Hide back, show front at midpoint
                if (firstHalfProgress >= 0.5) {
                    if (card.front) card.front.visible = true;
                    if (card.back) card.back.visible = false;
                    if (card.fruit) card.fruit.visible = false;
                }
            } else {
                // Second half: scale back to full width
                const secondHalfProgress = (progress - 0.5) * 2;
                const horizontalScale = 0.2 + (secondHalfProgress * 0.8); // Scale back to 100% width
                const verticalScale = originalScale.y; // Keep height the same
                
                card.scale.set(horizontalScale, verticalScale);
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Reset to original state
                card.scale.set(originalScale.x, originalScale.y);
                card.pivot.set(0, 0); // Reset pivot
                card.x = originalX; // Reset to original position
                card.y = originalY; // Reset to original position
                
                // Ensure final state is correct
                if (card.front) card.front.visible = true;
                if (card.back) card.back.visible = false;
                if (card.fruit) card.fruit.visible = false;
                
                if (onComplete) onComplete();
            }
        };
        
        animate();
    }
} 