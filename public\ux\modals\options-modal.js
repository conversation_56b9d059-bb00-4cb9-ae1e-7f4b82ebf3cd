const DEFAULT_PLAYER_OPTIONS = {
  mapTransition: 'zoom', // 'simple' or 'zoom'
  musicEnabled: true,
  atomicNodeUrl: 'https://aa.dapplica.io' // default node
};
 
function initializePlayerOptions(waxId) {
  if (!waxId) {
    console.warn('No WAX ID provided for player options initialization');
    return;
  }

  const storageKey = `playerOptions_${waxId}`;
  const storedOptions = localStorage.getItem(storageKey);
  
  if (storedOptions) {
    try {
      playerOptions = JSON.parse(storedOptions); 
      playerOptions = { ...DEFAULT_PLAYER_OPTIONS, ...playerOptions };
    } catch (error) {
      console.error('Error parsing stored player options:', error);
      playerOptions = { ...DEFAULT_PLAYER_OPTIONS };
    }
  } else {
    playerOptions = { ...DEFAULT_PLAYER_OPTIONS };
  }
  
  console.log('Player options initialized:', playerOptions);
}
 
function savePlayerOptions(waxId) {
  if (!waxId) {
    console.warn('No WAX ID provided for saving player options');
    return;
  }

  const storageKey = `playerOptions_${waxId}`;
  try {
    localStorage.setItem(storageKey, JSON.stringify(playerOptions));
    console.log('Player options saved:', playerOptions);
  } catch (error) {
    console.error('Error saving player options:', error);
  }
  if (typeof setWaxEndpointFromPlayerOptions === 'function') {
    setWaxEndpointFromPlayerOptions();
  }
} 

function updatePlayerOption(option, value, waxId) {
  if (!waxId) {
    console.warn('No WAX ID provided for updating player option');
    return;
  }

  playerOptions[option] = value;
  savePlayerOptions(waxId); 
  applyPlayerOption(option, value);
}
 
function applyPlayerOption(option, value) {
  switch (option) {
    case 'mapTransition': 
      console.log('Map transition setting updated to:', value);
      break;
    case 'musicEnabled':
      if (typeof AudioManager !== 'undefined') {
        AudioManager.toggleBGM(value);
      }
      break;
    case 'atomicNodeUrl':
      if (typeof setWaxEndpointFromPlayerOptions === 'function') {
        setWaxEndpointFromPlayerOptions();
      }
      break;
    default:
      console.log('Option applied:', option, value);
  }
}
 
function displayOptionsModal() {
  const modalContent = {
    body: createOptionsModalBody(),
    footer: createOptionsModalFooter()
  };

  const modal = createModal("Game Options", modalContent, "main-content"); 
  $(modal).modal('show'); 
  setupOptionsEventListeners(modal);
} 
function createOptionsModalBody() {
  // Build atomic node dropdown options
  let atomicNodeOptions = '';
  if (typeof atomicNodes !== 'undefined') {
    atomicNodes.forEach(node => {
      atomicNodeOptions += `<option value="${node.url}" ${playerOptions.atomicNodeUrl === node.url ? 'selected' : ''}>${node.name} (${node.url.replace('https://','')})</option>`;
    });
  }
  return `
    <div>
      <label for="map-transition-select">Map Transition:</label>
      <select id="map-transition-select" class="form-select">
        <option value="zoom" ${playerOptions.mapTransition === 'zoom' ? 'selected' : ''}>Zoom Effect</option>
        <option value="simple" ${playerOptions.mapTransition === 'simple' ? 'selected' : ''}>Simple Fade</option>
      </select>
      <br><br>
      <label>
        <input type="checkbox" id="music-enabled" ${playerOptions.musicEnabled ? 'checked' : ''}>
        Enable Background Music
      </label>
      <br><br>
      <label for="atomic-node-select">Atomic Assets API Node:</label>
      <select id="atomic-node-select" class="form-select">
        ${atomicNodeOptions}
      </select>
    </div>
  `;
}
 
function createOptionsModalFooter() {
  return `
    <button class="btn-secondary" id="reset-options-btn">Reset</button>
    <button class="btn-secondary" id="save-options-btn">Save</button>
  `;
}
 
function setupOptionsEventListeners(modal) {
  const waxId = userAccount || window.waxId; 
  const mapTransitionSelect = modal.querySelector('#map-transition-select');
  if (mapTransitionSelect) {
    mapTransitionSelect.addEventListener('change', function() {
      updatePlayerOption('mapTransition', this.value, waxId);
    });
  }
 
  const musicEnabledCheckbox = modal.querySelector('#music-enabled');
  if (musicEnabledCheckbox) {
    musicEnabledCheckbox.addEventListener('change', function() {
      updatePlayerOption('musicEnabled', this.checked, waxId);
    });
  }
 
  const atomicNodeSelect = modal.querySelector('#atomic-node-select');
  if (atomicNodeSelect) {
    atomicNodeSelect.addEventListener('change', function() {
      updatePlayerOption('atomicNodeUrl', this.value, waxId);
    });
  }
 
  const resetButton = modal.querySelector('#reset-options-btn');
  if (resetButton) {
    resetButton.addEventListener('click', function() {
      if (confirm('Are you sure you want to reset all options to their default values?')) {
        playerOptions = { ...DEFAULT_PLAYER_OPTIONS };
        savePlayerOptions(waxId); 
        updateOptionsModalUI(modal); 
        Object.keys(playerOptions).forEach(option => {
          applyPlayerOption(option, playerOptions[option]);
        });
        
        alert('Options have been reset to defaults!');
      }
    });
  } 
  const saveButton = modal.querySelector('#save-options-btn');
  if (saveButton) {
    saveButton.addEventListener('click', function() {
      savePlayerOptions(waxId); 
      $(modal).modal('hide');
    });
  }
}
 
function updateOptionsModalUI(modal) { 
  const mapTransitionSelect = modal.querySelector('#map-transition-select');
  if (mapTransitionSelect) {
    mapTransitionSelect.value = playerOptions.mapTransition;
  } 
  const musicEnabledCheckbox = modal.querySelector('#music-enabled');
  if (musicEnabledCheckbox) {
    musicEnabledCheckbox.checked = playerOptions.musicEnabled;
  } 
  const atomicNodeSelect = modal.querySelector('#atomic-node-select');
  if (atomicNodeSelect) {
    atomicNodeSelect.value = playerOptions.atomicNodeUrl;
  }
}
 
function getMapTransitionSetting() {
  return playerOptions.mapTransition || 'zoom';
}
 
function useSimpleTransitions() {
  return getMapTransitionSetting() === 'simple';
}
 
window.useSimpleTransitions = useSimpleTransitions; 
document.addEventListener('DOMContentLoaded', function() { 
  console.log('Options modal system initialized');
});
