// Main Three.js map navigation orchestrator
// This file coordinates all the modular components

/**
 * Initialize the Three.js map navigation system
 */
function initThreeJsMap() {
  const container = document.getElementById('threejs-map-container');
  if (!container) {
    console.error('Three.js map container not found');
    return;
  }
  
  container.innerHTML = '';

  // Initialize the renderer
  initializeRenderer(container);
  
  // Set up click handling
  setupClickHandler();
  
  // Set up tooltip handling
  setupTooltipHandlers();
  
  // Start the animation loop
  animate();
  
  // Initialize navigation state
  if (typeof nav !== 'undefined') {
    // Use existing nav state if available
  } else {
    // Initialize default state
    nav.view = 'worlds';
    nav.world = 0;
    nav.zone = 0;
  }
  
  // Initial render based on current view
  switch (nav.view) {
    case 'zones':
      renderZones();
      updateThreeJsNavButtons('zone');
      break;
    case 'locales':
      renderLocales();
      updateThreeJsNavButtons('locale');
      break;
    default:
      renderWorlds();
      updateThreeJsNavButtons('world');
      break;
  }
}

// Export functions for external use
window.initThreeJsMap = initThreeJsMap;
window.renderWorlds = renderWorlds;
window.renderZones = renderZones;
window.renderLocales = renderLocales; 