// Data service for Three.js map navigation

/**
 * Get zone counts for a specific world
 * @param {number} worldId - The world ID
 * @returns {Object} Object with unlocked and total zone counts
 */
function getWorldZoneCounts(worldId) {
  if (!window.allZones || !Array.isArray(window.allZones)) {
    return { unlocked: 0, total: 0 };
  }
  
  let unlocked = 0;
  let total = 0;
  
  for (const zone of window.allZones) {
    if (zone.mapgrid_4 === worldId) {
      total++;
      if (zone.status !== "locked") {
        unlocked++;
      }
    }
  }
  
  return { unlocked, total };
}

/**
 * Get adventure count for a specific world
 * @param {number} worldId - The world ID
 * @returns {number} Number of active adventures in the world
 */
function getWorldAdventureCount(worldId) {
  if (!window.allAdventures || !Array.isArray(window.allAdventures)) {
    return 0;
  }
  
  return window.allAdventures.filter(adventure => 
    adventure.status === 'In Progress' && adventure.mapgrid_4 === worldId
  ).length;
}

/**
 * Get team counts for a specific zone
 * @param {number} worldId - The world ID
 * @param {number} zoneId - The zone ID
 * @returns {Object} Object with player and other team counts
 */
function getZoneTeamCounts(worldId, zoneId) {
  if (!window.teamCounts || !window.teamCounts.playerTeams || !window.teamCounts.otherTeams) {
    return { player: 0, other: 0 };
  }
  
  const player = window.teamCounts.playerTeams[worldId]?.[zoneId] || 0;
  const other = window.teamCounts.otherTeams[worldId]?.[zoneId] || 0;
  
  return { player, other };
}

/**
 * Get active adventures for a specific zone
 * @param {number} worldId - The world ID
 * @param {number} zoneId - The zone ID
 * @returns {Array} Array of active adventures
 */
function getActiveAdventures(worldId, zoneId) {
  if (!window.allAdventures || !Array.isArray(window.allAdventures)) {
    return [];
  }
  
  return window.allAdventures.filter(adventure => 
    adventure.status === 'In Progress' && 
    adventure.mapgrid_4 === worldId && 
    adventure.mapgrid_16 === zoneId
  );
}

/**
 * Get locale data for a specific zone
 * @param {number} worldId - The world ID
 * @param {number} zoneId - The zone ID
 * @returns {Array} Array of locale data
 */
function getLocaleData(worldId, zoneId) {
  if (!window.allZones || !Array.isArray(window.allZones)) {
    return [];
  }
  
  for (const zone of window.allZones) {
    if (zone.mapgrid_4 === worldId && zone.mapgrid_16 === zoneId) {
      return zone.data?.locales || [];
    }
  }
  
  return [];
}

/**
 * Get tile image path for a tile type
 * @param {string} tileType - The tile type
 * @returns {string} Image path for the tile
 */
function getTileImage(tileType) {
  return TILE_TYPE_TO_IMAGE[tileType] || 'images/forest_world/grass.png';
}

/**
 * Get zones for a specific world
 * @param {number} worldId - The world ID
 * @returns {Array} Array of zones for the world
 */
function getWorldZones(worldId) {
  if (!window.allZones || !Array.isArray(window.allZones)) {
    return [];
  }
  return window.allZones
    .filter(zone => zone.mapgrid_4 === worldId)
    .sort((a, b) => a.mapgrid_16 - b.mapgrid_16);
} 