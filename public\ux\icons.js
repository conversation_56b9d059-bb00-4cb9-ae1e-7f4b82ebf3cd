async function displayTeamIcon(owner, id, adventure) {
var css_color='#0B5ED7';
 if(owner==='player'){
 var css_class='player_location';
 }
 if(owner==='npc'){
 var css_class='npc_location';
 }
 const teamIDCornerSpan = $('<div>').addClass('mapsquare-team-id');
 teamIDCornerSpan.css('background-color', css_color);
 teamIDCornerSpan.text(adventure.team_id ? adventure.team_id : "Starting...");
 const squareDiv = $('#' + id);
 squareDiv.addClass(css_class);
 if (squareDiv.find('.arrow-pointer-icon').length === 0) {
   const adv_icon = document.createElement('div');
   adv_icon.setAttribute('class', 'arrow-pointer-icon');
   squareDiv.append(teamIDCornerSpan);
   squareDiv.prepend(adv_icon);
 }
}

async function displayTeamReadyIcon(navigation, id, text) {
  // Only proceed if the current view is 'locales'
  if (navigation.view !== 'locales') return;

  const teamIDCornerSpan = $('<div>').addClass('mapsquare-team-id');
  teamIDCornerSpan.css('background-color', '#0B5ED7');
  teamIDCornerSpan.text(text);

  const squareDiv = $('#' + id);
  squareDiv.addClass('player_location');

  // Ensure the squareDiv is a mapsquare and not a worldsquare or zonesquare
  if (!squareDiv.hasClass('mapsquare')) return;

  if (!squareDiv.find('.teams-ready-icon').length) {
    const adv_icon = document.createElement('div');
    adv_icon.setAttribute('class', 'teams-ready-icon');
    squareDiv.append(teamIDCornerSpan);
    squareDiv.prepend(adv_icon);
  }
}


async function displayTreasureIcon(id, adventure) {
  const teamIDCornerSpan = $('<div>').addClass('mapsquare-team-id');
  teamIDCornerSpan.css('background-color', 'gold');
  teamIDCornerSpan.css('color', 'black');
  teamIDCornerSpan.text(adventure.team_id ? adventure.team_id : "Adventure Complete!");
  const squareDiv = $('#' + id);
  squareDiv.addClass('treasure_highlight');
  if (squareDiv.find('.treasure-icon').length === 0) {
    const icon = document.createElement('div');
    icon.setAttribute('class', 'treasure-icon');
    icon.setAttribute('title', 'Reward Ready!');
    squareDiv.append(teamIDCornerSpan);
    squareDiv.prepend(icon);
  }
}
