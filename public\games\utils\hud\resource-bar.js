// PIXI-based ResourceBar for HP/MP/resource bars
// Usage: new ResourceBar({ ... })
(function() {
    window.ResourceBar = function(options) {
        PIXI.Container.call(this);
        options = options || {};
        this.width = options.width || 200;
        this.height = options.height || 24;
        this.max = options.max || 100;
        this.value = options.value != null ? options.value : this.max;
        this.color = options.color != null ? options.color : 0x39ff14; // neon green default
        this.underColor = options.underColor != null ? options.underColor : 0xff2222; // red default
        this.overlay = options.overlay || null;
        this._init();
    };
    ResourceBar.prototype = Object.create(PIXI.Container.prototype);
    ResourceBar.prototype.constructor = ResourceBar;

    ResourceBar.prototype._init = function() {
        // Under bar (lost HP/MP)
        this.underBar = new PIXI.Graphics();
        this.underBar.visible = true;
        this.underBar.alpha = 1;
        this.addChild(this.underBar);
        
        // Main bar (current HP/MP)
        this.mainBar = new PIXI.Graphics();
        this.mainBar.visible = true;
        this.mainBar.alpha = 1;
        this.addChild(this.mainBar);
        
        // Overlay (optional)
        if (this.overlay) {
            this.overlaySprite = PIXI.Sprite.from(this.overlay);
            this.overlaySprite.width = this.width;
            this.overlaySprite.height = this.height;
            this.overlaySprite.visible = true;
            this.overlaySprite.alpha = 1;
            this.addChild(this.overlaySprite);
        }
        
        // Ensure the container itself is visible
        this.visible = true;
        this.alpha = 1;
        
        this._draw();
    };

    ResourceBar.prototype._draw = function() {
        console.log('ResourceBar._draw called - value:', this.value, 'max:', this.max, 'percent:', this.value / this.max);
        
        // Under bar
        this.underBar.clear();
        this.underBar.beginFill(this.underColor);
        this.underBar.drawRect(0, 0, this.width, this.height);
        this.underBar.endFill();
        this.underBar.visible = true;
        this.underBar.alpha = 1;
        
        // Main bar
        this.mainBar.clear();
        this.mainBar.beginFill(this.color);
        var percent = Math.max(0, Math.min(1, this.value / this.max));
        var fillWidth = this.width * percent;
        this.mainBar.drawRect(0, 0, fillWidth, this.height);
        this.mainBar.endFill();
        this.mainBar.visible = true;
        this.mainBar.alpha = 1;
        
        console.log('ResourceBar drawn - underBar visible:', this.underBar.visible, 'mainBar visible:', this.mainBar.visible);
        console.log('ResourceBar dimensions - width:', this.width, 'height:', this.height);
        console.log('Fill width:', fillWidth, 'percent:', percent);
        console.log('Container visible:', this.visible, 'alpha:', this.alpha);
        console.log('Container children count:', this.children.length);
    };

    ResourceBar.prototype.setValue = function(newValue) {
        this.value = Math.max(0, Math.min(this.max, newValue));
        this._draw();
    };
    ResourceBar.prototype.setMax = function(newMax) {
        this.max = newMax;
        if (this.value > this.max) this.value = this.max;
        this._draw();
    };
    ResourceBar.prototype.setColors = function(color, underColor) {
        this.color = color;
        this.underColor = underColor;
        this._draw();
    };
    ResourceBar.prototype.setOverlay = function(overlay) {
        if (this.overlaySprite) this.removeChild(this.overlaySprite);
        this.overlay = overlay;
        if (overlay) {
            this.overlaySprite = PIXI.Sprite.from(overlay);
            this.overlaySprite.width = this.width;
            this.overlaySprite.height = this.height;
            this.overlaySprite.visible = true;
            this.overlaySprite.alpha = 1;
            this.addChild(this.overlaySprite);
        }
    };
    
    ResourceBar.prototype.ensureVisible = function() {
        this.visible = true;
        this.alpha = 1;
        if (this.underBar) {
            this.underBar.visible = true;
            this.underBar.alpha = 1;
        }
        if (this.mainBar) {
            this.mainBar.visible = true;
            this.mainBar.alpha = 1;
        }
        if (this.overlaySprite) {
            this.overlaySprite.visible = true;
            this.overlaySprite.alpha = 1;
        }
        console.log('ResourceBar ensureVisible called - container visible:', this.visible);
    };
})(); 