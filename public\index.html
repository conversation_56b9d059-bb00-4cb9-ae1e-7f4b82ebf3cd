<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
	<title>Cute Crushies - Blockchain Game on the WAX Network</title><link href="images/ui/logos/cc-logo-25.png" rel="icon" type="image/png">
	<link href="./css/index.css" rel="stylesheet">
	<link href="./css/shop.css" rel="stylesheet">
	<link href="./css/dialog.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/common-pixel" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/ttvtechprecomput" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/commodore-64-pixelized" rel="stylesheet">
  <style>
    @font-face {
        font-family: 'CrushieFont';
        src: url('./data/fonts/crushie-font.ttf') format('truetype');
        font-weight: normal;
        font-style: normal;
    }
    </style>
  <script src="utils/waxjs.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/uikit@3.9.3/dist/css/uikit.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pixi.js/5.3.3/pixi.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/matter-js/0.19.0/matter.min.js"></script>
  <script src="games/utils/drawing/BrushGeneratorGlobal.js"></script>
  <script src="games/utils/drawing/SpritePoolGlobal.js"></script>
  <script src="games/utils/hud/hud-config.js"></script>
  <script src="games/utils/hud/resource-bar.js"></script>
  <script src="games/utils/hud/heart-hp.js"></script>
  <script src="games/utils/hud/collect-list.js"></script>
</head>
<body>

    <!-- Start Screen -->
    <div class="cover">

    <!-- Currently Disabled: Blurred Background for Start Screen -->
    <div class="bg-blur"></div>

    <div class="login-box">
      <!-- Floating Cute Crushies Logo on Start Screen -->
      <img class="nes-logo floating" src="images/ui/logos/cc-logo-25.png" width="369px" alt="Cute Crushies" title="Play Cute Crushies! A Blockchain Game on The WAX Network">

      <!-- Description of Game -->
      <div class="start-screen-description">
    <div id="message-container">
      <div class="message-controller">
          <button class="arrow-btn left-arrow" onclick="prevMessage()"></button>
          <p id="rotating-message">Enter the mysterious world of the Cute Crushies!</p>
          <button class="arrow-btn right-arrow" onclick="nextMessage()"></button>
      </div>
    </div>
  </div>

      <!-- Legacy Login Button -->    
      <!-- <a href="#" class="btn-block nes-btn" style="margin-bottom: 1.2em;" id="loginBtn">
          <img class="rotate-center" src="../images/ui/castle_key.png" width="32" height="32" style="margin-bottom:5px"> Login to Start
      </a> -->
      
      <a href="#" class="btn-block story-btn" style="margin-bottom: 1.2em;" id="storyBtn" onclick="displayWorldDescriptionModal(storyline, 0)"></a>
      <a href="#" class="btn-block login-btn" style="margin-bottom: 1.2em;" id="loginBtn"></a>

    </div>
    <!-- Copyright -->
    <div id="copyright" class="copyright">©2026 Summershiloh</div>

  </div>

  <nav class="navbar navbar-expand-lg navbar-light" style="background: #ffe91b; border-bottom: 1px solid #ccc;">
     <a class="navbar-brand pl-4" href="#">
         <img src="images/ui/logos/cc-logo-25.png" alt="CuteCrushies Logo" style="margin-left:1em;" width="64px" id="logoImage" onclick="changeLogoSize()" title="Cute Crushies">
     </a>

         <ul class="navbar-nav">
             <li class="nav-item yellow-border" id="playerCountersContainer">
                <ul>
                  <li><img src="images/ui/reward_icon.png" title="Rewards"><span>0 Rewards</span></li>
                  <li><img src="images/ui/ready_icon.gif" title="Ready"><span>0 Teams Ready!</span></li>
                  <li><img src="images/ui/bed_icon.png" title="Napping"><span>0 Napping</span></li>
                  <li><img id="crushinator-icon" src="images/ui/npc/crushinator-standby.png"><span></span></li>
                  <li><img src="images/ui/info_icon.png" title="Admin Dashboard"><span><a href="/admin/caps" target="_blank">Admin Dashboard</a></span></li>
                  <li><img src="images/ui/info_icon.png" title="Game Options"><span><a href="javascript:void(0);" onclick="displayOptionsModal()">Options</a></span></li>

                  <!-- <li><img src="images/ui/info_icon.png" title="How To Play"><span><a href="javascript:void(0);" onclick="displayHowToPlayModal()">How To Play</a></span></li> -->
                  <!-- <li><img src="images/ui/menu_icon.png" title="Menu"><span><a href="javascript:void(0);" onclick="displayMainGameMenuModal()">Menu</a></span></li> -->
                </ul>
             </li>
         </ul>
 </nav>


	<div class="container-fluid">
	  <div class="row">
	    <div class="col-md-12 justify-content-between">
      <div class="row">
        <div  id="column-1" class="col-md-3 mt-3">
          <div class="card">
            <!-- card content here -->
						<ul class="list-group">
              <li id="player-name" class="list-group-item d-flex justify-content-between align-items-center fw-bold">
                <span id="loginButton" onclick="login()" href="#" class="badge-primary badge-pill"><img title="Welcome to Cute Crushies!" src="../images/ui/player_icon2.png" width="12px"></span>
              </li>
              <li id="playerLevelBar" class="list-group-item d-flex justify-content-between align-items-center">
                <span class="badge-primary badge-pill" id="playerLevel"></span>
              </li>
              <li class="list-group-item p-0" style="background: none; border: none;">
                <div id="balance-bar" class="balance-bar d-flex flex-row align-items-center justify-content-between" style="gap: 6px; padding: 4px 0;">
                  <div class="balance-box" id="waxBalanceBox" title="WAX Balance">
                    <img src="images/ui/wax.png" alt="WAX" width="12" style="vertical-align:middle;"> <span id="waxBalance"></span>
                  </div>
                  <div class="balance-box" id="dustBalanceBox" title="DUST Balance">
                    <img src="images/ui/dust.png" alt="DUST" width="12" style="vertical-align:middle;"> <span id="dustBalance"></span>
                  </div>
                  <div class="balance-box" id="gxpBalanceBox" title="GXP Balance">
                    <img src="images/ui/gxp_icon.png" alt="GXP" width="12" style="vertical-align:middle;"> <span id="gxpBalance"></span>
                  </div>
                  <div class="balance-box" id="nectarBalanceBox" title="NECTAR Balance">
                    <img src="images/ui/nectar_icon.png" alt="NECTAR" width="12" style="vertical-align:middle;"> <span id="nectarBalance"></span>
                  </div>
                  <div class="balance-box" id="creditsBalanceBox" title="CREDITS Balance">
                    <img src="images/ui/credits_icon.png" alt="CREDITS" width="12" style="vertical-align:middle;"> <span id="creditsBalance"></span>
                  </div>
                </div>
              </li>
						</ul>
          </div>
 
     
      <div class="card card mt-3 player-log-bg" style="position: relative;">
          <!-- Tab Buttons -->
          <div class="tab-buttons">
              <button id="journal-button" class="tab-button active-tab" onclick="showJournal()">
                  <img src="../images/ui/world_scroll.png"> Journal
              </button>
              <button id="stats-button" class="tab-button" onclick="showStats()">
                  <img src="../images/ui/boost_icon.png"> Player Stats
              </button>
          </div>

          <!-- Content sections -->
          <div id="player-log-list" class="player-log-list scroll-wood"></div>
          <div id="player-stats-list" class="player-stats-list" style="display: none;"></div>
      </div>


        </div>
        <div id="column-3" class="col-md-4 mt-3">
          <div class="card">
            <!-- card content here -->

            <div id="tab-menu">
              <div class="tab-button" onclick="openTab('inventory', event)"><img src="../images/ui/hud/hud_inventory.png" style="width:12px"> Inventory</div>
              <div class="tab-button" id="teams_tab" onclick="openTab('teams', event)"><img src="../images/ui/hud/hud_teams.png" style="width:12px"> Teams <span id="teams-counter" class="tab-counter"></span></div>
              <div class="tab-button"  id="adventures_tab" onclick="openTab('adventure', event)"><img src="../images/ui/hud/hud_adventure.png" style="width:12px"> Adventures <span id="adventures-counter" class="tab-counter"></span></div>
              <div class="tab-button" onclick="openTab('games', event)"><img src="../images/ui/hud/hud_games.png" style="width:12px"> Games</div>
            </div>

            <div id="inventory" class="tab-container">
                <button class="btn-secondary flex-fill m-1" value="houses" onclick="setSelectedSchema('houses')"><img src="../images/ui/house_icon_lg.png" style="width:12px"> Houses</button>
                <button class="btn-secondary flex-fill m-1" value="creature" onclick="setSelectedSchema('creature')"><img src="../images/ui/creatures_icon.png" style="width:12px"> Creatures</button>
                <button class="btn-secondary flex-fill m-1" value="vehicles" onclick="setSelectedSchema('vehicles')"><img src="../images/ui/vehicles_icon.png" style="width:12px"> Vehicles</button>
                <button class="btn-secondary flex-fill m-1" value="items" onclick="setSelectedSchema('items')"><img src="../images/ui/items_icon.png" style="width:12px"> Items</button>
                <button class="btn-secondary flex-fill m-1" value="coins" onclick="setSelectedSchema('coins')"><img src="../images/ui/coins_icon.png" style="width:12px"> Coins</button>
            </div>

            <div id="teams" class="tab-container">
                <button class="btn-secondary flex-fill m-1" onclick="setSelectedTeamType('Ready')">
                    <span class="icon ready-icon"></span> Ready
                </button>
                <button class="btn-secondary flex-fill m-1" onclick="setSelectedTeamType('Napping')">
                    <span class="icon nap-icon"></span> Napping
                </button>
                <button class="btn-secondary flex-fill m-1" onclick="displayAvailableVehicles(displayInventory, event)">
                    <span class="icon plus-icon"></span> Create Team
                </button>
            </div>

            <div id="adventure" class="tab-container">

                <div id="total_progress_bar"></div>

                <button class="btn-secondary flex-fill m-1" onclick="setSelectedAdventureType('Complete')">
                    <span class="icon reward-icon"></span> Complete
                </button>
                <button class="btn-secondary flex-fill m-1" onclick="setSelectedAdventureType('In Progress')">
                    <span class="icon adventure-icon"></span> In Progress
                </button>

            </div>

            <div id="games" class="tab-container">
            <div class="form-group">
            <div id="game-buttons">
            </div>
            <input type="hidden" id="game-select" name="game-select">
            </div>
            </div>

						<div class="inventory-container inventory-scroll default-scroll" id="general-inventory"></div>

          </div>
        </div>

				<div id="column-2" class="col-md-5 mt-3">

          <div class="card">

          <!-- Legacy HTML Navigation Buttons (disabled for Three.js map, re-enable by uncommenting below)
          <div class="d-flex btn-group world_nav_container">
            <button id="world_btn" class="world_nav_buttons active" onclick="displayWorlds();">
      <span class="nav-icon world-icon"></span> Overworld
  </button>
  <button id="zone_btn" class="world_nav_buttons" onclick="displayZones(nav.world);">
      <span class="nav-icon zone-icon"></span> Zones
  </button>
  <button id="locale_btn" class="world_nav_buttons" onclick="displayLocales(nav.zone);">
      <span class="nav-icon locale-icon"></span> Locales
  </button>
            <div id="tooltip"></div>
          </div>
          -->

			  <!-- Legacy Map Container (disabled for Three.js map, re-enable by uncommenting below)
			  <div id="map-bg">
          <div class="container-fluid" style="width: 546px;">
	        <div id="map-border"><div class="map shadow"></div></div>
		    </div>
			  </div>
          -->

            <!-- Three.js Map Navigation Container -->
            <div id="threejs-map-container" class="threejs-map-centered">
              <!-- Three.js canvas will be injected here -->
            </div>

   	  </div>

			</div>


      </div>
    </div>
  </div>
</div>

<div id="main-content"></div>

<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<!-- Core utilities first -->
<script src="utils/utils.js"></script>
<script src="utils/transact.js"></script>
<script src="utils/map-utils.js"></script>
<script src="utils/image.js"></script>
<script src="utils/adventures.js"></script>
<script src="utils/teams.js"></script>
<script src="utils/options.js"></script>

<!-- Data -->
<script src="data/story.js"></script>
<script src="data/books.js"></script>
<script src="data/urls.js"></script>
<script src="data/levels.js"></script>
<script src="data/generate.js"></script>
<script src="data/games.js"></script>

<!-- Map Editor -->
<script src="editor/mapEditorUtils.js"></script>
<script src="editor/mapZoneApi.js"></script>
<script src="editor/mapEditorSettings.js"></script>
<script src="editor/zoneGenerator.js"></script>
<script src="editor/modalUtils.js"></script>
<script src="editor/mapEditorQueue.js"></script>

<!-- Audio -->
<script src="audio/audio.js"></script>
<script src="audio/dialog-audio.js"></script>

<!-- Games -->
<!-- Core game system -->
<script src="games/core/BaseGame.js"></script>
<script src="games/core/GameManager.js"></script>
<script src="games/core/gameSounds.js"></script>

<!-- Utilities and effects -->
<script src="games/utils/GameUtils.js"></script>

<!-- Visual Effects - Individual effect files -->
<script src="games/fx/effects/ExplosionEffect.js"></script>
<script src="games/fx/effects/SparkleEffect.js"></script>
<script src="games/fx/effects/RippleEffect.js"></script>
<script src="games/fx/effects/CardFlipEffect.js"></script>
<script src="games/fx/effects/ScorePopupEffect.js"></script>
<script src="games/fx/effects/ButtonClickEffect.js"></script>
<script src="games/fx/effects/LevelUpEffect.js"></script>
<script src="games/fx/effects/GameOverEffect.js"></script>
<script src="games/fx/effects/BackgroundParticlesEffect.js"></script>
<script src="games/fx/effects/PulseEffect.js"></script>
<script src="games/fx/effects/WhiteBlinkEffect.js"></script>
<script src="games/fx/effects/TracerEffect.js"></script>
<script src="games/fx/effects/WindEffect.js"></script>
<script src="games/fx/effects/StarStreaksFX.js"></script>

<!-- Visual Effects - Main file -->
<script src="games/fx/VisualEffects.js"></script> 

<!-- Individual games -->
<script src="games/CardMatch.js"></script>
<script src="games/TreasureFrenzy.js"></script>
<!-- LaserDefender Game Modules -->
<script src="games/LaserDefender/PhysicsManager.js"></script>
<script src="games/LaserDefender/TurretManager.js"></script>
<script src="games/LaserDefender/ProjectileManager.js"></script>
<script src="games/LaserDefender/EnemyManager.js"></script>
<script src="games/LaserDefender/PowerupManager.js"></script>
<script src="games/LaserDefender/TurretMovement.js"></script>
<script src="games/LaserDefender/LaserDefender.js"></script>
<script src="games/BouncyClick/physics.js"></script>
<script src="games/BouncyClick/BouncyClick.js"></script>
<script src="games/DrawTheWord.js"></script>
<!-- ForestScavenger Game Modules -->
<script src="games/ForestScavenger/GridManager.js"></script>
<script src="games/ForestScavenger/PlayerManager.js"></script>
<script src="games/ForestScavenger/GoblinManager.js"></script>
<script src="games/ForestScavenger/MaterialManager.js"></script>
<script src="games/ForestScavenger/SafeZoneManager.js"></script>
<script src="games/ForestScavenger/ForestScavenger.js"></script>

<!-- Main games entry point -->
<!-- (removed games/core/games.js, now handled by GameManager.js) -->

<!-- API -->
<script src="api/tables.js"></script>
<script src="api/post.js"></script>
<script src="api/delete.js"></script>
<script src="api/user.js"></script>
<script src="api/logs.js"></script>

<!-- Player -->
<script src="player/init.js"></script>
<script src="services/authService.js"></script>
<script src="services/dataService.js"></script> 
<script src="services/claimables/claimable.js"></script>
<script src="services/mapService.js"></script>
<script src="services/uiService.js"></script>
<script src="player/payments.js"></script>
<script src="player/balances.js"></script>

<!-- UX Components -->
<script src="ux/components/button.js"></script>
<script src="ux/components/progress-bar.js"></script>
<script src="ux/components/map-component.js"></script>
<script src="ux/components/volume-slider.js"></script>
<script src="ux/components/filter.js"></script>

<!-- Dialog System -->
<script src="utils/typingfx.js"></script>
<script src="ux/components/dialog.js"></script>

<!-- UX Main -->
<script src="ux/init.js"></script>
<script src="ux/nav.js"></script>
<script src="ux/hud.js"></script>
<script src="ux/buttons.js"></script>
<script src="ux/icons.js"></script>
<script src="ux/alert.js"></script>
<script src="ux/shop.js"></script>

<!-- ThreeJS Map Navigation -->
<script src="ux/threejsmap/navigation.js"></script>
<script src="ux/threejsmap/tooltip/mini-tooltip.js"></script>
<script src="ux/threejsmap/renderer.js"></script>
<script src="ux/threejsmap/canvasNav.js"></script>
<script src="ux/threejsmap/transitionEffect.js"></script>
<script src="blockchain/blend.js"></script>
<script src="ux/modals/base-modals.js"></script>
<script src="ux/modals/info-modals.js"></script>
<script src="ux/modals/team-modals.js"></script>
<script src="ux/modals/GameModalManager.js"></script>
<script src="ux/modals/map-modals.js"></script>
<script src="ux/modals/house-modals.js"></script>
<script src="ux/modals/options-modal.js"></script>
<script src="ux/confirm-modal.js"></script>
<script src="ux/inventory.js"></script>
<script src="ux/start-screen.js"></script>

<!-- Initialize WAX before main.js -->
<script>
		console.log('[WAX Init] Initializing WAX object...');
		const wax = new waxjs.WaxJS({
				rpcEndpoint: 'https://wax.greymass.com'
		});
		console.log('[WAX Init] WAX object created:', wax);
		console.log('[WAX Init] Available methods:', Object.keys(wax));
</script>

<!-- Main entry point, orchestrates services and event listeners -->
<script src="main.js"></script>

<div id="main-alert" class="alert alert-primary" role="alert" style="display:none"></div>
<div id="main-info" class="alert alert-primary" role="alert" style="display:none"></div>
<div id="info-modal"></div>

  <script src="ux/threejsmap/loader.js"></script>
  <script src="services/battle/battle-launcher.js"></script>
 

</body>
</html>
