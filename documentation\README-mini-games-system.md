# Mini Games System Folder Structure

```
public/games/
├── README.md                 # This documentation file
├── games.js                  # Main entry point (legacy compatibility)
├── core/                     # Core game system files
│   ├── BaseGame.js          # Base game class with common functionality
│   └── GameManager.js       # Game initialization and management
├── utils/                    # Utility functions and helpers
│   └── GameUtils.js         # DOM utilities, HUD management, common functions
├── fx/                       # Visual effects and animations
│   └── VisualEffects.js     # Particle effects, animations, visual feedback
└── games/                    # Individual game implementations
    ├── CardMatch.js         # Card matching game
    └── TreasureFrenzy.js    # Ball clicking game
```

## Key Features

### 🎮 **Countdown System**
- 3-second countdown before each game starts
- Gives players time to prepare after credit transaction
- Visual countdown overlay with "3, 2, 1, GO!" sequence

### 🎨 **Visual Effects System**
- Consistent particle effects across all games
- Explosion effects, sparkles, ripples, score popups
- Level up animations and game over effects
- Performance-optimized particle pooling

### 🛠️ **Modular Architecture**
- **BaseGame**: Common game functionality (timer, HUD, countdown)
- **GameManager**: Game initialization and modal management
- **GameUtils**: DOM utilities and common functions
- **VisualEffects**: Reusable visual effects and animations

### 🔧 **Easy Game Development**
- Extend `BaseGame` class for new games
- Override `onCountdownComplete()` to start game-specific logic
- Use `VisualEffects` for consistent visual feedback
- Automatic credit transaction and countdown handling

## Adding New Games

### 1. Create Game File
Create a new file in `games/` folder (e.g., `MyNewGame.js`):

```javascript
class MyNewGame extends BaseGame {
    constructor() {
        super();
        // Initialize game-specific properties
    }

    onCountdownComplete() {
        // Start your game logic here
        this.startMyGame();
    }

    startMyGame() {
        // Your game implementation
    }
}
```

### 2. Register Game
Add your game to the game registry in `GameManager.js`:

```javascript
initializeGame(game) {
    try {
        switch (game.name) {
            case "My New Game":
                this.currentGame = new MyNewGame();
                break;
            // ... other games
        }
    } catch (error) {
        GameUtils.showAlert('Failed to initialize game. Please try again.');
        return;
    }
}
```

### 3. Add Game Data
Add game information to `public/data/games.js`:

```javascript
{
    "id": 3,
    "type": "land",
    "name": "My New Game",
    "image": "",
    "instructions": "Your game instructions here!",
    "description": "A description of your game."
}
```

### 4. Update Active Games
Add your game ID to the `active_mini_games` array in `games.js`:

```javascript
var active_mini_games = [1, 2, 3]; // Add your game ID
```

## Visual Effects Usage

### Basic Effects
```javascript
// Explosion effect
VisualEffects.createExplosion(x, y, color, particleCount, stage);

// Sparkle effect
VisualEffects.createSparkle(x, y, stage, color);

// Score popup
VisualEffects.createScorePopup(x, y, score, stage, color);

// Level up effect
VisualEffects.createLevelUpEffect(stage);
```

### Advanced Effects
```javascript
// Background particles
VisualEffects.createBackgroundParticles(stage, count);

// Pulse effect on objects
VisualEffects.createPulseEffect(target, duration);

// Ripple effect
VisualEffects.createRipple(x, y, stage, color);
```

## HTML Integration

Include the organized files in your HTML:

```html
<!-- Core system -->
<script src="public/games/core/BaseGame.js"></script>
<script src="public/games/core/GameManager.js"></script>

<!-- Utilities and effects -->
<script src="public/games/utils/GameUtils.js"></script>
<script src="public/games/fx/VisualEffects.js"></script>

<!-- Individual games -->
<script src="public/games/games/CardMatch.js"></script>
<script src="public/games/games/TreasureFrenzy.js"></script>

<!-- Main entry point -->
<script src="public/games/games.js"></script>
```
 