class DialogAudioManager {
    constructor() {
        this.typingAudio1 = null;
        this.typingAudio2 = null;
        this.typingInterval = null;
        this.isPlaying = false;
        this.audioContext = null;
        this.gainNode = null;
        this.init();
    }

     init() {
        // Get the dialogue sounds from AudioManager
        if (window.AudioManager) {
            this.typingAudio1 = window.AudioManager.sounds.ui.dialog1;
            this.typingAudio2 = window.AudioManager.sounds.ui.dialog2;
        } else {
            // Fallback if AudioManager is not available
            this.typingAudio1 = new Audio('/audio/sfx/Dialogue-1.wav');
            this.typingAudio2 = new Audio('/audio/sfx/Dialogue-2.wav');
        }

        // Initialize Web Audio API for enhanced effects
        this.initWebAudio();
    }

    initWebAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.gainNode = this.audioContext.createGain();
            this.gainNode.connect(this.audioContext.destination);
        } catch (error) {
            console.warn('Web Audio API not supported, falling back to basic audio:', error);
        }
    }
    
    startTypingAudio() {
        if (this.isPlaying) return;
        
        this.isPlaying = true;
        
        // Play immediately
        this.playTypingSound();
        
        // Set up interval to play every 1 second
        this.typingInterval = setInterval(() => {
            this.playTypingSound();
        }, 1000);
    }

    stopTypingAudio() {
        this.isPlaying = false;
        
        if (this.typingInterval) {
            clearInterval(this.typingInterval);
            this.typingInterval = null;
        }
    }

    playTypingSound() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
        const randomChoice = Math.random() < 0.5;
        const selectedAudio = randomChoice ? this.typingAudio1 : this.typingAudio2;
        
        if (selectedAudio && this.audioContext) {
            // Always use enhanced audio processing on the WAV files
            this.playEnhancedSound(selectedAudio);
        } else if (selectedAudio) {
            // Fallback to basic audio
            selectedAudio.currentTime = 0;
            selectedAudio.play().catch(error => {
                console.warn('Failed to play typing audio:', error);
            });
        }
    }

    playEnhancedSound(audioElement) {
        try {
            // Create audio source from the audio element
            const source = this.audioContext.createMediaElementSource(audioElement);
            
            // Create filters for Banjo-Kazooie style effects
            const lowpassFilter = this.audioContext.createBiquadFilter();
            const highpassFilter = this.audioContext.createBiquadFilter();
            const distortion = this.audioContext.createWaveShaper();
            
            // Configure filters with more variation for Banjo-Kazooie effect
            lowpassFilter.type = 'lowpass';
            lowpassFilter.frequency.value = 600 + Math.random() * 600; // Random frequency between 600-1200Hz
            lowpassFilter.Q.value = 0.3 + Math.random() * 0.4; // Random Q value
            
            highpassFilter.type = 'highpass';
            highpassFilter.frequency.value = 150 + Math.random() * 350; // Random frequency between 150-500Hz
            highpassFilter.Q.value = 0.3 + Math.random() * 0.4; // Random Q value
            
            // Create distortion curve for "gibberish" effect
            const curve = new Float32Array(44100);
            const deg = Math.PI / 180;
            for (let i = 0; i < 44100; i++) {
                const x = (i * 2) / 44100 - 1;
                curve[i] = (3 + 20) * x * 20 * deg / (Math.PI + 20 * Math.abs(x));
            }
            distortion.curve = curve;
            distortion.oversample = '4x';
            
            // Create pitch modulation (like Banjo's voice variations)
            const oscillator = this.audioContext.createOscillator();
            const pitchGain = this.audioContext.createGain();
            oscillator.frequency.value = 1.5 + Math.random() * 4; // 1.5-5.5 Hz modulation
            pitchGain.gain.value = 30 + Math.random() * 120; // Random pitch variation
            oscillator.connect(pitchGain);
            
            // Create volume envelope for more natural sound
            const volumeEnvelope = this.audioContext.createGain();
            const envelopeDuration = 0.2 + Math.random() * 0.4; // 0.2-0.6 seconds
            volumeEnvelope.gain.setValueAtTime(0, this.audioContext.currentTime);
            volumeEnvelope.gain.linearRampToValueAtTime(0.6 + Math.random() * 0.4, this.audioContext.currentTime + 0.03);
            volumeEnvelope.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + envelopeDuration);
            
            // Create a second oscillator for additional "gibberish" effect
            const gibberishOsc = this.audioContext.createOscillator();
            const gibberishGain = this.audioContext.createGain();
            gibberishOsc.type = 'sine';
            gibberishOsc.frequency.value = 200 + Math.random() * 400; // 200-600 Hz
            gibberishGain.gain.value = 0.05 + Math.random() * 0.1; // Low volume for subtle effect
            
            // Connect the audio chain
            source.connect(lowpassFilter);
            lowpassFilter.connect(highpassFilter);
            highpassFilter.connect(distortion);
            distortion.connect(volumeEnvelope);
            volumeEnvelope.connect(this.gainNode);
            
            // Apply pitch modulation to the source
            pitchGain.connect(source.playbackRate);
            
            // Add gibberish oscillator
            gibberishOsc.connect(gibberishGain);
            gibberishGain.connect(volumeEnvelope);
            
            // Start the gibberish oscillator
            gibberishOsc.start(this.audioContext.currentTime);
            gibberishOsc.stop(this.audioContext.currentTime + envelopeDuration);
            
            // Reset and play
            audioElement.currentTime = 0;
            audioElement.play().catch(error => {
                console.warn('Failed to play enhanced typing audio:', error);
            });
            
        } catch (error) {
            console.warn('Enhanced audio failed, falling back to basic:', error);
            // Fallback to basic audio
            audioElement.currentTime = 0;
            audioElement.play().catch(err => {
                console.warn('Failed to play typing audio:', err);
            });
        }
    }

    isTypingAudioPlaying() {
        return this.isPlaying;
    }
}

window.dialogAudioManager = new DialogAudioManager();

if (typeof module !== 'undefined' && module.exports) {
    module.exports = DialogAudioManager;
} 