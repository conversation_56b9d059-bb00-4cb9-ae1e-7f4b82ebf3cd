function displayCreateTeamModal(displayInvFunc, filterFunc) {
  document.getElementById('general-inventory').innerHTML='';
  $('#create-team-modal-title').text('Create Team');
  $('#create-team-modal-footer')
    .append(
      !$('#create-team-modal-footer').find('button[onclick="confirmTeam()"]').length &&
        $('<button>', {
          class: 'btn-secondary',
          onclick: 'confirmTeam()',
          html: '<img src="../images/ui/approve_small_icon.png"> Confirm Team'
        })
    );
  $('#create-team-modal').css('display', 'block');
  displayInvFunc('creature', 'new-team-inventory', filterFunc);
}
 
function createTeamViewModal() {
  var content = {
    'body': 'This team has not been named.',
    'footer': ''
  };
  createModal("team-view", content, "main-content");
}
 
function createTeamModal() {
  const content = {
    body: `
      <div class="inventory-container inventory-scroll" id="new-team-inventory" style="max-height:250px;"></div>
      <div class="grid-header" id="new-team-inventory-header"></div>
      <div class="grid-inventory" id="new-team-selected-assets"></div>`,
    footer: ''
  };
  createModal("create-team-modal", content, "main-content");
}
