const { Router } = require('express'); 
const globalLimitController = require("../../controls/logs/global-limit"); 
const router = Router(); 
// Get all global limits
router.get("/", globalLimitController.getGlobalLimits); 
// Get global limit by reward type
router.get("/:reward_type", globalLimitController.getGlobalLimitByType); 
// Update global limit count
router.put("/:reward_type", globalLimitController.updateGlobalLimitCount); 
// Reset global limit count
router.post("/reset/:reward_type", globalLimitController.resetGlobalLimitCount); 
// Create a new global limit
router.post("/", globalLimitController.createGlobalLimit);

module.exports = router;