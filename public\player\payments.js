async function transactResource(type, amt, operation = 'subtract', updateFunction) {
  const resources = {
    credits: {
      balance: playerData.credits,
      precision: 0,
      successMessage: 'Transacted Credits successfully!',
      errorMessage: 'Not Enough Credits!'
    },
    gxp: {
      balance: Number(playerData.gxp),
      precision: 3,
      successMessage: 'Transacted GXP successfully!',
      errorMessage: 'Not Enough GXP!'
    },
    nectar: {
      balance: playerData.nectar,
      precision: 3,
      successMessage: 'Transacted Nectar successfully!',
      errorMessage: 'Not enough nectar!'
    }
  };
  const resource = resources[type];

  if (!resource) {
    showAlert('Invalid resource type!');
    return;
  }

  if (operation === 'subtract' && resource.balance < amt) {
    showAlert(`Not Enough ${type}.`);
    return;
  }
  if (operation === 'add') {
    playerData[type] += amt;
  } else if (operation === 'subtract') {
    playerData[type] -= amt;
  } else {
    showAlert('Invalid operation!');
    return;
  }
  playerData[type] = Number(playerData[type].toFixed(resource.precision));
 
  await updateFunction(); 

  if (typeof updateBalanceUI === 'function') {
    await updateBalanceUI();
  } else {
    console.warn('DEBUG: transactResource - updateBalanceUI function not available');
  }

  showAlert(resource.successMessage);
}

async function confirmZonePayment(getZoneInfoFunction, transactResourceFunction, updateZoneFunction, getMapDataFunction, customAmount = null) { 
 

  var amt_to_pay = customAmount !== null ? customAmount : $("#gxp_input").val();
  amt_to_pay = Number(amt_to_pay);

  if (amt_to_pay <= 0) {
    showAlert("Payment amount must be greater than 0 GXP.");
    return;
  }
  showAlert(`You will pay: ${amt_to_pay} GXP.`);

  try {
    var zone_gxp_req = getZoneInfoFunction('gxp_required', nav.world, nav.zone, allZones);
    var zone_id = getZoneInfoFunction('id', nav.world, nav.zone, allZones);

    if (zone_gxp_req < amt_to_pay) {
      showAlert(`Overpayment! Cannot pay this amount: ${amt_to_pay} GXP - Required GXP for Zone: ${zone_gxp_req}.`);
      return;
    } else if (amt_to_pay > 0) {
      await transactResourceFunction('gxp', amt_to_pay, 'subtract', updatePlayerBalances);

      const zoneUpdateResult = await updateZoneFunction(zone_id, amt_to_pay);

      if (zoneUpdateResult && zoneUpdateResult.success) {
        showAlert(`Successfully paid ${amt_to_pay} GXP towards zone ${nav.world + 1}-${nav.zone + 1}!`);
      } else {
        console.error('ERROR: confirmZonePayment - Zone update failed:', zoneUpdateResult?.error);
        showAlert(`GXP payment processed, but zone update failed. Please refresh and try again.`);
      }

      await getMapDataFunction();
    }
  } catch (error) {
    console.error("ERROR in confirmZonePayment:", error);
    showAlert("An error occurred while processing your payment. Please try again.");
  }
}