// we need to move this reward status function to the server, so that when adventure is complete and deleted, we immediately update to claimed on the rewards
async function updateRewardStatus(event_id) {
  if (!event_id) {
    console.error('ERROR: updateRewardStatus - event_id is undefined or null');
    return false;
  }

  try {
    const checkUrl = `${domain_url}/rewards/eventid/${event_id}`;
    const checkResponse = await axios.get(checkUrl);

    if (!checkResponse.data || checkResponse.data.length === 0) {
      console.error('ERROR: updateRewardStatus - No reward found for adventure_id:', event_id);
      return false;
    }
  } catch (checkError) {
    console.error('ERROR: updateRewardStatus - Error checking for reward:', checkError.message);
    console.error('ERROR: updateRewardStatus - Status code:', checkError.response?.status);
    return false;
  }

  const url = `${domain_url}/rewards/${event_id}`;
  const change = {
    "event_id": event_id,
    "status": "Claimed"
  };
  const config = {
    headers: {
      "Content-Type": "application/json",
    },
  };

  try {
    const response = await axios.put(url, change, config);

    if (typeof AudioManager !== 'undefined') {
      AudioManager.playUISound('rewardclaim');
    }

    return true;
  } catch (error) {
    console.error('ERROR: updateRewardStatus - Request failed:', error.message);
    console.error('ERROR: updateRewardStatus - Status code:', error.response?.status);
    console.error('ERROR: updateRewardStatus - Response data:', error.response?.data);
    return false;
  }
}

async function updateZoneBalance(row_id, amt) {
  try {
    let zoneInfo = null;
    for (let i = 0; i < allZones.length; i++) {
      if (allZones[i].id == row_id) {
        zoneInfo = allZones[i];
        break;
      }
    }

    if (!zoneInfo) {
      console.error('ERROR: updateZoneBalance - Zone not found with ID:', row_id);
      return { success: false, error: 'Zone not found' };
    }

    const total_gxp_zone = getZoneInfo('gxp_paid', zoneInfo.mapgrid_4, zoneInfo.mapgrid_16, allZones);
    const numericAmt = Number(amt);
    const numericTotalGxp = Number(total_gxp_zone);
    const numericSum = numericAmt + numericTotalGxp;
    const new_total_bal = Number(numericSum.toFixed(2));

    const url = domain_url + '/players/zones/' + row_id;
    const change = {
      "gxp_paid": new_total_bal,
      "id": row_id
    };
    const options = {
      headers: {
        "Content-Type": "application/json"
      }
    };

    const response = await axios.put(url, change, options);
    return { success: true, data: response.data };
  } catch (error) {
    console.error('ERROR: updateZoneBalance - Request failed:', error.message);
    console.error('ERROR: updateZoneBalance - Status code:', error.response?.status);
    console.error('ERROR: updateZoneBalance - Response data:', error.response?.data);

    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data
    };
  }
}

async function addAdventureApi(team_id) {
  if (!team_id) {
    console.error('ERROR: addAdventureApi - team_id is undefined or null');
    return { success: false, error: 'Team ID is missing' };
  }

  if (!newDestination) {
    console.error('ERROR: addAdventureApi - newDestination is undefined or null');
    return { success: false, error: 'Destination information is missing' };
  }

  const worldValid = newDestination.world !== undefined && newDestination.world !== null && !isNaN(Number(newDestination.world));
  const zoneValid = newDestination.zone !== undefined && newDestination.zone !== null && !isNaN(Number(newDestination.zone));
  const localeValid = newDestination.locale !== undefined && newDestination.locale !== null && !isNaN(Number(newDestination.locale));

  if (!worldValid || !zoneValid || !localeValid) {
    console.error('ERROR: addAdventureApi - newDestination is invalid:', newDestination);
    console.error('ERROR: addAdventureApi - JSON.stringify(newDestination):', JSON.stringify(newDestination));
    return { success: false, error: 'Destination information is missing' };
  }

  const payload = {
    "owner_id": wax.userAccount,
    "team_id": Number(team_id),
    "init_steps": 100,
    "current_steps": 0,
    "mapgrid_4": Number(newDestination.world),
    "mapgrid_16": Number(newDestination.zone),
    "mapgrid_256": Number(newDestination.locale),
    "status": "In Progress"
  };

  try {
    if (!sessionToken) {
      try {
        sessionToken = await login();
      } catch (loginError) {
        console.error('ERROR: addAdventureApi - Login error:', loginError);
        return { success: false, error: 'Authentication failed: ' + loginError.message };
      }
    }

    if (!sessionToken) {
      console.error('ERROR: addAdventureApi - Failed to obtain sessionToken');
      return { success: false, error: 'Failed to authenticate' };
    }

    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${sessionToken}`
      }
    };

    const response = await axios.post(`${domain_url}/adventures/`, payload, config);
    return { success: true, data: response.data };
  } catch (error) {
    console.error('ERROR: addAdventureApi -', error.message);
    console.error('ERROR: Status code:', error.response?.status);
    console.error('ERROR: Response data:', error.response?.data);

    if (error.response && error.response.status === 401) {
      try {
        sessionToken = await login();
        if (sessionToken) {
          const config = {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${sessionToken}`
            }
          };
          const retryResponse = await axios.post(`${domain_url}/adventures/`, payload, config);
          return { success: true, data: retryResponse.data };
        } else {
          return { success: false, error: 'Failed to refresh authentication' };
        }
      } catch (retryError) {
        console.error('ERROR: addAdventureApi - Retry failed:', retryError);
        return { success: false, error: 'Authentication retry failed: ' + retryError.message };
      }
    }

    if (error.response && error.response.status === 400) {
      return { success: false, error: error.response.data };
    }

    return { success: false, error: error.message || 'Unknown error' };
  }
}

async function updateTeamHouse(team, asset_id, alertFunc) {
  const data = { "house": asset_id };
  const config = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  try {
    const response = await axios.put(domain_url + '/teams/sethouse/' + team, { data }, config);
    if (response.status === 200) {
      alertFunc("Team house updated successfully.");
    } else {
      alertFunc("Error updating team house: " + response.status);
    }
  } catch (error) {
     console.log(error);
  }
}

async function postTeamData(teamData) {
  try {
    const response = await axios.post(domain_url + '/teams/', teamData, {
      headers: {
        'wax-user-account': wax.userAccount,
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    console.error("Error posting team data:", error);
    throw error;
  }
}