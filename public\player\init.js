var schemas = ['creature', 'food', 'items', 'vehicles', 'houses', 'coins'];
var nav = {
  view: 'world',
  world: 0,
  zone: 0,
  locale: 0
}
// Track the current inventory view
var currentInventoryView = 'creature';
var userAccount;
var sessionToken = null;
var session;
var waxId;
var token;
var playerData = {
  wallet: "",
  joinDate: "",
  lastOnline: new Date(),
  gxp: 0,
  nectar: 0,
  credits: 0,
  gameStats: {},
  dust: 0,
  wax: 0,
  crushiecoins: 0
};

var teamCounts = { playerTeams: {}, otherTeams: {} };

// temporary holding for duplicated var
var currentzonesJson = [];
var allBalances = {};
var allZones = [];
var allAdventures = [];
let allTeams = [];
let myTeams = [];
let activeAdventures = [];
let playerAdventures = [];
let allTeamsCreatures = [];
let allTeamsVehicles = [];
let vehiclesData = {};
let creaturesData = {};
let housesData = {};
var mainTimer;
var housesOccupied = [];
var newTeamObject = {
  vehicles: [],
  creatures: [],
  capacity: 0,
  house: 'None'
}
var newDestination = {
  world: 0,
  zone: 0,
  locale: 0
};
var playerCounter = {
  rewardsClaimable:0,
  teamsReady:0,
  teamsNapping:0
}
var enableSetAdventure=false;
var teamSelectedForAdventure='None';
var playerOptions = {}

// Initialize player options when user logs in
function initializePlayerOptionsOnLogin(waxId) {
  if (typeof initializePlayerOptions === 'function') {
    initializePlayerOptions(waxId);
  } else {
    // Fallback initialization if options modal isn't loaded yet
    playerOptions = {
      mapTransition: 'zoom',
      musicEnabled: true
    };
  }
}

// Map editor vars init
var mapEditorEnable=false;
var domain_url = "https://express-crushie.herokuapp.com";
var mapEditorQueue = [];
// These variables are now defined in mapEditorSettings.js
// var setLocaleName = "undefined";
// var setLocaleTile = "undefined";
// var setLocaleLandType = "undefined";
var randomLocaleNames = "false";
