// StarStreaksFX.js
// Reusable effect: fast vertical white streaks ("light speed" effect)
// Usage: var streaks = new window.StarStreaksFX(app.stage, { speed: 12, rate: 0.08, parallax: true, parallaxStrength: 2 });

function StarStreaksFX(parent, options) {
    options = options || {};
    this.parent = parent;
    this.speed = options.speed || 10;
    this.rate = options.rate || 0.1;
    this.width = options.width || 2;
    this.length = options.length || 60;
    this.alpha = options.alpha || 0.7;
    this.parallax = options.parallax || false;
    this.parallaxStrength = (typeof options.parallaxStrength === 'number') ? options.parallaxStrength : 1;
    this.streaks = [];
    this.spawnAccumulator = 0;
    // Two containers: one for starfield, one for streaks
    this.starfieldContainer = new PIXI.Container();
    this.streaksContainer = new PIXI.Container();
    this.parent.addChildAt(this.starfieldContainer, 0); // background
    this.parent.addChildAt(this.streaksContainer, 1);   // above starfield
    this._running = false;
    this._boundUpdate = this.update.bind(this);
    this._resizeListener = this.onResize.bind(this);
    this.resize();
    window.addEventListener('resize', this._resizeListener);
}

StarStreaksFX.prototype.start = function() {
    if (!this._running) {
        this._running = true;
        PIXI.Ticker.shared.add(this._boundUpdate);
    }
};

StarStreaksFX.prototype.stop = function() {
    if (this._running) {
        this._running = false;
        PIXI.Ticker.shared.remove(this._boundUpdate);
    }
};

StarStreaksFX.prototype.destroy = function() {
    this.stop();
    window.removeEventListener('resize', this._resizeListener);
    this.starfieldContainer.removeChildren();
    this.streaksContainer.removeChildren();
    if (this.starfieldContainer.parent) this.starfieldContainer.parent.removeChild(this.starfieldContainer);
    if (this.streaksContainer.parent) this.streaksContainer.parent.removeChild(this.streaksContainer);
    this.streaks = [];
};

StarStreaksFX.prototype.resize = function() {
    // Get parent size (assume renderer size)
    var renderer = this.parent.parent && this.parent.parent.renderer ? this.parent.parent.renderer : null;
    this.widthPx = renderer ? renderer.width : window.innerWidth;
    this.heightPx = renderer ? renderer.height : window.innerHeight;
};

StarStreaksFX.prototype.onResize = function() {
    this.resize();
};

StarStreaksFX.prototype.initStarfield = function() {
    this.starfieldStars = [];
    var starCount = this.starfieldCount || 80;
    var width = this.widthPx;
    var height = this.heightPx;
    this.starfieldContainer.removeChildren();
    for (var i = 0; i < starCount; i++) {
        var star = new PIXI.Graphics();
        var size = Math.random() * 1.2 + 0.5;
        star.beginFill(0xFFFFFF, Math.random() * 0.7 + 0.3);
        star.drawCircle(0, 0, size);
        star.endFill();
        star.x = Math.random() * width;
        star.y = Math.random() * height;
        star._starSpeed = (Math.random() * 0.5 + 0.1) * (this.parallax ? this.parallaxStrength : 1); // parallax
        this.starfieldContainer.addChild(star);
        this.starfieldStars.push(star);
    }
};

StarStreaksFX.prototype.update = function() {
    // Move starfield
    if (this.starfieldStars) {
        for (var i = 0; i < this.starfieldStars.length; i++) {
            var star = this.starfieldStars[i];
            star.y += star._starSpeed;
            if (star.y > this.heightPx) {
                star.y = -2;
                star.x = Math.random() * this.widthPx;
            }
        }
    }
    // Move streaks
    for (var i = this.streaks.length - 1; i >= 0; i--) {
        var streak = this.streaks[i];
        streak.y += this.speed;
        if (streak.y > this.heightPx) {
            this.streaksContainer.removeChild(streak);
            this.streaks.splice(i, 1);
        }
    }
    // Spawn new streaks
    this.spawnAccumulator += this.rate;
    while (this.spawnAccumulator > 1) {
        this.spawnStreak();
        this.spawnAccumulator -= 1;
    }
};

StarStreaksFX.prototype.spawnStreak = function() {
    var x = Math.random() * this.widthPx;
    var y = -this.length;
    var streak = new PIXI.Graphics();
    streak.beginFill(0xFFFFFF, this.alpha);
    streak.drawRect(-this.width / 2, 0, this.width, this.length);
    streak.endFill();
    streak.x = x;
    streak.y = y;
    this.streaksContainer.addChild(streak);
    this.streaks.push(streak);
};

var _oldCtor = StarStreaksFX;
StarStreaksFX = function(parent, options) {
    _oldCtor.call(this, parent, options);
    this.starfield = (options && typeof options.starfield !== 'undefined') ? options.starfield : true;
    this.starfieldCount = (options && options.starfieldCount) || 80;
    this.parallax = options && options.parallax;
    this.parallaxStrength = (typeof options.parallaxStrength === 'number') ? options.parallaxStrength : 1;
    if (this.starfield) {
        this.initStarfield();
    } else {
        this.starfieldStars = [];
    }
};
StarStreaksFX.prototype = Object.create(_oldCtor.prototype);
StarStreaksFX.prototype.constructor = StarStreaksFX;

if (typeof window !== 'undefined') {
    window.StarStreaksFX = StarStreaksFX;
} 