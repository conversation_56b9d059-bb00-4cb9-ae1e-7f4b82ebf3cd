const pool = require("../db");
const queries = require("../queries");

const addSystemLog = (req, res) => {
  const {
    msg_type,
    category,
    reward_type,
    message,
    details
  } = req.body;
  // Validate required fields
  if (!msg_type || !category || !message) {
    res.status(400).send("Missing required fields: msg_type, category, and message are required");
    return;
  }
  // Validate msg_type
  const validMsgTypes = ['INFO', 'WARNING', 'ERROR', 'CRITICAL'];
  if (!validMsgTypes.includes(msg_type)) {
    res.status(400).send(`Invalid msg_type. Must be one of: ${validMsgTypes.join(', ')}`);
    return;
  }
  // Insert the log
  pool.query(
    queries.add.addSystemLog,
    [msg_type, category, reward_type, message, details],
    (error, results) => {
      if (error) {
        res.status(500).send(error.message);
        console.error(error);
        return;
      }
      res.status(201).send("System log added successfully");
    }
  );
};

const getSystemLogs = (req, res) => {
  pool.query(queries.get.getSystemLogs, (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

const getSystemLogsByType = (req, res) => {
  const msgType = req.params.msg_type;

  pool.query(queries.getby.getSystemLogsByType, [msgType], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

const getSystemLogsByCategory = (req, res) => {
  const category = req.params.category;

  pool.query(queries.getby.getSystemLogsByCategory, [category], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

const getSystemLogsByRewardType = (req, res) => {
  const rewardType = req.params.reward_type;

  pool.query(queries.getby.getSystemLogsByRewardType, [rewardType], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

const getRecentSystemLogs = (req, res) => {
  // First, clean up expired logs (older than 30 days)
  pool.query(
    "DELETE FROM os_msg WHERE created_at < NOW() - INTERVAL '30 days' RETURNING *",
    (error, cleanupResults) => {
      if (error) {
        res.status(500).send(error.message);
        console.error("Cleanup error:", error);
        return;
      }

      const deletedCount = cleanupResults.rowCount; // Count of deleted rows

      // Then, fetch recent logs (last 24 hours)
      pool.query(
        "SELECT * FROM os_msg WHERE created_at > NOW() - INTERVAL '24 hours' ORDER BY created_at DESC",
        (error, logResults) => {
          if (error) {
            res.status(500).send(error.message);
            console.error("Fetch logs error:", error);
            return;
          }

          // Send response with recent logs and cleanup info
          res.status(200).json({
            recentLogs: logResults.rows,
            cleanup: `Cleaned up ${deletedCount} expired logs`,
          });
        }
      );
    }
  );
};

module.exports = {
  addSystemLog,
  getSystemLogs,
  getSystemLogsByType,
  getSystemLogsByCategory,
  getSystemLogsByRewardType,
  // cleanupExpiredLogs,
  getRecentSystemLogs
};
