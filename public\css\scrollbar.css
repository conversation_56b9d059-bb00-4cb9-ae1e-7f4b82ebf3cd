.scroll-default, .scroll-wood { 
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* Default Scrollbar Style */
  .scroll-default::-webkit-scrollbar {
    width: 16px;
  }

  .scroll-default::-webkit-scrollbar-track {
    background: url('../images/ui/sb/sb-default-track.png');
  }

  .scroll-default::-webkit-scrollbar-thumb {
    background: url('../images/ui/sb/sb-default-thumb.png');
  }

  .scroll-default::-webkit-scrollbar-button:vertical:decrement {
    background: url('../images/ui/sb/sb-default-arrow-up.png');
    height: 16px;
  }

  .scroll-default::-webkit-scrollbar-button:vertical:increment {
    background: url('../images/ui/sb/sb-default-arrow-down.png');
    height: 16px;
  }

  /* Wood Scrollbar Style */
  .scroll-wood::-webkit-scrollbar {
    width: 16px;
  }

  .scroll-wood::-webkit-scrollbar-track {
    background: url('../images/ui/sb/sb-wood-track.png');
  }

  .scroll-wood::-webkit-scrollbar-thumb {
    background: url('../images/ui/sb/sb-wood-thumb.png');
  }

  .scroll-wood::-webkit-scrollbar-button:vertical:decrement {
    background: url('../images/ui/sb/sb-wood-arrow-up.png');
    height: 16px;
  }

  .scroll-wood::-webkit-scrollbar-button:vertical:increment {
    background: url('../images/ui/sb/sb-wood-arrow-down.png');
    height: 16px;
  }