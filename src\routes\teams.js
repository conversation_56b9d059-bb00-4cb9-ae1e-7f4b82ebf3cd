// where the express routes are stored
const { Router } = require('express');
const allQuery = require("../controls/controller");

const router = Router();

router.get("/", allQuery.getTeams);
router.get("/:owner_id", allQuery.getTeamsByOwnerId);
router.get("/:owner_id/:team_id", allQuery.getTeamByTeamId);

router.post("/", allQuery.addTeam);

router.put("/:team_id", allQuery.updateTeam);
router.put("/sethouse/:team_id", allQuery.updateTeamHouse);
router.put("/setnap/:team_id", allQuery.updateTeamNap);
router.put("/setnap-batch", allQuery.updateTeamNapBatch);
router.put("/setlocation/:team_id", allQuery.updateTeamLocation);
router.put("/setstatus/:team_id", allQuery.updateTeamStatus);

router.delete("/:team_id", allQuery.removeTeam);

module.exports = router;
