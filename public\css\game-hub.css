#game-area {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

#game-container {
    position: relative;
    height: 500px;
    background-color: #222;
    box-shadow: 0 0 10px rgba(113, 113, 113, 0.5); /* Default box-shadow for dark mode */
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    overflow: hidden;
    border-radius: 12px;
}

#game-modal .modal-dialog {
    max-width: 550px; /* Adjust this value as needed to widen the game modal */
}

#game-over-screen {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url('../images/ui/bg/mini-game-default-bg.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 10;
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#game-over-screen h2 {
    font-size: 32px;
    color: #fff;
    margin-bottom: 20px;
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#game-over-screen p {
    font-size: 18px;
    color: #fff;
    margin-bottom: 10px;
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.hud {
    position: absolute;
    top: 4%;
    left: 50%; /* Move the left edge to the center of the parent */
    transform: translate(-50%, -50%); /* Shift the element back by half its width and height */
    display: flex;
    flex-direction: row;
    align-items: center; /* Center items vertically within the container */
    justify-content: center; /* Center items horizontally within the container */
    gap: 50px;
    z-index: 2; /* Ensure HUD elements are above the background image */
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}  

.hud-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    z-index: 1;
}

.hud-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hud-item {
    display: flex;
    align-items: center;
    gap: 5px; 
    color: #ffffff;
    text-shadow: -1px 1px 0 #551843;
    font-weight: bold;
    text-transform: uppercase; 

    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.hud-item img {
    width: 20px;
    height: 20px;
}

#game-instructions {
    position: absolute;
    bottom: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 8px 12px;
    border-radius: 6px;
    max-width: 500px;
    font-size: 12px;
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    /* Add a subtle pulse animation to indicate it's clickable */
    animation: subtle-pulse 2s ease-in-out infinite;
}

@keyframes subtle-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
    }
}

#game-instructions:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.02);
    animation: none; /* Stop the pulse animation on hover */
}

#game-instructions img {
    width: 20px;
    height: 20px;
}

#main-menu-button, #play-again-button, #start-button {
    padding: 15px 30px;
    font-size: 18px;
    background-color: #D3D3D3; /* Very light gray */
    color: #000;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s, box-shadow 0.3s; /* Smooth transition for background and shadow */
    margin: 5px;
    box-shadow: 0 4px #B0B0B0; /* Slightly darker gray for the shadow */
}

#main-menu-button:hover, #play-again-button:hover, #start-button:hover {
    background-color: #E0E0E0; /* Even brighter gray for hover */
    box-shadow: 0 4px #C0C0C0; /* Slightly lighter gray for hover shadow */
}

#start-screen {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url('../images/ui/bg/mini-game-default-bg.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 10;
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#start-screen h1 {
    font-size: 32px;
    color: #fff;
    margin-bottom: 20px;
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#start-screen p {
    color: #fff;
    margin-bottom: 20px;
    text-align: center;
    /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
