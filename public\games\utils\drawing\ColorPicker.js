// ColorPicker.js - Color selection tool for DrawingCanvas
class ColorPicker {
    constructor(container, options = {}) {
        this.container = container;
        this.onColorChange = options.onColorChange || (() => {});
        this.selectedColor = options.defaultColor || 0x222222;
        this.colors = [
            0x222222, // Black
            0xFF0000, // Red
            0x00FF00, // Green
            0x0000FF, // Blue
            0xFFFF00  // Yellow
        ];
        this.createColorSwatches();
        this.createCustomColorPicker();
    }

    createColorSwatches() {
        this.swatchContainer = new PIXI.Container();
        this.container.addChild(this.swatchContainer);

        const swatchSize = 22; // Smaller swatch
        const padding = 0; // No gap
        const swatchesPerRow = this.colors.length; // Single row

        this.colors.forEach((color, index) => {
            const col = index;
            
            const swatch = new PIXI.Graphics();
            swatch.beginFill(color);
            swatch.drawRect(0, 0, swatchSize, swatchSize);
            swatch.endFill();
            
            // Add border
            swatch.lineStyle(2, 0xFFFFFF, 1);
            swatch.drawRect(0, 0, swatchSize, swatchSize);
            
            swatch.x = col * (swatchSize + padding);
            swatch.y = 0;
            
            swatch.interactive = true;
            swatch.cursor = 'pointer';
            swatch.on('pointerdown', () => this.selectColor(color, swatch));
            
            this.swatchContainer.addChild(swatch);
            
            // Store reference for border management
            swatch.colorValue = color;
            swatch.isSelected = false;
        });
    }

    createCustomColorPicker() {
        this.customPickerContainer = new PIXI.Container();
        this.container.addChild(this.customPickerContainer);

        // Custom color button (square)
        this.customButton = new PIXI.Graphics();
        this.customButton.beginFill(this.selectedColor);
        this.customButton.drawRect(0, 0, 22, 22);
        this.customButton.endFill();
        this.customButton.lineStyle(2, 0x000000, 1);
        this.customButton.drawRect(0, 0, 22, 22);
        this.customButton.x = this.colors.length * 22 + 16;
        this.customButton.y = 0;
        this.customButton.interactive = true;
        this.customButton.cursor = 'pointer';
        this.customButton.on('pointerdown', () => this.openCustomColorPicker());
        this.customPickerContainer.addChild(this.customButton);

        // Add 'custom' label
        this.customLabel = new PIXI.Text('custom', {
            fontFamily: 'Arial',
            fontSize: 9,
            fill: 0x333333
        });
        this.customLabel.anchor.set(0.5, 0);
        this.customLabel.x = this.customButton.x + 11;
        this.customLabel.y = this.customButton.y + 24;
        this.customPickerContainer.addChild(this.customLabel);
    }

    selectColor(color, swatch) {
        // Remove previous selection
        this.swatchContainer.children.forEach(child => {
            if (child.colorValue !== undefined) {
                child.isSelected = false;
                child.clear();
                child.beginFill(child.colorValue);
                child.drawRect(0, 0, 22, 22);
                child.endFill();
                child.lineStyle(2, 0xFFFFFF, 1);
                child.drawRect(0, 0, 22, 22);
            }
        });
        // Remove border from custom button, but do not change its fill color
        this.customButton.clear();
        this.customButton.beginFill(this.customColor !== undefined ? this.customColor : this.selectedColor);
        this.customButton.drawRect(0, 0, 22, 22);
        this.customButton.endFill();
        this.customButton.lineStyle(2, 0x000000, 1);
        this.customButton.drawRect(0, 0, 22, 22);
        // Add selection border to swatch
        swatch.isSelected = true;
        swatch.clear();
        swatch.beginFill(color);
        swatch.drawRect(0, 0, 22, 22);
        swatch.endFill();
        swatch.lineStyle(3, 0x000000, 1);
        swatch.drawRect(0, 0, 22, 22);
        this.selectedColor = color;
        this.onColorChange(color);
    }

    openCustomColorPicker() {
        // Create a simple color picker using HTML input
        const input = document.createElement('input');
        input.type = 'color';
        input.value = '#' + this.selectedColor.toString(16).padStart(6, '0');
        input.style.position = 'absolute';
        input.style.opacity = '0';
        input.style.pointerEvents = 'none';
        
        document.body.appendChild(input);
        
        input.addEventListener('change', (e) => {
            const hexColor = e.target.value;
            const color = parseInt(hexColor.replace('#', ''), 16);
            this.selectCustomColor(color);
            document.body.removeChild(input);
        });
        
        input.click();
    }

    selectCustomColor(color) {
        // Remove selection from all swatches
        this.swatchContainer.children.forEach(child => {
            if (child.colorValue !== undefined) {
                child.isSelected = false;
                child.clear();
                child.beginFill(child.colorValue);
                child.drawRect(0, 0, 22, 22);
                child.endFill();
                child.lineStyle(2, 0xFFFFFF, 1);
                child.drawRect(0, 0, 22, 22);
            }
        });
        // Add border to custom button and update its fill color
        this.customColor = color;
        this.customButton.clear();
        this.customButton.beginFill(color);
        this.customButton.drawRect(0, 0, 22, 22);
        this.customButton.endFill();
        this.customButton.lineStyle(3, 0x000000, 1);
        this.customButton.drawRect(0, 0, 22, 22);
        this.selectedColor = color;
        this.onColorChange(color);
    }

    hsvToHex(h, s, v) {
        const c = v * s / 100;
        const x = c * (1 - Math.abs((h / 60) % 2 - 1));
        const m = v - c;
        
        let r, g, b;
        if (h < 60) { r = c; g = x; b = 0; }
        else if (h < 120) { r = x; g = c; b = 0; }
        else if (h < 180) { r = 0; g = c; b = x; }
        else if (h < 240) { r = 0; g = x; b = c; }
        else if (h < 300) { r = x; g = 0; b = c; }
        else { r = c; g = 0; b = x; }
        
        const rHex = Math.round((r + m) * 255).toString(16).padStart(2, '0');
        const gHex = Math.round((g + m) * 255).toString(16).padStart(2, '0');
        const bHex = Math.round((b + m) * 255).toString(16).padStart(2, '0');
        
        return parseInt(rHex + gHex + bHex, 16);
    }

    getSelectedColor() {
        return this.selectedColor;
    }
}

window.ColorPicker = ColorPicker; 