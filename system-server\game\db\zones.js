const axios = require("axios"); 
const fetch = require('node-fetch');
const { app_url } = require("../../config");

async function unlockMapZone(row_id, status) {
	var url = `${app_url}/players/zones/ul/` + row_id;
	let change = {
		"status": status,
		"id": row_id
	}
	const options = {
		method: 'PUT',
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(change)
	}
	fetch(url, options).then(result => {
		console.log('ALERT! ZONE HAS BEEN UNLOCKED (row id ' + row_id + ')')
	})
}

async function isZonePaidFully() {
	axios.get(`${app_url}/players/zones`).then((response) => {
		var data = response.data;
		// find all LOCKED zones
		for (var m in data) {
			if (data[m].status == 'locked') {
				// is zone PAID or OVERPAID?
				if (Number(data[m].gxp_paid) >= Number(data[m].gxp_required)) {
					console.log('ALERT: ' + data[m].id + ' is fully paid, ready to UNLOCK! Paid: ' + data[m].gxp_paid + ' / ' + data[m].gxp_required + '.');
					unlockMapZone(data[m].id, 'unlocked');
				}
			}
		}
	}, (error) => {
		console.log(error);
	});
}


module.exports = {
  isZonePaidFully,
  unlockMapZone
};
