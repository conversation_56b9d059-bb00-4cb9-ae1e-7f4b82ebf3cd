async function deleteAdventureApi(adventure_id) {
  if (!adventure_id) {
    console.error('ERROR: deleteAdventureApi - adventure_id is undefined or null');
    return false;
  }

  const url = `${domain_url}/adventures/${adventure_id}`;

  try {
    const response = await axios.delete(url);
    return true; // Indicates success
  } catch (error) {
    console.error('ERROR: deleteAdventureApi - Request failed:', error.message);
    console.error('ERROR: deleteAdventureApi - Status code:', error.response?.status);
    console.error('ERROR: deleteAdventureApi - Response data:', error.response?.data);
    return false; // Indicates failure
  }
}

async function deleteTeamApi(teamid) {
  const url = `${domain_url}/teams/${teamid}`;
  // First check if team exists
  const team = myTeams.find(t => t.team_id === teamid);
  if (!team) {
    throw new Error('Team not found');
  }
  // Check for active adventures
  const hasActiveAdventure = playerAdventures.some(
    adv => adv.team_id === teamid && adv.status === 'In Progress'
  );
  if (hasActiveAdventure) {
    throw new Error('Cannot delete team with active adventures');
  }
  try {
    await axios.delete(url, {
      headers: {
        'wax-user-account': wax.userAccount,
        'Content-Type': 'application/json'
      }
    });
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}