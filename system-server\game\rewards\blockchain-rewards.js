const { app_url } = require("../../config"); 
if (process.env.NODE_ENV !== 'production') {
 require('dotenv').config();
}
const {
 Api,
 JsonRpc
} = require('eosjs');
const {
 JsSignatureProvider
} = require('eosjs/dist/eosjs-jssig');
const fetch = require('node-fetch');
const {
 TextDecoder,
 TextEncoder
} = require('util');
const axios = require("axios");
const privateKeys = [process.env.MYKEY];
const signatureProvider = new JsSignatureProvider(privateKeys);
const rpc = new JsonRpc("https://wax.cryptolions.io", {
 fetch
});
const api = new Api({
 rpc,
 signatureProvider,
 textDecoder: new TextDecoder(),
 textEncoder: new TextEncoder()
});
 
const nft_reward_memo = '🌟 Congratulations! You received an NFT reward from Cute Crushies.';
const dust_reward_memo = '🌟 Congratulations! You received a DUST reward from Cute Crushies.';
 
function precise(x, y = 4) {
 return Number.parseFloat(x).toFixed(y);
}
 
async function mintNFTReward(reward) {
  try {
    const result = await api.transact({
      actions: [{
        account: 'atomicassets',
        name: 'mintasset',
        authorization: [{
          actor: "crushinator1",
          permission: 'active',
        }],
        data: {
          authorized_minter: "crushinator1",
          collection_name: "cutecrushies",
          schema_name: reward.schema,
          template_id: reward.template_id,
          new_asset_owner: reward.wax_id,
          immutable_data: [],
          mutable_data: [],
          tokens_to_back: []
        }
      }]
    }, {
      blocksBehind: 3,
      expireSeconds: 120,
    });
    return true;
  } catch (error) {
    return false;
  }
}
 
async function transferDUSTReward(reward) {
  try {
    const transferAction = {
      account: 'niftywizards',
      name: 'transfer',
      authorization: [{
        actor: 'crushinator1',
        permission: 'active',
      }],
      data: {
        from: 'crushinator1',
        to: reward.wax_id,
        quantity: (precise(reward.amount) + " DUST").toString(),
        memo: dust_reward_memo
      }
    };
    const result = await api.transact({
      actions: [transferAction],
    }, {
      blocksBehind: 3,
      expireSeconds: 120,
    });
    return true;
  } catch (error) {
    return false;
  }
}
 
async function updatePlayerPoints(playerId, rewardType, rewardAmount) {
  try {
    const { getPlayerBalances } = require("../api/player");
    const playerBalances = await getPlayerBalances();
    const newBalance = Number(playerBalances[playerId][rewardType]) + Number(rewardAmount);

    const url = `${app_url}/players/${rewardType}/${playerId}`;
    const change = {
      [rewardType]: newBalance,
    };

    const config = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const response = await axios.put(url, change, config);
    return true;
  } catch (error) {
    return false;
  }
}

module.exports = {
  mintNFTReward,
  transferDUSTReward,
  updatePlayerPoints,
  precise
};