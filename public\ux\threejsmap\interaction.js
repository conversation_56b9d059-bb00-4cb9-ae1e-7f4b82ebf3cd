// Use window.playThreeJsMapTransitionEffect (no import)

function handleClick(event) {
  const { raycaster, mouse } = getRaycaster();
  const renderer = getRenderer();
  const camera = getCamera();
  const clickableTiles = getClickableTiles();
  
  const rect = renderer.domElement.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  raycaster.setFromCamera(mouse, camera);

  // First, check overlay sprites (e.g., claimables)
  if (typeof getOverlayDivs === 'function') {
    const overlaySprites = getOverlayDivs().filter(obj => obj.type === 'Sprite');
    if (overlaySprites.length > 0) {
      const overlayIntersects = raycaster.intersectObjects(overlaySprites);
      if (overlayIntersects.length > 0) {
        const sprite = overlayIntersects[0].object;
        if (sprite.userData && typeof sprite.userData.onClick === 'function') {
          sprite.userData.onClick(event);
          return; // Don't process tile click if overlay was clicked
        }
      }
    }
  }

  // Then, check tiles as before
  const intersects = raycaster.intersectObjects(clickableTiles);
  
  if (intersects.length > 0) {
    const obj = intersects[0].object;
    const { type, id, locked, zone, locale, hasAdventure } = obj.userData;
    
    if (type === 'world') {
      handleWorldClick(event, rect, obj, id);
    } else if (type === 'zone' && !locked) {
      handleZoneClick(id);
    } else if (type === 'zone' && locked) {
      handleLockedZoneClick(id, zone);
    } else if (type === 'locale') {
      handleLocaleClick(obj.userData);
    }
  }
}
 
function handleMouseMove(event) {
  const { raycaster, mouse } = getRaycaster();
  const renderer = getRenderer();
  const camera = getCamera();
  const clickableTiles = getClickableTiles();
  
  const rect = renderer.domElement.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(clickableTiles);
  
  // Reset all hover effects first
  clickableTiles.forEach(tile => {
    if (tile.material) {
      // Reset opacity
      if (tile.material.opacity !== undefined) {
        tile.material.opacity = 1.0;
      }
      // Reset color to original
      if (tile.userData.originalColor && tile.material.color) {
        tile.material.color.setHex(tile.userData.originalColor);
      }
      // Reset texture to original for locked zones
      if (tile.userData.type === 'zone' && tile.userData.locked && tile.userData.originalTexture) {
        tile.material.map = tile.userData.originalTexture;
        tile.material.needsUpdate = true;
      }
    }
  });
  
  if (intersects.length > 0) {
    const obj = intersects[0].object;
    
    // Add hover effect for locale tiles
    if (obj.userData.type === 'locale') {
      if (obj.material) {
        // Store original color if not already stored
        if (!obj.userData.originalColor && obj.material.color) {
          obj.userData.originalColor = obj.material.color.getHex();
        }
        // Remove transparency effect, instead increase brightness
        if (obj.material.color) {
          obj.material.color.multiplyScalar(1.2);
        }
      }
    }
    
    // Add hover effect for locked zone tiles
    if (obj.userData.type === 'zone' && obj.userData.locked) {
      if (obj.material && obj.material.map) {
        // Store original texture if not already stored
        if (!obj.userData.originalTexture) {
          obj.userData.originalTexture = obj.material.map;
        }
        
        // Use cached hover texture instead of loading it every time
        const hoverTexture = getCachedTexture(IMAGES.zoneLockedHover);
        if (hoverTexture) {
          hoverTexture.wrapS = THREE.RepeatWrapping;
          hoverTexture.wrapT = THREE.RepeatWrapping;
          hoverTexture.repeat.set(4, 4);
          obj.material.map = hoverTexture;
          obj.material.needsUpdate = true;
        }
      }
    }
    
    // Handle mini tooltip for locale tiles
    if (obj.userData.type === 'locale' && typeof handleMiniTooltipMouseEnter === 'function') {
      handleMiniTooltipMouseEnter(event, obj.userData);
    }
  } else {
    // Restore original colors when not hovering
    clickableTiles.forEach(tile => {
      if (tile.material && tile.userData.originalColor) {
        tile.material.color.setHex(tile.userData.originalColor);
      }
    });
    
    // Hide mini tooltip when not hovering
    if (typeof handleMiniTooltipMouseLeave === 'function') {
      handleMiniTooltipMouseLeave();
    }
  }
} 

function handleWorldClick(event, rect, obj, worldId) {
  // Check if click is on scroll icon area (8x8 pixels in bottom right corner)
  const clickX = event.clientX - rect.left;
  const clickY = event.clientY - rect.top;
  const worldX = rect.width / 2 + obj.position.x;
  const worldY = rect.height / 2 - obj.position.y;
  const size = GRID_CONFIG.worlds.size;
  const scrollSize = 8; // 8x8 pixel icon
  const scrollX = worldX + size / 2 - scrollSize - 5; // Bottom right corner
  const scrollY = worldY - size / 2 + 5; // Bottom right corner
  
  // Check if click is within scroll icon bounds
  if (clickX >= scrollX && clickX <= scrollX + scrollSize && 
      clickY >= scrollY && clickY <= scrollY + scrollSize) {
    handleWorldDescriptionClick(worldId);
  } else {
    handleWorldNavigationClick(worldId);
  }
}
 
function handleWorldDescriptionClick(worldId) {
  // Show world description modal
  if (typeof displayWorldDescriptionModal === 'function' && typeof world_descriptions !== 'undefined') {
    displayWorldDescriptionModal(world_descriptions, worldId);
  } else {
    // Fallback: show basic world info
    const worldName = WORLD_NAMES[worldId] || `World ${worldId + 1}`;
    alert(`${worldName}\n\nClick the world background to navigate to zones.`);
  }
}
 
function handleWorldNavigationClick(worldId) {
  var camera = getCamera();
  var scene = getScene();
  var clickableTiles = getClickableTiles();
  var mesh = null, fromMesh = null, toMesh = null;
  var targetView = 'zones';
  var transitionType = window.getMapTransitionType(nav.view, targetView);
  if (transitionType === 'zoom-in') {
    mesh = clickableTiles.find(function(m) { return m.userData.type === 'world' && m.userData.id === worldId; });
  } else if (transitionType === 'zoom-out-fade') {
    fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'world' && m.userData.id === nav.world; });
    toMesh = clickableTiles.find(function(m) { return m.userData.type === 'zone'; });
  }
  window.playThreeJsMapTransitionEffect({
    mesh: mesh,
    camera: camera,
    scene: scene,
    type: transitionType,
    fromMesh: fromMesh,
    toMesh: toMesh,
    fadeOnly: transitionType === 'fade-only',
    onComplete: function() {
      nav.view = 'zones';
      nav.world = worldId;
      nav.zone = 0;
      renderZones();
      updateThreeJsNavButtons('zone');
    }
  });
}
 
function handleZoneClick(zoneId) {
  var camera = getCamera();
  var scene = getScene();
  var clickableTiles = getClickableTiles();
  var mesh = null, fromMesh = null, toMesh = null;
  var targetView = 'locales';
  var transitionType = window.getMapTransitionType(nav.view, targetView);
  if (transitionType === 'zoom-in') {
    mesh = clickableTiles.find(function(m) { return m.userData.type === 'zone' && m.userData.id === zoneId; });
  } else if (transitionType === 'zoom-out-fade') {
    fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'zone' && m.userData.id === nav.zone; });
    toMesh = clickableTiles.find(function(m) { return m.userData.type === 'locale'; });
  }
  window.playThreeJsMapTransitionEffect({
    mesh: mesh,
    camera: camera,
    scene: scene,
    type: transitionType,
    fromMesh: fromMesh,
    toMesh: toMesh,
    fadeOnly: transitionType === 'fade-only',
    onComplete: function() {
      nav.view = 'locales';
      nav.zone = zoneId;
      renderLocales();
      updateThreeJsNavButtons('locale');
    }
  });
}
 
function handleLockedZoneClick(zoneId, zone) {
  // Set the navigation state for the modal
  if (typeof nav !== 'undefined') {
    nav.world = nav.world;
    nav.zone = zoneId;
  }
  
  // Store current zone data for the modal
  if (typeof currentzonesJson !== 'undefined') {
    currentzonesJson[zoneId] = zone;
  }
  
  // Show the GXP payment modal
  if (typeof displayPayZoneModal === 'function') {
    displayPayZoneModal();
  } else {
    console.log('Zone is locked, GXP payment modal not available');
    alert('This zone is locked. GXP payment functionality not available.');
  }
}
 
function handleLocaleClick(userData) {  
  // Skip tile click handling if this locale has a claimable
  // The claimable overlay will handle the click instead
  if (userData.hasClaimable && !userData.claimableState.found) {
    console.log('Claimable found! Skipping tile click - overlay will handle it.');
    return;
  } 
  if (typeof newDestination !== 'undefined') {
    newDestination.world = nav.world;
    newDestination.zone = nav.zone;
    newDestination.locale = userData.id;
  } 
  if (typeof nav !== 'undefined') {
    nav.locale = userData.id;
  } 
  const tileType = userData.locale?.Tile;
  const isTown = tileType === 'town' || tileType === 'ds_town';
  
  if (isTown) { 
    // Check if player has a team selected for adventure
    if (typeof teamSelectedForAdventure !== 'undefined' && teamSelectedForAdventure && 
        typeof enableSetAdventure !== 'undefined' && enableSetAdventure) {
      console.log('Team selected for adventure, not showing shop');
      return;
    }
    
    // Show shop modal for town
    const townId = userData.id.toString();
    const townName = userData.locale?.Locale_Name || `Town ${userData.id + 1}`;
    
    if (typeof showShopModal === 'function') {
      showShopModal(townId, townName);
    } else {
      console.error('showShopModal function not found');
      // Fallback: show basic town info
      if (typeof showAlert === 'function') {
        showAlert(`${townName}\nSquare #${userData.id + 1}\nType: Town`);
      }
    }
    return;
  }
  
  // Check if adventure setting is enabled and show modal
  if (typeof enableSetAdventure !== 'undefined' && enableSetAdventure && 
      typeof teamSelectedForAdventure !== 'undefined' && teamSelectedForAdventure) {
    
    // Get team vehicle info
    let team_vehicle = null;
    if (typeof findTeamData === 'function' && typeof myTeams !== 'undefined') {
      team_vehicle = findTeamData(myTeams, teamSelectedForAdventure, 'vehicles', 0);
    }
    
    // Check if player has enough nectar
    if (typeof playerData !== 'undefined' && playerData.nectar < 1) {
      if (typeof showAlert === 'function') {
        showAlert('Not enough nectar to start adventure');
      }
      return;
    }
    
    // Check vehicle compatibility
    if (typeof isVehicleTypeAllowed === 'function' && team_vehicle && userData.locale) {
      const terrain = userData.locale.Terrain || 'land';
      if (!isVehicleTypeAllowed(team_vehicle, terrain, showAlert)) {
        return;
      }
    }
    
    // Show adventure confirmation modal
    const title = "Start New Adventure";
    const localeName = userData.locale?.Locale_Name || `Tile ${userData.id + 1}`;
    const body = `Send Team #${teamSelectedForAdventure} to \n${localeName} (${nav.world + 1}, ${nav.zone + 1}, ${userData.id + 1})?`;
    
    if (typeof displaySetLocationModal === 'function') {
      displaySetLocationModal(title, body);
    } else {
      // Fallback: show basic confirmation
      if (confirm(body)) {
        if (typeof addAdventure === 'function') {
          addAdventure(teamSelectedForAdventure);
        }
      }
    }
  } else {
    // Show info about the locale if no adventure is being set
    const localeName = userData.locale?.Locale_Name || `Tile ${userData.id + 1}`;
    const terrain = userData.locale?.Terrain || 'Land';
    const capitalizedTerrain = terrain.charAt(0).toUpperCase() + terrain.slice(1);
    
    if (typeof showAlert === 'function') {
      showAlert(`${localeName}\nSquare #${userData.id + 1}\nType: ${capitalizedTerrain}`);
    }
  }
}
 
async function handleClaimableClick(userData) {
  try {
    // Prevent double claim
    if (userData.claimableState.found) {
      console.log('Claimable already found, ignoring click');
      return;
    }
    
    // Add GXP reward
    if (typeof transactResource === 'function') {
      await transactResource('gxp', 10, 'add', showAlert, updatePlayerBalances);
    } else {
      console.warn('transactResource function not available');
      // Fallback: just show alert
      if (typeof showAlert === 'function') {
        showAlert("You found 10 GXP!");
      }
    }
    
    // Mark claimable as found
    if (window.claimable && typeof window.claimable.setClaimableFound === 'function') {
      window.claimable.setClaimableFound(nav.world, nav.zone);
    }
    
    // Show success message
    if (typeof showAlert === 'function') {
      showAlert("You found 10 GXP! There are no more claimables for you today.");
    }
    
    // Refresh the locale view to remove the claimable icon
    if (typeof renderLocales === 'function') {
      await renderLocales();
    }
    
  } catch (error) {
    console.error('Error handling claimable click:', error);
    if (typeof showAlert === 'function') {
      showAlert('Error claiming reward. Please try again.');
    }
  }
} 
function setupClickHandler() {
  const renderer = getRenderer();
  renderer.domElement.addEventListener('click', handleClick);
}
 
function setupTooltipHandlers() {
  const renderer = getRenderer();
  renderer.domElement.addEventListener('mousemove', handleMouseMove);
  renderer.domElement.addEventListener('mouseleave', () => {
    if (typeof handleTooltipMouseLeave === 'function') {
      handleTooltipMouseLeave();
    }
  });
} 