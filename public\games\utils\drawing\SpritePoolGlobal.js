// SpritePoolGlobal.js - Global sprite pool for PIXI
(function(){
    function SpritePoolGlobal() {
        this.sprites = [];
        this.index = 0;
    }
    SpritePoolGlobal.prototype.get = function() {
        if(this.index < this.sprites.length) {
            return this.sprites[this.index++];
        }
        var sprite = new PIXI.Sprite();
        sprite.anchor.set(0.5);
        this.sprites.push(sprite);
        return sprite;
    };
    SpritePoolGlobal.prototype.reset = function() {
        this.index = 0;
    };
    SpritePoolGlobal.prototype.destroy = function() {
        for(var i = 0; i < this.sprites.length; i++)
            this.sprites[i].destroy();
    };
    window.SpritePoolGlobal = SpritePoolGlobal;
})(); 