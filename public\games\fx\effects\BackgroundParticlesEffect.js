// Background Particles Effect
class BackgroundParticlesEffect {
    constructor(visualEffects) {
        this.visualEffects = visualEffects;
    }

    // Create background particles
    create(stage, count = 20) {
        const particles = [];
        
        for (let i = 0; i < count; i++) {
            const particle = this.visualEffects.getParticle();
            particle.beginFill(0xFFFFFF, 0.3);
            particle.drawCircle(0, 0, Math.random() * 2 + 1);
            particle.endFill();
            
            particle.x = Math.random() * stage.width;
            particle.y = Math.random() * stage.height;
            
            stage.addChild(particle);
            particles.push(particle);
        }
        
        // Animate background particles
        const animate = () => {
            particles.forEach(particle => {
                particle.y += 0.5;
                
                if (particle.y > stage.height) {
                    particle.y = -10;
                    particle.x = Math.random() * stage.width;
                }
            });
            
            requestAnimationFrame(animate);
        };
        
        animate();
        
        return particles;
    }
} 