var active_mini_games = [1, 2];
var time = 0;
var score = 0;
var level = 0;
var gameInterval;
// Cache DOM elements
var timeElement, levelElement, scoreElement, endTimeElement, endLevelElement, endScoreElement, resultMessage;

document.addEventListener('DOMContentLoaded', function() {
    timeElement = document.getElementById('game-timer');
    levelElement = document.getElementById('game-level');
    scoreElement = document.getElementById('game-score');
    endTimeElement = document.getElementById('end-time');
    endLevelElement = document.getElementById('end-level');
    endScoreElement = document.getElementById('end-score');
    resultMessage = document.getElementById('result-message');
});

function createGameButtons() {
    const gamesContainer = document.getElementById('game-buttons');
    gamesContainer.innerHTML = '';

    active_mini_games.forEach(gameId => {
        const game = mini_games.find(game => game.id === gameId);
        if (game) {
            const button = document.createElement('button');
            button.setAttribute('type', 'button');
            button.setAttribute('class', 'btn-secondary flex-fill m-1');
            button.setAttribute('data-value', game.id);

            const icon = document.createElement('span');
            icon.className = `icon game-icon`;
            button.appendChild(icon);

            button.appendChild(document.createTextNode(game.name));

            button.addEventListener('click', () => {
                if (playerData.credits >= 1) {
                    displayGameModal(game.id);
                } else {
                    alert("Not enough credits to play (requires 1 credit).");
                }
            });
            gamesContainer.appendChild(button);
        }
    });
}

function displayGameModal(gameId) {
    const game = mini_games.find(game => game.id === gameId);
    if (!game) {
        alert('Game not found!');
        return;
    }
    if (window.currentGame) {
        window.currentGame.stopGame();
        window.currentGame = null;
    }

    // Get DOM elements
    const gameCanvas = document.getElementById('game-canvas');
    if (gameCanvas) {
        gameCanvas.innerHTML = '';
    }

    const gameModal = document.getElementById('game-modal');
    if (!gameModal) {
        return; // Exit if modal not found
    }

    const startScreen = document.getElementById('start-screen');
    const gameOverScreen = document.getElementById('game-over-screen');

    // Check if title elements exist before setting content
    const modalTitle = document.getElementById('game-modal-title');
    if (modalTitle) {
        modalTitle.textContent = game.name;
    }

    const startTitle = document.getElementById('start-title');
    if (startTitle) {
        startTitle.textContent = game.name;
    }

    // Update game instructions with the specific instructions for this game
    const startScreenInstructions = document.querySelector('#start-screen p');
    const gameInstructions = document.getElementById('game-instructions');

    if (startScreenInstructions && game.instructions) {
        startScreenInstructions.textContent = game.instructions;
    }

    if (gameInstructions && game.instructions) {
        // Keep the info icon but update the text
        const infoIcon = gameInstructions.querySelector('img');
        gameInstructions.innerHTML = '';
        if (infoIcon) {
            gameInstructions.appendChild(infoIcon);
        }
        gameInstructions.appendChild(document.createTextNode(' ' + game.instructions));
    } 
    gameModal.style.display = "block";
    if (startScreen) {
        startScreen.style.display = "flex";
    }
    if (gameOverScreen) {
        gameOverScreen.style.display = "none";
    }  
    if (typeof PIXI === 'undefined') {
        alert('Game engine not loaded properly. Please refresh the page and try again.');
        return;
    } 
    document.getElementById('game-canvas'); 
    try {
        switch (game.name) {
            case "Card Match":
                window.currentGame = new CardMatch();
                break;
            case "Treasure Frenzy":
                window.currentGame = new TreasureFrenzy();
                break;
            default:
                alert('Game not implemented yet!');
                return;
        }
    } catch (error) {
        alert('Failed to initialize game. Please try again.');
        return;
    } 
    const startButton = document.getElementById('start-button');
    const playAgainButton = document.getElementById('play-again-button');
    const mainMenuButton = document.getElementById('main-menu-button');

    startButton.onclick = () => window.currentGame.startGame();
    playAgainButton.onclick = () => window.currentGame.startGame();
    mainMenuButton.onclick = () => {
        document.getElementById('game-over-screen').style.display = 'none';
        document.getElementById('start-screen').style.display = 'flex';
    };
    const exitButton = document.getElementById('game-modal-cancel-button');
    if (exitButton) {
        exitButton.textContent = "Exit Game";
        exitButton.onclick = () => {
            if (window.currentGame) {
                window.currentGame.stopGame();
                window.currentGame = null;
            }
            gameModal.style.display = "none";
        };
    } else { 
        const gameModalHeader = document.querySelector('#game-modal .modal-header');
        if (gameModalHeader) {
            const fallbackExitButton = document.createElement('button');
            fallbackExitButton.textContent = "Exit Game";
            fallbackExitButton.className = "btn-secondary";
            fallbackExitButton.onclick = () => {
                if (window.currentGame) {
                    window.currentGame.stopGame();
                    window.currentGame = null;
                }
                gameModal.style.display = "none";
            };
            gameModalHeader.appendChild(fallbackExitButton);
        }
    }
}

function initGame(){
  clearInterval(gameInterval);
  gameInterval = null;
  time = 0;
  score = 0;
  level = 0;
} 

function stopGame() {
    initGame();
    updateHUD();
    document.getElementById('start-screen').style.display = 'flex';
    document.getElementById('game-modal').style.display = "none";
}

function endGame(result) {
    clearInterval(gameInterval);
    endTimeElement.textContent = time;
    endLevelElement.textContent = level;
    endScoreElement.textContent = score;
    resultMessage.textContent = result;
    document.getElementById('game-over-screen').style.display = 'flex';
}

function updateHUD() {
    timeElement.textContent = time;
    levelElement.textContent = level;
    scoreElement.textContent = score;
}

class BaseGame {
    constructor() {
        this.time = 0;
        this.score = 0;
        this.level = 1;
        this.gameInterval = null;

        try {
            this.app = new PIXI.Application({
                width: 500,
                height: 500,  // Reduced height to account for HUD
                backgroundColor: 0x1099bb,
                resolution: window.devicePixelRatio || 1,
                antialias: true
            });

            const gameCanvas = document.getElementById('game-canvas');

            if (gameCanvas) {
                gameCanvas.appendChild(this.app.view);

                // Force canvas to be visible
                const canvasElement = this.app.view;
                canvasElement.style.display = 'block';
                canvasElement.style.visibility = 'visible';
                canvasElement.style.opacity = '1';
            }
        } catch (error) {
            // Handle error silently
        }
    }

    async startGame() {
        // First check if player has enough credits
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }

        try {
            // Attempt to subtract 1 credit from the player's account
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);

            // Continue with game initialization after successful transaction
            this.initGame();
            document.getElementById('start-screen').style.display = 'none';
            document.getElementById('game-area').style.display = 'block';
            document.getElementById('game-over-screen').style.display = 'none';
        } catch (error) {
            console.error('ERROR: Failed to start game:', error);
            showAlert("Failed to start game. Please try again.");
        }
    }

    initGame() {
        clearInterval(this.gameInterval);
        this.gameInterval = null;
        this.time = 0;
        this.score = 0;
        this.level = 1;

        this.app.stage.removeChildren();
    }

    updateHUD() {
        document.getElementById('game-timer').textContent = this.time;
        document.getElementById('game-level').textContent = this.level;
        document.getElementById('game-score').textContent = this.score;
    }

    endGame(result) {
        clearInterval(this.gameInterval);
        document.getElementById('end-time').textContent = this.time;
        document.getElementById('end-level').textContent = this.level;
        document.getElementById('end-score').textContent = this.score;
        document.getElementById('result-message').textContent = result;
        document.getElementById('game-over-screen').style.display = 'flex';
        document.getElementById('game-area').style.display = 'none';
    }

    stopGame() {
        // Clear the game interval
        clearInterval(this.gameInterval);
        this.gameInterval = null;

        // Reset game state
        this.time = 0;
        this.score = 0;
        this.level = 1;

        // Clear the stage
        this.app.stage.removeChildren();

        // Update HUD
        this.updateHUD();

        // Reset display states
        document.getElementById('start-screen').style.display = 'flex';
        document.getElementById('game-area').style.display = 'none';
        document.getElementById('game-over-screen').style.display = 'none';

        // Hide the modal
        document.getElementById('game-modal').style.display = "none";
    }
}

// minor to dos
// [ ] add more pictures for the cards at levels above level 5, and even more variety of cards at level 10 and above
// [ ] we want a congrats graphic that says! Great! when you finish a level
// [ ] reward gxp for completion of levels , use credits for each play through