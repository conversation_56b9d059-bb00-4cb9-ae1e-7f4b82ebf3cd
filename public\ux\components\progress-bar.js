function createProgressBar(event, current, total) {
  let text = '';
  let progressBarClass = '';
  if (event === 'In Progress') {
    text = "Adventuring";
    progressBarClass = 'progress-bar-green bottom-0 start-0';
  } else if (event === 'Napping') {
    text = "Napping";
    progressBarClass = 'progress-bar-purple bottom-0 start-0';
  } else if (event === 'Ready') {
    text = "Ready";
    progressBarClass = 'progress-bar-blue bottom-0 start-0';
  } else if (event === 'All Adventures') {
    text = "All Adventures";
    progressBarClass = 'progress-bar-gold';
  }

  const progressBarContainer = document.createElement("div");
  progressBarContainer.classList.add("progress");

  const progressBar = document.createElement("div");
  progressBar.classList.add("progress-bar", ...progressBarClass.split(' ')); // Fixed here
  progressBar.setAttribute('role', 'progressbar');
  progressBar.setAttribute('aria-valuemin', '0');
  progressBar.setAttribute('aria-valuemax', '100');

  const progress = Math.round((current / total) * 100);
  progressBar.setAttribute('aria-valuenow', progress);
  progressBar.style.width = progress + '%';
  progressBar.innerHTML = progress + '% ' + text;

  progressBarContainer.appendChild(progressBar);
  return progressBarContainer;
}

async function updateTotalProgressBar(playerAdventuresData) {
  const activeAdventures = playerAdventuresData.filter(adventure => adventure.status === 'In Progress');
  if (activeAdventures.length === 0) {
  return;
  }
  const progressBarContainer = document.getElementById("total_progress_bar");
  progressBarContainer.innerHTML = '';
  if (activeAdventures.length === 0) {
    return;
  }
  let total_current_steps = 0;
  let total_init_steps = 0;
  activeAdventures.forEach(adventure => {
    total_current_steps += Number(adventure.current_steps);
    total_init_steps += Number(adventure.init_steps);
  });
  const progressBar = createProgressBar('All Adventures', total_current_steps, total_init_steps);
  progressBarContainer.appendChild(progressBar);
}
