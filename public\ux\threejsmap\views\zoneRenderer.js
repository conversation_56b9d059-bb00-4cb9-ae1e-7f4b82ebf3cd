// Zone view renderer for Three.js map navigation

/**
 * Render the zones view in a 4x4 grid
 */
async function renderZones() {
  resetCameraForView('zones');
  if (typeof disposeCloudLayer === 'function') disposeCloudLayer();
  clearScene();
  const { size, gap, gridSize } = GRID_CONFIG.zones;
  
  // Get zones for this world
  const worldZones = getWorldZones(nav.world);
  console.log('Zones for world', nav.world, ':', worldZones);
  
  for (let row = 0; row < gridSize; row++) {
    for (let col = 0; col < gridSize; col++) {
      // Fix zone order to match old world navigation: 1-4 top row, 5-8 second row, 9-12 third row, 13-16 bottom row
      const idx = row * gridSize + col;
      const x = (col - 1.5) * (size + gap);
      const y = (row - 1.5) * (size + gap);
      
      // Find zone data
      const zone = worldZones.find(z => z.mapgrid_16 === idx);
      const locked = !zone || zone.status === "locked";
      
      // Use appropriate texture based on lock status
      const texturePath = locked ? IMAGES.zoneLocked : IMAGES.zoneUnlocked[nav.world];
      
      // Create mesh with proper error handling and tiling
      try {
        await createClickableMeshWithTiling(texturePath, x, y, size, 10, { 
          type: 'zone', 
          id: zone ? zone.mapgrid_16 : idx, 
          locked, 
          zone 
        });
      } catch (error) {
        console.warn(`Failed to load zone image for zone ${idx}:`, error);
        // Fallback to colored cube
        const fallbackColor = locked ? 0x666666 : 0x4CAF50;
        createFallbackMesh(x, y, size, 10, { 
          type: 'zone', 
          id: zone ? zone.mapgrid_16 : idx, 
          locked, 
          zone 
        }, fallbackColor);
      }
      
      if (locked) {
        // Zone name for locked zones (centered)
        const zoneName = zone?.zone_name || `Zone ${idx + 1}`;
        
        // Lock icon positioned in front of the zone title
        const lockSize = 8;
        const lockX = x - size / 2 + 10; // Position lock icon on the left side
        const lockY = y - 2; // Align with the zone title
        addOverlayImage(IMAGES.lock, lockX, lockY, lockSize, 12);
        
        // Zone title (centered)
        addLabel(zoneName, x, y, size, '#fff', 12);
        
        // GXP progress bar and amount as a label
        if (zone) {
          const gxpPaid = zone.gxp_paid || 0;
          const gxpRequired = zone.gxp_required || 1000000;
          const progressPercent = Math.min((gxpPaid / gxpRequired) * 100, 100);
          // Format GXP amounts
          const formatGXP = (amount) => {
            if (amount >= 1000000) {
              return (amount / 1000000).toFixed(1) + 'M';
            } else if (amount >= 1000) {
              return (amount / 1000).toFixed(1) + 'k';
            }
            return amount.toString();
          };
          // --- Add white progress bar ---
          // Bar width/height relative to zone size
          const barWidth = size * 0.7;
          const barHeight = 8;
          const barX = x;
          const barY = y + 16; // just below the title
          addProgressBar(barX, barY, barWidth, barHeight, progressPercent);
          // GXP amount text (below the bar)
          const gxpText = `${formatGXP(gxpPaid)} / ${formatGXP(gxpRequired)} GXP`;
          addLabel(gxpText, x, barY + barHeight + 8, size * 0.8, '#0ff', 8, {
            background: 'rgba(0,0,0,0.7)',
            borderRadius: '4px',
            padding: '2px 6px'
          });
          // Lock icon in front of the GXP label (to the left)
          const lockSize = 12;
          const lockX = x - (size * 0.8) / 2 + lockSize / 2; // left edge of label + half icon width
          const lockY = barY + barHeight + 8; // same y as GXP label
          addOverlayImage(IMAGES.lock, lockX, lockY, lockSize, 13);
        }
      } else {
        // Add team counts for unlocked zones
        const teamCounts = getZoneTeamCounts(nav.world, idx);
        if (teamCounts.player > 0 || teamCounts.other > 0) {
          let teamText = '';
          if (teamCounts.player > 0) teamText += `P:${teamCounts.player}`;
          if (teamCounts.other > 0) teamText += ` O:${teamCounts.other}`;
          // Place at bottom center, just above the bottom edge, styled like GXP label
          addLabel(teamText, x, y + size / 2 - 12, size * 0.8, '#fff', 8, {
            background: 'rgba(0,0,0,0.7)',
            borderRadius: '4px',
            padding: '2px 6px'
          });
        }
        
        // Add overlay for unlocked zones
        const isCurrentZone = nav.zone === idx;
        const overlayPath = isCurrentZone ? IMAGES.overlaySelect : IMAGES.overlayBorder;
        addOverlayImage(overlayPath, x, y, size, 15);
      }
      
      // Zone name - centered vertically and horizontally in the zone square (for unlocked zones)
      const label = locked ? "" : (zone?.zone_name || `Zone ${idx + 1}`);
      if (label) {
        addLabel(label, x, y, size, '#fff', 12);
      }
    }
  }
  
  // Update navigation buttons to reflect current view
  if (typeof updateThreeJsNavButtons === 'function') {
    updateThreeJsNavButtons('zone');
  }
  if (typeof addCanvasNavButtons === 'function') {
    addCanvasNavButtons();
  }
} 