const pool = require("../db");
const levels = require("../../logic/levels");
const queries = require("../queries");
const { assignReward } = require("../rewards/rewards");
 
const updatePlayerLevel = (req, res) => {
  const id = req.params.id;
  console.log("The User to get level up: " + id);
  const { xp } = req.body;
  const new_level = levels.getPlayerLevelByXP(xp);
  console.log("The New Player Level will be: " + new_level + " - Player XP: " + xp + ", " + id);

  pool.query(queries.up.updatePlayerLevel, [new_level, id], (updateError, updateResults) => {
    if (updateError) {
      res.status(500).send("Error updating player level.");
    } else {
      res.status(200).send("PLAYER LV INCREASED: Player level updated successfully.");
    }
  });
}
 
const updatePlayerXP = async (req, res) => {
  try {
    const id = req.params.id;
    const { xp } = req.body;
    const playerDataResults = await pool.query(queries.getby.getPlayerById, [id]);
    const playerData = playerDataResults.rows[0];
    var currentLevel = playerData.lv;
    console.log('PlayerData results:', playerDataResults);
    console.log('Current level of player is ' + currentLevel);

    // Calculate the new level based on the updated XP
    const newLevel = levels.getPlayerLevelByXP(xp);
    console.log('New level of player is ' + newLevel);

    // Update both XP and level in the database
    const updatedXPandLevelResult =  await pool.query(queries.up.updatePlayerXPAndLevel, [xp, newLevel, id]);
    console.log(updatedXPandLevelResult);

    if (isNaN(currentLevel)) {
      console.error(`Invalid current level for player ${id}:`, playerData.level);
      throw new Error("Invalid current level");
    }

    // Log the current and new levels for debugging
    console.log(`Player ${id} - Current Level: ${currentLevel}, New Level: ${newLevel}, XP: ${xp}`);

    // If the player leveled up, apply rewards for each level gained
    if (newLevel > currentLevel) {
      const levelsGained = newLevel - currentLevel;

      // Get the player's settings to include in the log
      const playerSettingsResult = await pool.query(queries.getby.getPlayerSettingsById, [id]);
      let playerSettings = {};
      if (playerSettingsResult && playerSettingsResult.rows.length > 0) {
        playerSettings = playerSettingsResult.rows[0].data || {};
      }

      for (let i = 1; i <= levelsGained; i++) {
        const nextLevel = Number(currentLevel + i);
        const reward = levels.getRewardForLevel(nextLevel);
        console.log(`Applying rewards for level ${nextLevel}:`, reward);
        const updatingPlayerStatResults = await updatePlayerStats(id, reward);
        console.log("Player stat update result: " + updatingPlayerStatResults);
        const assignRewardForLevelupResult = await assignReward(id, reward, nextLevel);
        console.log("Assigned rewards for level up: " + assignRewardForLevelupResult);

        // Create a log entry for the level up with the updated stats
        const updatedSettings = updatingPlayerStatResults ? updatingPlayerStatResults.data : playerSettings;

        // Calculate the changes in stats
        const changes = {};
        if (reward.nectar_max !== undefined) {
          const prevValue = playerSettings.nectar_max || 0;
          changes.nectar_max = `${reward.nectar_max}(+${reward.nectar_max - prevValue})`;
        }
        if (reward.credits_max !== undefined) {
          const prevValue = playerSettings.credits_max || 0;
          changes.credits_max = `${reward.credits_max}(+${reward.credits_max - prevValue})`;
        }
        if (reward.adventure_gxp_bonus !== undefined) {
          const prevValue = playerSettings.adventure_gxp_bonus || 0;
          const diff = reward.adventure_gxp_bonus - prevValue;
          changes.adventure_gxp_bonus = diff === 0 ?
            `${reward.adventure_gxp_bonus}(no change)` :
            `${reward.adventure_gxp_bonus}(+${diff})`;
        }
        if (reward.nap_rate_bonus !== undefined) {
          const prevValue = playerSettings.nap_rate_bonus || 0;
          const diff = reward.nap_rate_bonus - prevValue;
          changes.nap_rate_bonus = diff === 0 ?
            `${reward.nap_rate_bonus}(no change)` :
            `${reward.nap_rate_bonus}(+${diff})`;
        }

        const logData = {
          desc: `You leveled up to level ${nextLevel}!`,
          level: nextLevel,
          changes: changes
        };

        // Add the log entry
        pool.query(queries.add.addGameLog, [id, "new", "level_up", JSON.stringify(logData)], (error, results) => {
          if (error) {
            console.error("Error adding level up log:", error);
          } else {
            console.log("Game log entry added for level up.");
          }
        });

        // Update playerSettings for the next iteration
        playerSettings = updatedSettings;
      }

      return res.status(200).json({
        message: "Level up!",
        newLevel,
        success: true
      });
    }
    // If no level up, just return success
    return res.status(200).json({
      message: "XP updated",
      success: true
    });

  } catch (error) {
    console.error("Error updating player XP:", error);
    return res.status(500).json({
      message: error.message || "Internal Server Error",
      success: false
    });
  }
};
 
async function updatePlayerStats(id, reward) {
  let playerSettingData = {};

  try {
    console.log(`Starting updatePlayerStats for player ${id} with reward:`, reward);

    // Fetch current player stats from the database
    const currentStatsQuery = 'SELECT data FROM player_settings WHERE wax_id = $1';
    const currentStats = await pool.query(currentStatsQuery, [id]);
    let currentData = {};

    // Initialize currentData if stats exist
    if (currentStats && Array.isArray(currentStats.rows) && currentStats.rows.length > 0) {
      currentData = currentStats.rows[0].data || {};
    }

    console.log(`Current player stats for player ${id}:`, currentData);

    // Process new rewards
    for (const key in reward) {
      console.log(`Processing reward key: ${key}, value: ${reward[key]}`);
      switch (key) {
        case 'nectar_max':
          playerSettingData.nectar_max = reward[key];
          console.log(`Updated nectar_max to: ${reward[key]}`);
          break;
        case 'credits_max':
          playerSettingData.credits_max = reward[key];
          console.log(`Updated credits_max to: ${reward[key]}`);
          break;
        case 'adventure_gxp_bonus':
          playerSettingData.adventure_gxp_bonus = reward[key];
          console.log(`Updated adventure_gxp_bonus to: ${reward[key]}`);
          break;
        case 'nap_rate_bonus':
          playerSettingData.nap_rate_bonus = reward[key];
          console.log(`Updated nap_rate_bonus to: ${reward[key]}`);
          break;
        default:
          console.log(`Skipping unknown reward key: ${key}`);
          break;
      }
    }

    console.log(`Player setting data to be updated:`, playerSettingData);

    // Update the database only if there are changes
    if (Object.keys(playerSettingData).length > 0) {
      const updateQuery = `
        UPDATE player_settings
        SET data = data || $1::jsonb
        WHERE wax_id = $2
        RETURNING data;
      `;

      console.log(`Executing update query:`, updateQuery, `with data:`, playerSettingData);

      // Execute the update query
      const result = await pool.query(updateQuery, [playerSettingData, id]);
      console.log("Player stats updated successfully:", result.rows[0]);
      return result.rows[0];
    } else {
      console.log("No changes to player stats. Skipping database update.");
    }

    // Return current data if no changes were made
    return currentData;
  } catch (error) {
    console.error("Error updating player stats:", error);
    throw error;
  }
}

module.exports = {
  updatePlayerLevel,
  updatePlayerXP,
  updatePlayerStats
};
