class LaserDefenderEnemyManager {
    constructor(game) {
        this.game = game;
        this.enemies = [];
        this.enemyBodies = [];
        this.spawnTimer = 0;
        this.asteroidSpawnTimer = 0;
        this.npcSpawnTimer = 0;
    }

    updateEnemies() {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            const body = this.enemyBodies[i];
            
            if (body.position.x < 30) {
                Matter.Body.setPosition(body, { x: 30, y: body.position.y });
                Matter.Body.setVelocity(body, { x: Math.abs(body.velocity.x), y: body.velocity.y });
            } else if (body.position.x > this.game.app.screen.width - 30) {
                Matter.Body.setPosition(body, { x: this.game.app.screen.width - 30, y: body.position.y });
                Matter.Body.setVelocity(body, { x: -Math.abs(body.velocity.x), y: body.velocity.y });
            }
            
            if (enemy.type === 'shooter' || enemy.type === 'shielded' || enemy.type === 'boss') {
                const dx = this.game.turretManager.turretX - enemy.x;
                const dy = this.game.turretManager.turretY - enemy.y;
                const angle = Math.atan2(dy, dx);
                enemy.rotation = angle - Math.PI / 2;
            }
            
            if (enemy.type === 'shooter' && Math.random() < 0.005) {
                this.game.projectileManager.createEnemyProjectile(enemy);
            }
            
            if (body.position.y > this.game.app.screen.height + 50) {
                this.game.app.stage.removeChild(enemy);
                this.enemies.splice(i, 1);
                Matter.World.remove(this.game.physicsManager.world, body);
                this.enemyBodies.splice(i, 1);
            }
        }
    }

    spawnEnemies() {
        const config = this.game.getLevelConfig();
        const maxEnemies = config.enemyCount * 10;
        this.asteroidSpawnTimer += 16;
        this.npcSpawnTimer += 16;
        if (this.asteroidSpawnTimer >= (config.spawnRate / 10 + Math.random() * config.spawnRate * 0.05) && this.enemies.length < maxEnemies) {
            const asteroidsToSpawn = Math.min(1 + Math.floor(Math.random() * 2), maxEnemies - this.enemies.length); // 1 or 2
            for (let i = 0; i < asteroidsToSpawn; i++) {
                const asteroidSpeed = config.enemySpeed * (0.6 + Math.random() * 0.3);
                this.spawnEnemyOfType('asteroid', asteroidSpeed);
            }
            // Stagger the next spawn by randomizing the timer reset
            this.asteroidSpawnTimer = Math.random() * config.spawnRate * 0.6;
        }
        if ((config.hasShooters || config.hasShielded || config.hasBoss) && this.npcSpawnTimer >= (config.spawnRate * 2 / 10 + Math.random() * config.spawnRate * 0.1) && this.enemies.length < maxEnemies) {
            const npcTypes = [];
            if (config.hasShooters) npcTypes.push('shooter');
            if (config.hasShielded) npcTypes.push('shielded');
            if (config.hasBoss && Math.random() < 0.1) npcTypes.push('boss');
            if (npcTypes.length > 0) {
                if (Math.random() < 0.3 && maxEnemies - this.enemies.length >= 3) {
                    const baseY = -50;
                    const baseX = Math.random() * (this.game.app.screen.width - 120) + 60;
                    const formationType = npcTypes[Math.floor(Math.random() * npcTypes.length)];
                    const baseSpeed = config.enemySpeed * (0.9 + Math.random() * 0.2);
                    for (let i = 0; i < 3; i++) {
                        this.spawnEnemyOfType(formationType, baseSpeed, baseX + i * 40, baseY);
                    }
                } else {
                    const npcsToSpawn = Math.min(2, maxEnemies - this.enemies.length);
                    for (let i = 0; i < npcsToSpawn; i++) {
                        const type = npcTypes[Math.floor(Math.random() * npcTypes.length)];
                        const shipSpeed = config.enemySpeed * (1.0 + Math.random() * 0.4);
                        this.spawnEnemyOfType(type, shipSpeed);
                    }
                }
            }
            this.npcSpawnTimer = 0;
        }
    }

    spawnEnemyOfType(type, speed, x = null, y = null) {
        const config = this.game.getLevelConfig();
        const enemyData = this.createEnemy(type, speed, x, y);
        this.game.app.stage.addChild(enemyData.sprite);
        this.enemies.push(enemyData.sprite);
        this.enemyBodies.push(enemyData.body);
    }

    createEnemy(type, speed, x = null, y = null) {
        let enemy;
        let body;
        let spawnX, spawnY;
        // Prevent overlapping spawns for asteroids
        if (type === 'asteroid') {
            let attempts = 0;
            let overlap;
            do {
                spawnX = x !== null ? x : Math.random() * (this.game.app.screen.width - 60) + 30;
                spawnY = y !== null ? y : -50;
                overlap = false;
                for (let i = 0; i < this.enemyBodies.length; i++) {
                    const other = this.enemyBodies[i];
                    if (other && other.label === 'asteroid') {
                        const dx = other.position.x - spawnX;
                        const dy = other.position.y - spawnY;
                        const dist = Math.sqrt(dx * dx + dy * dy);
                        if (dist < 40) { // 40px is max asteroid size
                            overlap = true;
                            break;
                        }
                    }
                }
                attempts++;
            } while (overlap && attempts < 10);
        } else {
            spawnX = x !== null ? x : Math.random() * (this.game.app.screen.width - 60) + 30;
            spawnY = y !== null ? y : -50;
        }
        switch (type) {
            case 'asteroid':
                const asteroidSize = [24, 32, 40][Math.floor(Math.random() * 3)];
                enemy = new PIXI.Sprite(this.game.textures.asteroid);
                enemy.width = asteroidSize;
                enemy.height = asteroidSize;
                enemy.health = 1;
                enemy.score = 10; 
                const density = Math.random() * 0.002 + 0.001;
                const restitution = Math.random() * 0.5 + 0.7;
                const friction = Math.random() * 0.05 + 0.01;
                body = Matter.Bodies.circle(
                    spawnX,
                    spawnY,
                    asteroidSize / 2,
                    {
                        label: 'asteroid',
                        density,
                        restitution,
                        friction,
                        frictionAir: 0.01,
                        collisionFilter: {
                            category: 0x0008,
                            mask: 0x0004 | 0x0001
                        }
                    }
                );
                // Give random velocity
                Matter.Body.setVelocity(body, {
                    x: (Math.random() - 0.5) * 8,
                    y: (Math.random() - 0.5) * 8
                });
                Matter.Body.setAngularVelocity(body, (Math.random() - 0.5) * 0.2);
                break;
            case 'shooter':
                enemy = new PIXI.Sprite(this.game.textures.asteroidNpc);
                enemy.width = 36;
                enemy.height = 36;
                enemy.health = 2;
                enemy.score = 25;
                body = Matter.Bodies.circle(
                    spawnX,
                    spawnY,
                    18,
                    {
                        label: 'shooter',
                        friction: 0,
                        frictionAir: 0,
                        collisionFilter: {
                            category: 0x0008,
                            mask: 0x0004 | 0x0001
                        }
                    }
                );
                break;
            case 'shielded':
                enemy = new PIXI.Sprite(this.game.textures.asteroidNpc);
                enemy.width = 44;
                enemy.height = 44;
                enemy.tint = 0x00FFFF;
                enemy.health = 4;
                enemy.score = 50;
                body = Matter.Bodies.circle(
                    spawnX,
                    spawnY,
                    22,
                    {
                        label: 'shielded',
                        friction: 0,
                        frictionAir: 0,
                        collisionFilter: {
                            category: 0x0008,
                            mask: 0x0004 | 0x0001
                        }
                    }
                );
                break;
            case 'boss':
                enemy = new PIXI.Sprite(this.game.textures.asteroidNpc);
                enemy.width = 56;
                enemy.height = 56;
                enemy.tint = 0xFF00FF;
                enemy.health = 10;
                enemy.score = 200;
                body = Matter.Bodies.circle(
                    spawnX,
                    spawnY,
                    28,
                    {
                        label: 'boss',
                        friction: 0,
                        frictionAir: 0,
                        collisionFilter: {
                            category: 0x0008,
                            mask: 0x0004 | 0x0001
                        }
                    }
                );
                break;
        }
        enemy.anchor.set(0.5);
        enemy.type = type;
        Matter.Body.setVelocity(body, {
            x: (Math.random() - 0.5) * 1.5,
            y: speed
        });
        Matter.World.add(this.game.physicsManager.world, body);
        return { sprite: enemy, body: body };
    }

    cleanup() {
        this.enemies.forEach(enemy => {
            if (enemy.parent) enemy.parent.removeChild(enemy);
        });
        
        this.enemyBodies.forEach(body => {
            if (body) Matter.World.remove(this.game.physicsManager.world, body);
        });
        
        this.enemies = [];
        this.enemyBodies = [];
    }
}

window.LaserDefenderEnemyManager = LaserDefenderEnemyManager;