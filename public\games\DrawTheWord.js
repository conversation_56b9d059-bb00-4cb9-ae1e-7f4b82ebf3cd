// DrawTheWord.js - Mini game for drawing a random word
class DrawTheWord extends BaseGame {
    constructor() {
        super();
        this.words = ["ice cream","fish", "apple", "tree", "house", "dog", "cat", "robot", "sun", "moon", "cloud", "maze", "unicorn", "cupcake", "pizza", "truck", "bicycle", "boat", "chair", "table", "lamp", "plant", "flower", "bird", "butterfly", "horse", "elephant", "lion", "bear", "monkey", "snake", "turtle"];
        this.currentWord = null;
        this.drawingsCompleted = 0;
        this.gxpAwarded = false;
        this.drawingCanvas = null;
        this.instructionsText = null;
        this.doneButton = null;
        this.textTimer = null; // Timer for hiding text after 5 seconds
    }

    async startGame() {
        // Require a credit to play, show countdown before drawing
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }
        try {
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);
            this.initGame(); 
            const startScreen = document.getElementById('start-screen');
            const gameArea = document.getElementById('game-area');
            const gameOverScreen = document.getElementById('game-over-screen');
            if (startScreen) startScreen.style.display = 'none';
            if (gameArea) gameArea.style.display = 'block';
            if (gameOverScreen) gameOverScreen.style.display = 'none';
            // Show countdown before enabling drawing
            this.startCountdown();
        } catch (error) {
            console.error('ERROR: Failed to start DrawTheWord game:', error);
            showAlert("Failed to start game. Please try again.");
        }
    }

    setupUI() {
        // Clear stage
        this.app.stage.removeChildren();
        // Create drawing canvas
        if (!this.drawingCanvas) {
            this.drawingCanvas = new DrawingCanvas(500, 500, { // Full height to cover background texture completely
                background: 'images/games/drawtheword/paper.png',
                cursor: 'images/games/drawtheword/pencil.png',
                useBrushShader: true,
                renderer: this.app.renderer
            });
            this.app.stage.addChild(this.drawingCanvas.container);
            // Listen for strokeend event for scoring and effect
            this.drawingCanvas.container.on('strokeend', (event) => {
                if (this.drawingCanvas.isOverHUD) return; // Prevent scoring if pointer is over HUD
                this.score += 10;
                this.updateHUD();
                const pos = event.data.getLocalPosition(this.drawingCanvas.container);
                if (window.VisualEffects) {
                    window.VisualEffects.createScorePopup(pos.x, pos.y, 10, this.app.stage);
                }
            });
        } else {
            this.drawingCanvas.clear();
        }
        // Create instructions text
        if (!this.instructionsText) {
            this.instructionsText = new PIXI.Text('', {fontFamily: 'Press Start 2P', fontSize: 16, fill: 0x333333, align: 'center'});
            this.instructionsText.x = 250;
            this.instructionsText.y = 55; // Back to original position since we have full height
            this.instructionsText.anchor = new PIXI.Point(0.5, 0);
            this.app.stage.addChild(this.instructionsText);
        }
        this.updateInstructions();
        // Create "I'm done" button
        if (!this.doneButton) {
            this.doneButton = this.createDoneButton();
            this.app.stage.addChild(this.doneButton);
        }
    }

    updateInstructions() {
        if (this.instructionsText) {
            this.instructionsText.text = `Can you draw ${this.getArticleForWord(this.currentWord)} ${this.currentWord}?`;
            this.instructionsText.visible = true;
            
            // Clear any existing timer
            if (this.textTimer) {
                clearTimeout(this.textTimer);
            }
            
            // Hide text after 5 seconds
            this.textTimer = setTimeout(() => {
                if (this.instructionsText) {
                    this.instructionsText.visible = false;
                }
            }, 5000);
        }
    }

    getArticleForWord(word) {
        if (!word) return 'a';
        
        // Words that use "the" (unique objects, celestial bodies, etc.)
        const theWords = ['sun', 'moon', 'earth', 'sky', 'ocean', 'mountain', 'river', 'star', 'planet'];
        if (theWords.includes(word.toLowerCase())) {
            return 'the';
        }
        
        // Words that start with vowels (a, e, i, o, u) use "an"
        const vowels = ['a', 'e', 'i', 'o', 'u'];
        if (vowels.includes(word.charAt(0).toLowerCase())) {
            return 'an';
        }
        
        // Default to "a" for consonant-starting words
        return 'a';
    }

    nextWord() {
        this.currentWord = this.words[Math.floor(Math.random() * this.words.length)];
        this.updateInstructions();
        if (this.drawingCanvas) this.drawingCanvas.clear();
    }

    createDoneButton() {
        const button = new PIXI.Container();
        const bg = new PIXI.Graphics();
        bg.beginFill(0xffffff);
        bg.lineStyle(2, 0x333333);
        bg.drawRoundedRect(0, 0, 120, 40, 10);
        bg.endFill();
        button.addChild(bg);
        const label = new PIXI.Text("I'm done", {fontFamily: 'Arial', fontSize: 20, fill: 0x333333});
        label.x = 60;
        label.y = 20;
        label.anchor = new PIXI.Point(0.5, 0.5);
        button.addChild(label);
        button.x = 367;
        button.y = 390; // Move higher to avoid HUD
        button.interactive = true;
        button.buttonMode = true;
        button.on('pointerdown', () => this.onDone());
        return button;
    }

    onDone() {
        if (this.drawingCanvas && this.drawingCanvas.hasDrawn()) {
            this.drawingsCompleted++;
            if (this.drawingsCompleted <= 3 && !this.gxpAwarded) {
                this.score += 100; // Points for each drawing
                this.updateHUD();
                if (this.drawingsCompleted === 3) {
                    this.gxpAwarded = true;
                    showAlert('Victory! You earned GXP for 3 drawings!');
                    // Issue GXP reward here (call backend or update playerData)
                    if (typeof transactResource === 'function') {
                        transactResource('gxp', 10, 'add', showAlert, updatePlayerBalances); // Award 10 GXP (adjust as needed)
                    }
                } else {
                    showAlert('Great drawing! Here is your next word.');
                }
            } else if (this.drawingsCompleted > 3) {
                showAlert('You can keep drawing, but no more GXP will be awarded.');
            }
            this.nextWord();
        } else {
            showAlert('Please draw something before clicking "I\'m done"!');
        }
    }

    stopGame() {
        super.stopGame();
        if (this.textTimer) {
            clearTimeout(this.textTimer);
            this.textTimer = null;
        }
        this.drawingCanvas = null;
        this.instructionsText = null;
        this.doneButton = null;
        this.drawingsCompleted = 0;
        this.gxpAwarded = false;
    }

    onCountdownComplete() {
        // Only allow drawing after countdown completes
        this.nextWord();
        this.setupUI();
    }

    updateHUD() {
        // Implementation of updateHUD method
    }
}

window.DrawTheWord = DrawTheWord; 