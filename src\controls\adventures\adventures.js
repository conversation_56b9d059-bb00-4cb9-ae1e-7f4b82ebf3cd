const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addAdventure = (req, res) => {
  const {
    owner_id,
    team_id,
    init_steps,
    current_steps,
    mapgrid_4,
    mapgrid_16,
    mapgrid_256,
    status
  } = req.body;

  // Check if team is already assigned an adventure
  const checkAdventureQuery = `SELECT * FROM adventures WHERE team_id = $1 AND status = 'In Progress'`;
  pool.query(checkAdventureQuery, [team_id], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      return;
    }
    if (results.rows.length > 0) {
      res.status(400).send("Team is already assigned an adventure.");
      return;
    }

    // Add the new adventure and update team status
    pool.query(queries.add.addAdventure, [owner_id, team_id, init_steps, current_steps, mapgrid_4, mapgrid_16, mapgrid_256, status], (error, results) => {
      if (error) {
        res.status(500).send(error.message);
        return;
      }
      const updateTeamQuery = `UPDATE teams SET status = 'In Progress' WHERE team_id = $1`;
      pool.query(updateTeamQuery, [team_id], (error, results) => {
        if (error) {
          res.status(500).send(error.message);
          return;
        }

        // Add game log entry
        const logData = {
          desc: "You started a new adventure.",
          location: [mapgrid_4, mapgrid_16, mapgrid_256]
        };
        pool.query(queries.add.addGameLog, [owner_id, "new", "adventure", JSON.stringify(logData)], (error, results) => {
          if (error) {
            res.status(500).send(error.message);
            return;
          }
          res.status(201).send("NEW ADVENTURE ITEM added successfully!");
        });
      });
    });
  });
};

const getAdventures = (req, res) => {
  pool.query(queries.get.getAdventures, (error, results) => {
    res.status(200).json(results.rows);
    return;
  });
};

const getAdventuresByOwnerId = (req, res) => {
  var owner_id = req.params.owner_id;
  pool.query(queries.getby.getAdventuresByOwnerId, [owner_id], (error, results) => {
    res.status(200).json(results.rows);
    return;
  });
};

const getAdventuresByTeamId = (req, res) => {
  var team_id = req.params.team_id;
  pool.query(queries.getby.getAdventuresByTeamId, [team_id], (error, results) => {
    res.status(200).json(results.rows);
    return;
  });
};

const removeAdventure = (req, res) => {
  const adventure_id = req.params.adventure_id;
  if (!adventure_id) {
    return res.status(400).send("Missing adventure_id parameter");
  }

  // First, get the team_id associated with this adventure
  pool.query("SELECT team_id FROM adventures WHERE adventure_id = $1", [adventure_id], (error, adventureResult) => {
    if (error) {
      return res.status(500).send(error.message);
    }

    if (adventureResult.rows.length === 0) {
      return res.status(404).send("Adventure not found");
    }

    const team_id = adventureResult.rows[0].team_id;

    // Now remove the adventure
    pool.query(queries.remove.removeAdventure, [adventure_id], (error, results) => {
      if (error) {
        return res.status(500).send(error.message);
      }

      // Update the team status to Napping
      pool.query(queries.up.updateTeamNap, [0, 'Napping', team_id], (error, teamResult) => {
        if (error) {
          return res.status(500).send(error.message);
        }
        res.status(200).send("ADVENTURE removed successfully.");
      });
    });
  });
};

const updateAdventure = (req, res) => {
  var id = req.params.adventure_id;
  var current_steps = req.params.current_steps;
  var status = req.params.status;
  var {
    current_steps,
    status
  } = req.body;
  pool.query(queries.up.updateAdventure, [current_steps, status, id], (error, results) => {
    if (error) {
      throw error
    }
    res.status(200).send("ITEM updated successfully.");
  });
};

module.exports = {
  addAdventure,
  getAdventures,
  getAdventuresByOwnerId,
  getAdventuresByTeamId,
  removeAdventure,
  updateAdventure
};