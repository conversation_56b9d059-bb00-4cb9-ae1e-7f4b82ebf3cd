/* Info modal container */
.modal-content {
    border: 1px solid #ededed;
    border-radius: 3px;
    background: #ffe91b;
    font-size: 14px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.25);
}

.modal-title {
  color: white;
  font-weight: bold;
  font-size: 24px;
  text-shadow: -1px 1px 0 #551843;
}

.modal-header {
    background: #9e683c;
    background-image: url('../images/ui/bg/modal_header.png');
}

.modal-footer{
  border-top: none !important;
}

.modal-backdrop.fade {
  opacity: 0.5;
}

/* Book navigation styles */
.book-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #ddd;
    padding-top: 15px;
    margin-top: 15px;
}

.book-navigation button {
    background: #9e683c;
    color: white;
    border: 2px solid #551843;
    border-radius: 5px;
    padding: 8px 12px;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.book-navigation button:hover:not(:disabled) {
    background: #b87a4a;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.book-navigation button:disabled {
    background: #ccc;
    border-color: #999;
    color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.page-indicator {
    font-weight: bold;
    color: #551843 !important;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
}
