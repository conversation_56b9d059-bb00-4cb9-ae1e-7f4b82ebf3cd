class DataService {
  static lastNotificationTime = 0;
  
  static async fetchInitialData(userAccount) {
    console.log('[DATA DEBUG] fetchInitialData called');
    const urls = await createUrls(userAccount);

    // First load essential data
    await Promise.all([
      reloadPlayerData.all(),
      getPlayerCoreData(),
      displayBalances(url_player, url_dust),
      getPlayerSettings(userAccount)
    ]);

    // Then load map data - this is critical for displaying the world correctly
    await getMapData();
    await getAdventures();

    // Finally load the rest of the data
    const activeTab = determineActiveTab();
    const dataPromises = [
      displayGameLogs(userAccount),
      updatePlayerCounters(playerCounter, myTeams, playerAdventures),
      updateTotalProgressBar(playerAdventures)
    ];
    
    // Only display inventory if the inventory tab is active
    if (activeTab === 'inventory') {
      dataPromises.push(displayInventory(currentInventoryView, 'general-inventory'));
    }
    
    return Promise.all(dataPromises);
  }

  static setupPeriodicUpdates(userAccount, interval = 5 * 60 * 1000) {
    return setInterval(async () => {
      // Update data in the background
      await reloadPlayerData.all();
      await getPlayerCoreData();
      await getMapData();
      await getAdventures();
      await displayTeamsOnMap(displayTeamIcon, displayVehicleMovingIcon, displayTreasureIcon, displayTeamReadyIcon);

      // Check which tab is currently active
      const activeTab = determineActiveTab();
      
      // Only refresh the inventory display if the user is in the inventory tab
      // This prevents disruption when viewing adventures or teams
      if (activeTab === 'inventory') {
        displayInventory(currentInventoryView, 'general-inventory');
      } else {
        // For other tabs, show a simple alert that data has been updated
        // This allows users to manually refresh if they want to see the latest data
        const currentTime = Date.now();
        const timeSinceLastNotification = currentTime - DataService.lastNotificationTime;
        
        // Only show notification if it's been at least 30 seconds since the last one
        if (timeSinceLastNotification > 30000) {
          // Removed showAlert call as requested
          DataService.lastNotificationTime = currentTime;
        }
      }

      displayGameLogs(userAccount);
      updatePlayerCounters(playerCounter, myTeams, playerAdventures);
      updateTotalProgressBar(playerAdventures);
    }, interval);
  }
}
