
function createNewLocaleName(currentTile) {
    let setLocaleName; 
    switch (currentTile) {
      case 'grassplains':
        setLocaleName = getRandomName(grasses);
        break;
      case 'water':
        setLocaleName = getRandomName(waters);
        break;
      case 'forest':
        setLocaleName = getRandomName(forests) + " Forest";
        break;
      case 'castle':
        setLocaleName = getRandomName(castles) + " Castle";
        break;
      case 'town':
        setLocaleName = getRandomName(towns) + " Town";
        break;
      case 'ruins':
        setLocaleName = getRandomName(ruins) + " Ruins";
        break;
      case 'tr_water':
        setLocaleName = getRandomName(tr_water) + " Waters";
        break;
      case 'tr_island':
        setLocaleName = getRandomName(tr_islands) + " Island";
        break;
      case 'tr_waterland':
        setLocaleName = getRandomName(tr_waterlands) + " Waterland";
        break;
      case 'tr_castle':
        setLocaleName = getRandomName(tr_castles) + " Longhouse";
        break;
      case 'ds_dirt':
        setLocaleName = getRandomName(ds_dirt);
        break;
      case 'ds_dunes':
        setLocaleName = getRandomName(ds_dunes) + " Dunes";
        break;
      case 'ds_ruins':
        setLocaleName = getRandomName(ds_ruins) + " Ruins";
        break;
      case 'ds_castle':
        setLocaleName = getRandomName(ds_castles) + " Fortress";
        break;
      case 'ds_town':
        setLocaleName = getRandomName(ds_towns) + " Town";
        break;
      case 'sp_normal':
        setLocaleName = getRandomName(sp_space) + " Space";
        break;
      case 'sp_debris':
        setLocaleName = getRandomName(sp_debris);
        break;
      case 'sp_gas1':
        setLocaleName = getRandomName(sp_gasfields) + " Field";
        break;
      case 'sp_station1':
        setLocaleName = getRandomName(sp_stations) + " Station";
        break;
      case 'sp_gplanet1':
      case 'sp_dplanet1':
      case 'sp_rplanet1':
      case 'sp_iplanet1':
        setLocaleName = getRandomName(sp_planets) + " Planet";
        break;
    }
    return setLocaleName;
  }
  