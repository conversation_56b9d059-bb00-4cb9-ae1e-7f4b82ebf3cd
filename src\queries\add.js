const addPlayer = "INSERT INTO new_players (wax_id, date_joined, last_online, lv, xp, gxp, nectar, credits) VALUES ($1, $2, $3, 1, 100, $4, $5, $6) ON CONFLICT (wax_id) DO NOTHING";
const addPlayerSettings = 'INSERT INTO player_settings (wax_id, data) VALUES ($1, $2::jsonb)';
const addTeam = "INSERT INTO teams (owner_id, team_name, mapgrid_4, mapgrid_16, mapgrid_256, nap_current, nap_total, status, data) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9::JSONB) RETURNING team_id";
const addAdventure = "INSERT INTO adventures (owner_id, team_id, init_steps, current_steps, mapgrid_4, mapgrid_16, mapgrid_256, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)";
const addHouse = "INSERT INTO houses (owner_id, renter_id, asset_id, asset_name, price, capacity, status) VALUES ($1, $2, $3, $4,  $5, $6, $7) ON CONFLICT (asset_id) DO NOTHING";
const addReward = "INSERT INTO rewards (wax_id, event_id, type, title, description, schema, template_id, amount, created_date, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)";
const addEscrow = "INSERT INTO escrow (asset_id, owner_id, renter_id, elapsed_days, max_days, currency_type, balance, deposit, start_date, end_date, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)";
const addZone = "INSERT INTO map_zones (mapgrid_4, mapgrid_16, zone_name, zone_type, gxp_paid, gxp_required, status) VALUES ($1, $2, $3, $4, $5, $6, $7)";
const addGameLog = "INSERT INTO os_log (wax_id, status, type, data) VALUES ($1, $2, $3, $4);"
const addUserSession = "WITH delete AS (DELETE FROM user_sessions WHERE wax_id = $1 RETURNING *) INSERT INTO user_sessions (wax_id, token, expires_at) VALUES ($1, $2, NOW() + INTERVAL '1 hour')";
const addRental = "INSERT INTO rentals (owner_id, renter_id, asset_id, asset_info, start_date, end_date, price_per_day, days_rented, total_price, amount_paid, currency, rental_status, payment_status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)";
const addGlobalLimit = "INSERT INTO os_global_cap (reward_type, current_count, max_limit, duration_hours, expires_at) VALUES ($1, $2, $3, $4, $5)";
const addPlayerCap = "INSERT INTO os_player_cap (wax_id, reward_type, current_count, max_limit, expires_at) VALUES ($1, $2, $3, $4, $5)";
const addSystemLog = "INSERT INTO os_msg (msg_type, category, reward_type, message, details) VALUES ($1, $2, $3, $4, $5::JSONB)";

module.exports = {
	addPlayer, addPlayerSettings,
	addTeam,
	addAdventure,
	addHouse,
	addReward,
	addEscrow,
	addZone,
	addGameLog,
	addUserSession,
	addRental,
	addGlobalLimit,
	addPlayerCap,
	addSystemLog
}
