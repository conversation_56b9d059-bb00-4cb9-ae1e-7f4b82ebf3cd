async function createSessionForUser() {
  try {
    const pubKey = wax.pubKeys[0];
    const response = await axios.post(`${domain_url}/sessions`, {
      user: userAccount,
      token: pubKey
    });

    if (response.status === 200 || response.status === 201) {
      return response.data;
    } else {
      console.error('Failed to create session:', response.status, response.data);
      throw new Error('Failed to create session');
    }
  } catch (error) {
    console.error('Error during session creation:', error.message);
    throw error;
  }
}
