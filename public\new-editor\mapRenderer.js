// mapRenderer.js - Three.js rendering and scene management for the map editor 
let scene, camera, renderer;
let tileMeshes = [];
let raycaster, mouse; 
const initThreeJsMapEditor = function() {
  if (typeof THREE === 'undefined') {
    console.error('Three.js library not loaded');
    return;
  }
  
  const container = document.getElementById('threejs-map-container');
  if (!container) {
    console.error('Container not found for Three.js initialization');
    return;
  }
  container.innerHTML = ''; 
  scene = new THREE.Scene();
  console.log('Three.js scene initialized:', scene); 
  // Camera - start with a larger viewport to accommodate different grid sizes
  const width = 512; // Fixed width for the viewport
  const height = 512; // Fixed height for the viewport
  camera = new THREE.OrthographicCamera(
    -width/2, width/2, height/2, -height/2, 0.1, 1000
  );
  camera.position.z = 10; 
  renderer = new THREE.WebGLRenderer({ antialias: false, alpha: false });
  renderer.setClearColor(0x222222);
  renderer.setSize(width, height);
  container.appendChild(renderer.domElement); 
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2(); 
  renderer.domElement.addEventListener('click', onMapClick); 
  addNavigationButtons(); 
  animate();  
  window.renderer = renderer;
  window.scene = scene;
}; 
const animate = function() { 
  if (globalThis.editedGlowMeshes && globalThis.editedGlowMeshes.length > 0) {
    const time = Date.now() * 0.003;  
    const blink = (Math.sin(time * 2) + 1) / 2;  
    // Vibrant green: #00ff00 (0x00ff00), White: #ffffff (0xffffff)
    const r = Math.round(255 * (1 - blink));
    const g = Math.round(255 * (1 - blink) + 255 * blink);
    const b = Math.round(255 * (1 - blink));
    const color = (r << 16) | (g << 8) | b;
    globalThis.editedGlowMeshes.forEach(glowMesh => {
      if (glowMesh.material) {
        glowMesh.material.color.setHex(color);
        glowMesh.material.needsUpdate = true;
      }
    });
  }
  // Animate blinking border for selected tile (orange/white, slow blink)
  if (selectedTileMesh && selectedTileMesh.material) {
    const time = Date.now() * 0.001; // slower speed
    const blink = (Math.sin(time) + 1) / 2; // 0 to 1
    // Orange: #ffa500 (0xffa500), White: #ffffff (0xffffff)
    const r = Math.round(255 * (1 - blink) + 255 * blink);
    const g = Math.round(165 * (1 - blink) + 255 * blink);
    const b = Math.round(0 * (1 - blink) + 255 * blink);
    const color = (r << 16) | (g << 8) | b;
    selectedTileMesh.material.color.setHex(color);
    selectedTileMesh.material.needsUpdate = true;
  }
  if (renderer && scene && camera) {
    renderer.render(scene, camera);
  }
  requestAnimationFrame(animate);
};
 
const onMapClick = function(event) {
  const rect = renderer.domElement.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1; 
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(tileMeshes); 
  if (intersects.length > 0) {
    const obj = intersects[0].object;
    const type = obj.userData.type;
    const id = obj.userData.id; 
    if (type === 'world') {
      handleWorldClick(id);
    } else if (type === 'zone') {
      handleZoneClick(id);
    } else if (type === 'locale') {
      handleLocaleClick(obj.userData);
    }
  }
};
 
const handleWorldClick = function(worldId) {
  const nav = window.getNavigation();
  nav.world = worldId;
  nav.zone = 0;
  window.setNavigation(nav);
  window.clearEditedTiles(); 
  // Update tile type dropdown based on new world
  if (window.updateTileTypeDropdown) {
    window.updateTileTypeDropdown();
  } 
  renderZones();
  // Update navigation label will be handled in renderZones
};

const handleZoneClick = function(zoneId) {
  const nav = window.getNavigation();
  nav.zone = zoneId;
  window.setNavigation(nav);
  window.clearEditedTiles();
  renderLocales();
  // Update navigation label will be handled in renderLocales
};

const handleLocaleClick = function(userData) {
  const idx = userData.idx; 
  if (window.currentMode === 'paint') {
    // Paint mode: apply current inputs to the clicked tile
    if (window.paintTile) {
      window.paintTile(idx);
    }
  } else {
    // Edit mode: load tile data into inputs
    if (window.loadTileForEditing) {
      window.loadTileForEditing(idx);
    }
  }
  
  // If this tile has been edited, show a tooltip or highlight it
  const editedTiles = window.getEditedTiles();
  if (editedTiles.has(idx)) {
    console.log('Tile ' + idx + ' has pending changes. You can re-edit it.');
  }
};
 
const addNavigationButtons = function() {
  const container = document.getElementById('threejs-map-container');
  if (!container) {
    console.error('Container not found, cannot add navigation buttons');
    return;
  } 
  const buttonContainer = document.createElement('div');
  buttonContainer.classList.add('editor-nav-buttons');
  
  const worldBtn = window.createNavButton('World', 'world-icon', function() {
    const nav = window.getNavigation();
    nav.view = 'worlds';
    nav.world = 0;
    nav.zone = 0;
    window.setNavigation(nav);
    window.clearEditedTiles(); 
    if (window.updateTileTypeDropdown) {
      window.updateTileTypeDropdown();
    } 
    renderWorlds();
    window.updateNavButtons('world');
  });
  
  const zoneBtn = window.createNavButton('Zone', 'zone-icon', function() {
    const nav = window.getNavigation();
    nav.view = 'zones';
    window.setNavigation(nav);
    window.clearEditedTiles();
    renderZones();
    window.updateNavButtons('zone');
  });
  
  const localeBtn = window.createNavButton('Locale', 'locale-icon', function() {
    const nav = window.getNavigation();
    if (nav.view === 'worlds') {
      nav.view = 'zones';
      window.setNavigation(nav);
      window.clearEditedTiles();
      renderZones();
    } else if (nav.view === 'zones') {
      nav.view = 'locales';
      window.setNavigation(nav);
      window.clearEditedTiles();
      renderLocales();
    }
    window.updateNavButtons('locale');
  });
  buttonContainer.appendChild(worldBtn);
  buttonContainer.appendChild(zoneBtn);
  buttonContainer.appendChild(localeBtn);
  container.appendChild(buttonContainer);
  window.updateNavButtons('world');
};

const clearScene = function() {
  if (!scene) return;
  clearSelectionHighlight();
  tileMeshes.forEach(function(mesh) {
    removeGlowingBorder(mesh);
    scene.remove(mesh);
    if (mesh.material) {
      if (mesh.material.map) mesh.material.map.dispose();
      mesh.material.dispose();
    }
    if (mesh.geometry) mesh.geometry.dispose();
  });
  tileMeshes = [];
  const navButtons = document.querySelector('.editor-nav-buttons');
  if (navButtons) {
    navButtons.remove();
  }
};

globalThis.editedGlowMeshes = globalThis.editedGlowMeshes || [];
const addGlowingBorder = function(mesh, tileIndex) {
  removeGlowingBorder(mesh);
  const size = window.TILE_SIZE + 4;
  const borderGeometry = new THREE.EdgesGeometry(new THREE.PlaneGeometry(size, size));
  const borderMaterial = new THREE.LineBasicMaterial({
    color: 0xffffff, // Start as white, will animate
    transparent: true,
    opacity: 0.95,
    linewidth: 2
  });
  const border = new THREE.LineSegments(borderGeometry, borderMaterial);
  border.position.copy(mesh.position);
  border.position.z = mesh.position.z + 0.1; // Place above the main mesh
  border.userData.tileIndex = tileIndex;
  mesh.userData.glowMesh = border;
  scene.add(border);
  globalThis.editedGlowMeshes.push(border);
};

const removeGlowingBorder = function(mesh) {
  if (mesh.userData.glowMesh) {
    scene.remove(mesh.userData.glowMesh);
    mesh.userData.glowMesh.geometry.dispose();
    mesh.userData.glowMesh.material.dispose();
    globalThis.editedGlowMeshes = globalThis.editedGlowMeshes.filter(gm => gm !== mesh.userData.glowMesh);
    delete mesh.userData.glowMesh;
  }
};
let selectedTileMesh = null;
let selectedTileIndex = null;

const addSelectionHighlight = function(tileIndex) {
  clearSelectionHighlight();

  if (tileIndex >= 0 && tileIndex < tileMeshes.length) {
    const editedTiles = window.getEditedTiles ? window.getEditedTiles() : new Set();
    if (window.currentMode === 'edit' && !editedTiles.has(tileIndex)) {
      const mesh = tileMeshes[tileIndex];
      const size = window.TILE_SIZE + 6;
      const borderGeometry = new THREE.EdgesGeometry(new THREE.PlaneGeometry(size, size));
      const borderMaterial = new THREE.LineBasicMaterial({
        color: 0xffffff, // Start as white, will animate
        transparent: true,
        opacity: 0.95,
        linewidth: 2
      });
      const border = new THREE.LineSegments(borderGeometry, borderMaterial);
      border.position.copy(mesh.position);
      border.position.z = mesh.position.z + 0.2; 
      border.userData.isSelectionHighlight = true;
      scene.add(border);
      selectedTileMesh = border;
      selectedTileIndex = tileIndex;
    } else {
      selectedTileMesh = null;
      selectedTileIndex = null;
    }
  }
};

const clearSelectionHighlight = function() {
  if (selectedTileMesh) {
    scene.remove(selectedTileMesh);
    selectedTileMesh.geometry.dispose();
    selectedTileMesh.material.dispose();
    selectedTileMesh = null;
    selectedTileIndex = null;
  }
};

const updateAllEditedTilesVisual = function() {
  const editedTiles = window.getEditedTiles();
  editedTiles.forEach(function(tileIndex) {
    if (tileIndex >= 0 && tileIndex < tileMeshes.length) {
      const mesh = tileMeshes[tileIndex];
      addGlowingBorder(mesh, tileIndex);
    }
  });
};

const updateTileVisual = function(tileIndex) {
  if (tileIndex >= 0 && tileIndex < tileMeshes.length) {
    const mesh = tileMeshes[tileIndex];
    const mapData = window.getMapData();
    const tile = mapData[tileIndex];
    const texturePath = window.TILE_TEXTURES[tile.type] || window.TILE_TEXTURES['grassplains'];
    const loader = new THREE.TextureLoader();
    loader.load(texturePath, function(texture) {
      texture.magFilter = THREE.NearestFilter;
      texture.minFilter = THREE.NearestFilter;
      mesh.material.map = texture;
      mesh.material.needsUpdate = true;      
      // Add glowing border if tile has been edited
      const editedTiles = window.getEditedTiles();
      if (editedTiles.has(tileIndex)) {
        addGlowingBorder(mesh, tileIndex);
      }
    });
  }
};

const createClickableMesh = function(texturePath, x, y, size, depth, userData) {
  depth = depth || 10;
  userData = userData || {};
    return new Promise(function(resolve, reject) {
    console.log('createClickableMesh called with scene:', scene);
    if (!scene) {
      console.error('Scene is undefined in createClickableMesh');
      reject(new Error('Scene not initialized'));
      return;
    }
    const loader = new THREE.TextureLoader();
    loader.load(
      texturePath,
      function(texture) {
        console.log('Texture loaded successfully:', texturePath);
        texture.magFilter = THREE.NearestFilter;
        texture.minFilter = THREE.NearestFilter;
        
        const material = new THREE.MeshBasicMaterial({ 
          map: texture,
          transparent: true,
          opacity: 1.0
        });
        const geometry = new THREE.BoxGeometry(size, size, depth);
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(x, y, 0);
        mesh.userData = userData;
        scene.add(mesh);
        tileMeshes.push(mesh);
        resolve(mesh);
      },
      function(progress) {
        console.log('Loading progress:', texturePath, progress);
      },
      function(error) {
        reject(error);
      }
    );
  });
};

const createFallbackMesh = function(x, y, size, depth, userData, color) {
  depth = depth || 10;
  userData = userData || {};
  color = color || 0x9E9E9E;
  if (!scene) {
    console.error('Scene not initialized');
    return null;
  }
  const material = new THREE.MeshBasicMaterial({ 
    color: color,
    transparent: true,
    opacity: 1.0
  });
  const geometry = new THREE.BoxGeometry(size, size, depth);
  const mesh = new THREE.Mesh(geometry, material);
  mesh.position.set(x, y, 0);
  mesh.userData = userData;
  scene.add(mesh);
  tileMeshes.push(mesh);
  return mesh;
};

// Texture loading
const loadTileTextures = function() {
  if (typeof THREE === 'undefined') {
    console.error('Three.js library not loaded');
    return Promise.reject(new Error('Three.js library not loaded'));
  }
  
  const loader = new THREE.TextureLoader();
  const keys = Object.keys(window.TILE_TEXTURES);
  const promises = keys.map(function(key) {
    return new Promise(function(resolve) {
      loader.load(window.TILE_TEXTURES[key], function(texture) {
        texture.magFilter = THREE.NearestFilter;
        texture.minFilter = THREE.NearestFilter;
        resolve([key, texture]);
      });
    });
  });
  return Promise.all(promises).then(function(results) {
    const textures = {};
    results.forEach(function(result) { 
      textures[result[0]] = result[1]; 
    });
    return textures;
  });
};

const createTileGrid = function(textures) {
  if (!scene) {
    console.error('Scene not initialized');
    return;
  }  
  // Remove old meshes
  tileMeshes.forEach(function(mesh) { scene.remove(mesh); });
  tileMeshes = [];
  const mapData = window.getMapData();
  const totalSize = window.GRID_SIZE * window.TILE_SIZE;
  const offsetX = -totalSize / 2 + window.TILE_SIZE / 2;
  const offsetY = -totalSize / 2 + window.TILE_SIZE / 2;
    for (let y = 0; y < window.GRID_SIZE; y++) {
    for (let x = 0; x < window.GRID_SIZE; x++) {
      const idx = y * window.GRID_SIZE + x;
      const tile = mapData[idx] || { type: 'grassplains', name: '', variant: 'land' };
      const texture = textures[tile.type] || textures['grassplains'];
      const geometry = new THREE.PlaneGeometry(window.TILE_SIZE, window.TILE_SIZE);
      const material = new THREE.MeshBasicMaterial({ map: texture });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.x = x * window.TILE_SIZE + offsetX;
      mesh.position.y = y * window.TILE_SIZE + offsetY;
      mesh.position.z = 0;
      mesh.userData = { x: x, y: y, idx: idx, type: 'locale' };
      scene.add(mesh);
      tileMeshes.push(mesh);
      const editedTiles = window.getEditedTiles();
      if (editedTiles.has(idx)) {
        addGlowingBorder(mesh, idx);
      }
    }
  }
};

const resetCameraForView = function(viewType) {
  if (!camera) return;
    switch (viewType) {
    case 'worlds':
      // For world view (2x2 grid), adjust camera to fit the world layout
      camera.zoom = 1;
      camera.position.set(0, 0, 10);
      break;
    case 'zones':
      // For zone view (4x4 grid), adjust camera to fit the zone layout
      camera.zoom = 1;
      camera.position.set(0, 0, 10);
      break;
    case 'locales':
      // For locale view (16x16 grid), center the camera on the grid
      camera.zoom = 1;
      camera.position.set(0, 0, 10);
      break;
  }
  camera.updateProjectionMatrix();
};

// Make functions globally accessible
window.initThreeJsMapEditor = initThreeJsMapEditor;
window.clearScene = clearScene;
window.addGlowingBorder = addGlowingBorder;
window.removeGlowingBorder = removeGlowingBorder;
window.addSelectionHighlight = addSelectionHighlight;
window.clearSelectionHighlight = clearSelectionHighlight;
window.updateAllEditedTilesVisual = updateAllEditedTilesVisual;
window.updateTileVisual = updateTileVisual;
window.createClickableMesh = createClickableMesh;
window.createFallbackMesh = createFallbackMesh;
window.loadTileTextures = loadTileTextures;
window.createTileGrid = createTileGrid;
window.addNavigationButtons = addNavigationButtons;
window.resetCameraForView = resetCameraForView; 
const highlightSelectedTile = function() {
  if (window.clearSelectionHighlight) {
    window.clearSelectionHighlight();
  }
  if (window.getSelectedTile && window.getSelectedTile() !== null && window.addSelectionHighlight) {
    window.addSelectionHighlight(window.getSelectedTile());
  } 
};
window.highlightSelectedTile = highlightSelectedTile; 