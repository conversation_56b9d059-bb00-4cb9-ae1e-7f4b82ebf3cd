function getPlayerOption(optionKey, defaultValue = null) {
  if (typeof playerOptions !== 'undefined' && playerOptions[optionKey] !== undefined) {
    return playerOptions[optionKey];
  }
  return defaultValue;
}

/**
 * Set a player option value
 * @param {string} optionKey - The option key to set
 * @param {any} value - The value to set
 * @param {string} waxId - The player's WAX ID
 */
function setPlayerOption(optionKey, value, waxId) {
  if (typeof updatePlayerOption === 'function') {
    updatePlayerOption(optionKey, value, waxId);
  } else {
    if (typeof playerOptions !== 'undefined') {
      playerOptions[optionKey] = value;
    }
  }
}

function isPlayerOptionEnabled(optionKey) {
  return getPlayerOption(optionKey, false) === true;
}

function getAllPlayerOptions() {
  return typeof playerOptions !== 'undefined' ? { ...playerOptions } : {};
}

function resetAllPlayerOptions(waxId) {
  if (typeof initializePlayerOptions === 'function') {
    initializePlayerOptions(waxId);
  }
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getPlayerOption,
    setPlayerOption,
    isPlayerOptionEnabled,
    getAllPlayerOptions,
    resetAllPlayerOptions
  };
}
