// ImageSaver.js - Save drawing canvas as PNG
class ImageSaver {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.onSave = options.onSave || (() => {});
        this.onError = options.onError || (() => {});
        this.quality = options.quality || 1.0;
    }

    saveAsPNG(filename = 'drawing.png') {
        try {
            // Create a temporary canvas to combine all layers
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            
            // Set canvas size to match the drawing canvas
            tempCanvas.width = this.canvas.width;
            tempCanvas.height = this.canvas.height;
            
            // Get the PIXI renderer
            const renderer = this.canvas.app ? this.canvas.app.renderer : PIXI.autoDetectRenderer();
            
            // Render the entire container to a texture
            const texture = renderer.generateTexture(this.canvas.container);
            
            // Convert PIXI texture to canvas
            const pixiCanvas = renderer.extract.canvas(texture);
            
            // Draw the PIXI canvas to our temp canvas
            tempCtx.drawImage(pixiCanvas, 0, 0);
            
            // Convert to blob and download
            tempCanvas.toBlob((blob) => {
                if (blob) {
                    this.downloadBlob(blob, filename);
                    this.onSave(filename);
                } else {
                    this.onError('Failed to create image blob');
                }
            }, 'image/png', this.quality);
            
        } catch (error) {
            console.error('Error saving image:', error);
            this.onError(error.message);
        }
    }

    saveWithBackground(filename = 'drawing-with-bg.png') {
        try {
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            tempCanvas.width = this.canvas.width;
            tempCanvas.height = this.canvas.height;
            tempCtx.fillStyle = '#FFFFFF';
            tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
            if (this.canvas.bgSprite && this.canvas.bgSprite.texture) {
                const bgTexture = this.canvas.bgSprite.texture;
                const bgImage = bgTexture.baseTexture.resource.source;
                if (bgImage) {
                    tempCtx.drawImage(bgImage, 0, 0, tempCanvas.width, tempCanvas.height);
                }
            }
            const renderer = this.canvas.app ? this.canvas.app.renderer : (this.canvas._renderer || PIXI.autoDetectRenderer());
            let drawingSource = this.canvas.renderSprite;
            if (!drawingSource && this.canvas.renderTexture) {
                drawingSource = new PIXI.Sprite(this.canvas.renderTexture);
            }
            if (!drawingSource) {
                this.onError('No render texture to save');
                return;
            }
            const drawingTexture = renderer.generateTexture(drawingSource);
            const drawingCanvas = renderer.extract.canvas(drawingTexture);
            tempCtx.drawImage(drawingCanvas, 0, 0);
            tempCanvas.toBlob((blob) => {
                if (blob) {
                    this.downloadBlob(blob, filename);
                    this.onSave(filename);
                } else {
                    this.onError('Failed to create image blob');
                }
            }, 'image/png', this.quality);
        } catch (error) {
            console.error('Error saving image with background:', error);
            this.onError(error.message);
        }
    }

    saveDrawingOnly(filename = 'drawing-lines.png') {
        try {
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            tempCanvas.width = this.canvas.width;
            tempCanvas.height = this.canvas.height;
            tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
            const renderer = this.canvas.app ? this.canvas.app.renderer : (this.canvas._renderer || PIXI.autoDetectRenderer());
            let drawingSource = this.canvas.renderSprite;
            if (!drawingSource && this.canvas.renderTexture) {
                drawingSource = new PIXI.Sprite(this.canvas.renderTexture);
            }
            if (!drawingSource) {
                this.onError('No render texture to save');
                return;
            }
            const drawingTexture = renderer.generateTexture(drawingSource);
            const drawingCanvas = renderer.extract.canvas(drawingTexture);
            tempCtx.drawImage(drawingCanvas, 0, 0);
            tempCanvas.toBlob((blob) => {
                if (blob) {
                    this.downloadBlob(blob, filename);
                    this.onSave(filename);
                } else {
                    this.onError('Failed to create image blob');
                }
            }, 'image/png', this.quality);
        } catch (error) {
            console.error('Error saving drawing only:', error);
            this.onError(error.message);
        }
    }

    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    getDataURL() {
        try {
            const renderer = this.canvas.app ? this.canvas.app.renderer : PIXI.autoDetectRenderer();
            const texture = renderer.generateTexture(this.canvas.container);
            const canvas = renderer.extract.canvas(texture);
            return canvas.toDataURL('image/png', this.quality);
        } catch (error) {
            console.error('Error getting data URL:', error);
            this.onError(error.message);
            return null;
        }
    }

    saveToClipboard() {
        try {
            const dataURL = this.getDataURL();
            if (dataURL) {
                // Convert data URL to blob
                fetch(dataURL)
                    .then(res => res.blob())
                    .then(blob => {
                        if (navigator.clipboard && navigator.clipboard.write) {
                            const item = new ClipboardItem({ 'image/png': blob });
                            navigator.clipboard.write([item]).then(() => {
                                this.onSave('clipboard');
                            }).catch(err => {
                                this.onError('Failed to copy to clipboard: ' + err.message);
                            });
                        } else {
                            this.onError('Clipboard API not supported');
                        }
                    });
            }
        } catch (error) {
            console.error('Error saving to clipboard:', error);
            this.onError(error.message);
        }
    }
}

window.ImageSaver = ImageSaver; 