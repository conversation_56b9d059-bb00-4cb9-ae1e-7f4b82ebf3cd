<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Crushie Battle MVP</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/pure/2.0.6/pure-min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="battle.css">
</head>
<body>
  <div class="battle-container">
    <div class="battle-card">
      <div class="battle-header">
        <img src="../images/ui/hud/hud_teams.png" alt="Team">
        <h3 id="team-display">Your Squad</h3>
        <div class="team-status">
          <span class="status-badge ready">Ready</span>
        </div>
      </div>
      
      <div class="vehicle-display" id="team-vehicle">
        <div class="vehicle-image-container">
          <img src="../images/ui/team_icon.png" alt="Vehicle" id="vehicle-img">
          <div class="vehicle-overlay">
            <i class="fas fa-cog"></i>
          </div>
        </div>
        <div class="vehicle-info">
          <h4 id="vehicle-name">Loading Vehicle...</h4>
          <p id="vehicle-terrain">Terrain: Loading...</p>
          <div class="vehicle-stats">
            <span class="stat-item">
              <i class="fas fa-shield-alt"></i>
              <span id="vehicle-defense">0</span>
            </span>
            <span class="stat-item">
              <i class="fas fa-bolt"></i>
              <span id="vehicle-speed">0</span>
            </span>
          </div>
        </div>
      </div>
      
      <div class="team-summary">
        <div class="summary-item">
          <i class="fas fa-users"></i>
          <span id="team-size">0</span> Creatures
        </div>
        <div class="summary-item">
          <i class="fas fa-heart"></i>
          <span id="team-hp">0</span> Total HP
        </div>
        <div class="summary-item">
          <i class="fas fa-star"></i>
          <span id="team-power">0</span> Power
        </div>
      </div>
      
      <div class="creature-grid" id="team-creatures">
        <div class="loading">Loading creatures...</div>
      </div>
      
      <div class="debug-info" id="debug-info">
        <strong>Debug Info:</strong><br>
        <span id="debug-status">Loading...</span>
      </div>
    </div>

    <div class="battle-card">
      <div class="battle-arena" id="battle-terrain">
        <div class="battle-content">
          <div class="battle-header-info">
            <div class="turn-counter">
              <i class="fas fa-clock"></i>
              Turn <span id="turn-number">1</span>
            </div>
            <div class="phase-indicator">
              <i class="fas fa-layer-group"></i>
              Phase <span id="phase-number">1</span>
            </div>
          </div>
          
          <div class="boss-health-container">
            <div class="boss-health">
              <div class="health-fill" id="boss-hp" style="width: 70%"></div>
              <div class="health-text">
                <span id="boss-hp-text">70%</span>
              </div>
            </div>
          </div>
          
          <div class="boss-info">
            <div class="boss-avatar">
              <img src="../images/games/battle/goblin-default.png" alt="Goblin Boss" id="boss-img">
              <div class="boss-level">Lv. 15</div>
            </div>
            <h2 id="terrain-name">Goblin Camp</h2>
            <p><strong>Boss:</strong> <span id="boss-name">Goblin Warrior</span></p>
            <div class="boss-stats">
              <span class="boss-stat">
                <i class="fas fa-fist-raised"></i>
                <span id="boss-attack">85</span>
              </span>
              <span class="boss-stat">
                <i class="fas fa-shield-alt"></i>
                <span id="boss-defense">45</span>
              </span>
            </div>
          </div>
          
          <div class="phase-requirements" id="phase-requirements">
            <div class="phase-requirement">
              <i class="fas fa-shield-alt"></i> Need 2 Tanks
            </div>
            <div class="phase-requirement">
              <i class="fas fa-heart"></i> Team HP > 200
            </div>
          </div>
          
          <div class="battle-effects" id="battle-effects"></div>
        </div>
      </div>
    </div>

    <div class="battle-card">
      <div class="battle-header">
        <img src="../images/ui/hud/hud_games.png" alt="Actions">
        <h3>Battle Actions</h3>
        <div class="action-counter">
          <i class="fas fa-dice"></i>
          <span id="action-points">3</span> AP
        </div>
      </div>
      
      <div class="action-grid">
        <button class="action-btn primary" data-action="ability" data-cost="1">
          <i class="fas fa-bolt"></i>
          <div class="action-content">
            <div class="action-name">Ability Charge</div>
            <div class="action-cost">1 AP</div>
          </div>
        </button>
        <button class="action-btn special" data-action="breaker" data-cost="2">
          <i class="fas fa-fire"></i>
          <div class="action-content">
            <div class="action-name">Boss Breaker</div>
            <div class="action-cost">2 AP</div>
          </div>
        </button>
        <button class="action-btn utility" onclick="Battle.showModal('team-modal')">
          <i class="fas fa-exchange-alt"></i>
          <div class="action-content">
            <div class="action-name">Swap Creatures</div>
            <div class="action-cost">Free</div>
          </div>
        </button>
        <button class="action-btn utility" onclick="Battle.showModal('vehicle-modal')">
          <i class="fas fa-car"></i>
          <div class="action-content">
            <div class="action-name">Vehicle Skills</div>
            <div class="action-cost">1 AP</div>
          </div>
        </button>
      </div>

      <div class="quick-actions">
        <button class="quick-btn" data-action="auto">
          <i class="fas fa-play"></i>
          Auto Battle
        </button>
        <button class="quick-btn" data-action="pause">
          <i class="fas fa-pause"></i>
          Pause
        </button>
        <button class="quick-btn" data-action="reset">
          <i class="fas fa-redo"></i>
          Reset
        </button>
      </div>

      <div class="battle-log-container">
        <div class="log-header">
          <h4>Battle Log</h4>
          <button class="log-clear">
            <i class="fas fa-trash"></i>
          </button>
        </div>
        <div id="battle-log" class="battle-log">
          <div class="log-entry system">Battle started!</div>
          <div class="log-entry enemy">Goblin Warrior is charging...</div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal" id="team-modal">
    <h3>
      <i class="fas fa-exchange-alt"></i>
      Swap Creatures 
      <button class="btn-close">×</button>
    </h3>
    <div class="modal-content">
      <div class="swap-instructions">
        <i class="fas fa-info-circle"></i>
        Click on a creature to swap it with your current team member
      </div>
      <div class="creature-grid" id="available-creatures">
        <div class="loading">Loading available creatures...</div>
      </div>
    </div>
  </div>

  <div class="modal" id="vehicle-modal">
    <h3>
      <i class="fas fa-car"></i>
      Vehicle Skills 
      <button class="btn-close">×</button>
    </h3>
    <div class="modal-content">
      <div class="vehicle-skill-info">
        <div class="skill-description">
          <i class="fas fa-lightbulb"></i>
          Vehicle skills provide powerful terrain-specific abilities
        </div>
      </div>
      <div class="action-grid" id="vehicle-skills">
        <div class="loading">Loading vehicle skills...</div>
      </div>
    </div>
  </div>

  <div class="modal" id="result-modal">
    <h3 id="result-title">
      <i class="fas fa-trophy"></i>
      Battle Result 
      <button class="btn-close">×</button>
    </h3>
    <div id="result-content">
      <div class="result-summary">
        <div class="result-icon">
          <i class="fas fa-crown"></i>
        </div>
        <h4>Victory!</h4>
        <p>You defeated Ancient Golem!</p>
      </div>
      <div class="rewards-section">
        <h5>Rewards Earned:</h5>
        <div class="reward-items">
          <div class="reward-item">
            <i class="fas fa-coins"></i>
            <span>250 Intel</span>
          </div>
          <div class="reward-item">
            <i class="fas fa-gem"></i>
            <span>1 Crushie Gem</span>
          </div>
          <div class="reward-item">
            <i class="fas fa-star"></i>
            <span>+50 Experience</span>
          </div>
        </div>
      </div>
      <div class="result-actions">
        <button class="result-btn primary" onclick="Battle.nextBattle()">
          <i class="fas fa-arrow-right"></i>
          Continue
        </button>
        <button class="result-btn secondary" onclick="Battle.hideModal('result-modal')">
          <i class="fas fa-home"></i>
          Return to Hub
        </button>
      </div>
    </div>
  </div>

  <div class="battle-effects-overlay" id="battle-effects-overlay"></div>
  <div class="tooltip" id="tooltip">
    <div class="tooltip-content"></div>
  </div>
  <div class="notification-container" id="notification-container"></div>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
  <script src="../../api/tables.js"></script>
  <script src="../battle/data.js"></script>
  <script src="battle.js"></script>
  <script src="ui.js"></script>
  <script src="actions.js"></script>
  <script src="data.js"></script>
  <script>
    $(document).ready(function() {
      Battle.init();
    });
  </script>
</body>
</html> 