// BlastWaveFX.js
// Reusable effect: expanding, fading shockwave ring ("blast wave")
// Usage: new window.BlastWaveFX(stage, { x, y, color, radius, lineWidth, duration })

function BlastWaveFX(parent, options) {
    options = options || {};
    this.parent = parent;
    this.x = options.x || 0;
    this.y = options.y || 0;
    this.color = typeof options.color === 'number' ? options.color : 0x00FFFF;
    this.radius = options.radius || 80;
    this.lineWidth = options.lineWidth || 6;
    this.duration = options.duration || 500; // ms
    this._startTime = null;
    this._graphics = new PIXI.Graphics();
    this._graphics.x = this.x;
    this._graphics.y = this.y;
    this.parent.addChild(this._graphics);
    this._running = false;
    this._boundUpdate = this.update.bind(this);
    this.start();
}

BlastWaveFX.prototype.start = function() {
    if (!this._running) {
        this._running = true;
        this._startTime = performance.now();
        requestAnimationFrame(this._boundUpdate);
    }
};

BlastWaveFX.prototype.update = function(now) {
    if (!this._running) return;
    var elapsed = now - this._startTime;
    var progress = Math.min(elapsed / this.duration, 1);
    var currentRadius = 10 + (this.radius - 10) * progress;
    var alpha = 0.7 * (1 - progress);
    this._graphics.clear();
    this._graphics.lineStyle(this.lineWidth, this.color, alpha);
    this._graphics.drawCircle(0, 0, currentRadius);
    if (progress < 1) {
        requestAnimationFrame(this._boundUpdate);
    } else {
        this.destroy();
    }
};

BlastWaveFX.prototype.destroy = function() {
    this._running = false;
    if (this._graphics.parent) this._graphics.parent.removeChild(this._graphics);
    this._graphics.destroy();
};

if (typeof window !== 'undefined') {
    window.BlastWaveFX = BlastWaveFX;
} 