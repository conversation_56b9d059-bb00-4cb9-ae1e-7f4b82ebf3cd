// Pulse Effect
class PulseEffect {
    constructor() {
        this.activePulses = new Map(); // Track active pulse animations
    }

    // Create pulse effect with customizable rate
    create(target, options = {}) {
        const {
            pulseRate = 1, // Pulses per second (default: 1 pulse per second)
            intensity = 0.2, // Scale intensity (default: 0.2)
            originalScale = target.scale.x, // Original scale to return to
            continuous = true, // Whether to pulse continuously or just once
            startTime = Date.now() // Start time for animation continuity
        } = options;

        // Stop any existing pulse on this target
        this.stop(target);

        const animationId = `pulse_${Date.now()}_${Math.random()}`;
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = (elapsed / 1000) * pulseRate; // Convert to seconds and apply rate
            
            if (!continuous && progress >= 1) {
                target.scale.set(originalScale);
                this.activePulses.delete(target);
                return;
            }
            
            // Use modulo for continuous pulsing, or direct progress for single pulse
            const pulseProgress = continuous ? progress % 1 : Math.min(progress, 1);
            const scale = originalScale + Math.sin(pulseProgress * Math.PI * 2) * intensity;
            target.scale.set(scale);
            
            // Store animation reference for potential stopping
            this.activePulses.set(target, {
                animationId,
                animationFrame: requestAnimationFrame(animate),
                startTime: startTime // Store start time for potential restoration
            });
        };
        
        animate();
        return animationId;
    }

    // Create a quick single pulse (backward compatibility)
    createSinglePulse(target, duration = 1000) {
        const pulseRate = 1000 / duration; // Convert duration to pulse rate
        return this.create(target, {
            pulseRate: pulseRate,
            continuous: false
        });
    }

    // Stop pulsing for a specific target
    stop(target) {
        const pulseData = this.activePulses.get(target);
        if (pulseData) {
            cancelAnimationFrame(pulseData.animationFrame);
            this.activePulses.delete(target);
        }
    }

    // Stop all active pulses
    stopAll() {
        for (const [target, pulseData] of this.activePulses) {
            cancelAnimationFrame(pulseData.animationFrame);
        }
        this.activePulses.clear();
    }

    // Get active pulse count
    getActiveCount() {
        return this.activePulses.size;
    }
} 