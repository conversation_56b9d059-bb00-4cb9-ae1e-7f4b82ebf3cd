// Game Over Effect
class GameOverEffect {
    constructor() {
        // No dependencies needed for this effect
    }

    // Create game over effect
    create(stage, result) {
        const gameOverText = new PIXI.Text(result, {
            fontFamily: 'Arial',
            fontSize: 48,
            fill: 0xFF0000,
            stroke: 0xFFFFFF,
            strokeThickness: 4
        });
        
        gameOverText.x = stage.width / 2 - gameOverText.width / 2;
        gameOverText.y = stage.height / 2 - gameOverText.height / 2;
        gameOverText.alpha = 0;
        
        stage.addChild(gameOverText);
        
        // Animate game over effect
        let alpha = 0;
        
        const animate = () => {
            alpha += 0.02;
            
            gameOverText.alpha = alpha;
            
            if (alpha < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
} 