// Three.js-only transition effect for map navigation
// Usage: window.playThreeJsMapTransitionEffect({ mesh, camera, scene, type, onComplete })

// Helper to create a white border rectangle around a mesh
function createBorderRectangle(mesh, color, thickness) {
  color = typeof color !== 'undefined' ? color : 0xffffff;
  thickness = typeof thickness !== 'undefined' ? thickness : 2;
  var size = (mesh.geometry.parameters && mesh.geometry.parameters.width) || (mesh.geometry.boundingBox && mesh.geometry.boundingBox.max.x * 2) || 1;
  var half = size / 2;
  var z = mesh.position.z + 11; // Slightly above the mesh
  var points = [
    new THREE.Vector3(-half, -half, z), new THREE.Vector3(half, -half, z),
    new THREE.Vector3(half, -half, z), new THREE.Vector3(half, half, z),
    new THREE.Vector3(half, half, z), new THREE.Vector3(-half, half, z),
    new THREE.Vector3(-half, half, z), new THREE.Vector3(-half, -half, z)
  ];
  var geometry = new THREE.BufferGeometry().setFromPoints(points);
  var material = new THREE.LineBasicMaterial({ color: color, linewidth: thickness });
  var border = new THREE.LineSegments(geometry, material);
  border.position.copy(mesh.position);
  border.position.z = z;
  return border;
}

// Helper to create a fullscreen black plane for fade
function createFadePlane(camera, color) {
  color = typeof color !== 'undefined' ? color : 0x000000;
  var width = camera.right - camera.left;
  var height = camera.top - camera.bottom;
  var geometry = new THREE.PlaneGeometry(width, height);
  var material = new THREE.MeshBasicMaterial({ color: color, transparent: true, opacity: 0, depthTest: false });
  var plane = new THREE.Mesh(geometry, material);
  plane.position.set(0, 0, camera.position.z - 1); // In front of everything
  return plane;
}

// Animate property helper
function animateProperty(obj, prop, from, to, duration, onUpdate, onComplete) {
  var start = Date.now();
  function animate() {
    var now = Date.now();
    var t = Math.min((now - start) / duration, 1);
    obj[prop] = from + (to - from) * t;
    if (onUpdate) onUpdate(obj[prop], t);
    if (t < 1) {
      requestAnimationFrame(animate);
    } else {
      if (onComplete) onComplete();
    }
  }
  animate();
}

function animateCameraPositionAndZoom(camera, from, to, fromZoom, toZoom, duration, onUpdate, onComplete) {
  var start = Date.now();
  function animate() {
    var now = Date.now();
    var t = Math.min((now - start) / duration, 1);
    camera.position.x = from.x + (to.x - from.x) * t;
    camera.position.y = from.y + (to.y - from.y) * t;
    camera.zoom = fromZoom + (toZoom - fromZoom) * t;
    camera.updateProjectionMatrix();
    if (onUpdate) onUpdate(t);
    if (t < 1) {
      requestAnimationFrame(animate);
    } else {
      if (onComplete) onComplete();
    }
  }
  animate();
}

// Main transition effect
function playThreeJsMapTransitionEffect(params) {
  // params: { mesh, camera, scene, type, onComplete, fromMesh, toMesh, fadeOnly }
  var mesh = params.mesh;
  var camera = params.camera;
  var scene = params.scene;
  var type = params.type;
  var onComplete = params.onComplete;
  var fromMesh = params.fromMesh;
  var toMesh = params.toMesh;
  var fadeOnly = params.fadeOnly;

  // Check player options for transition preference
  var useSimpleTransitions = false;
  if (typeof window.useSimpleTransitions === 'function') {
    useSimpleTransitions = window.useSimpleTransitions();
  } else if (typeof playerOptions !== 'undefined' && playerOptions.mapTransition) {
    useSimpleTransitions = playerOptions.mapTransition === 'simple';
  }

  // Helper to get mesh center
  function getMeshCenter(m) {
    if (!m) return { x: 0, y: 0, z: camera.position.z };
    var pos = m.position || { x: 0, y: 0, z: 0 };
    return { x: pos.x, y: pos.y, z: camera.position.z };
  }

  // Remove all clickable meshes before starting the zoom (for a clean transition)
  // --- Disabled: We want textures/meshes to remain visible during zoom effects ---
  /*
  if (typeof window.getClickableTiles === 'function' && typeof window.getScene === 'function') {
    var scene = params.scene || window.getScene();
    var tiles = window.getClickableTiles();
    if (tiles && scene) {
      for (var i = 0; i < tiles.length; i++) {
        scene.remove(tiles[i]);
        if (tiles[i].material && tiles[i].material.map) tiles[i].material.map.dispose();
        if (tiles[i].material) tiles[i].material.dispose();
        if (tiles[i].geometry) tiles[i].geometry.dispose();
      }
      // Don't clear clickableTiles array here, let clearScene do it after new view
    }
  }
  */

  // Fade-only transition (for world<->locale or when simple transitions are enabled)
  if (fadeOnly || type === 'fade-only' || useSimpleTransitions) {
    var fadePlane = createFadePlane(camera);
    scene.add(fadePlane);
    animateProperty(fadePlane.material, 'opacity', 0, 1, 180, null, function() {
      if (onComplete) onComplete();
      setTimeout(function() {
        animateProperty(fadePlane.material, 'opacity', 1, 0, 180, null, function() {
          scene.remove(fadePlane);
        });
      }, 120);
    });
    return;
  }

  // --- ZOOM EFFECT: Reverse zooming using nav buttons (zone->world, locale->zone) ---
  // This effect shrinks the current grid view to 80% into the center and fades to black
  if (type === 'zoom-out-fade') {
    var origZoom = camera.zoom;
    var origPos = camera.position.clone();
    var center = { x: 0, y: 0, z: camera.position.z };
    var shrinkZoom = origZoom * 0.8; // Shrink to 80%
    // Animate zoom out to 80% and fade to black
    var fadePlane = createFadePlane(camera);
    scene.add(fadePlane);
    // Animate both zoom and fade in parallel
    var duration = 400;
    var fadeStarted = false;
    function animateBoth() {
      var start = Date.now();
      function step() {
        var now = Date.now();
        var t = Math.min((now - start) / duration, 1);
        // Ease in for smoothness
        var ease = t < 0.5 ? 2*t*t : -1+(4-2*t)*t;
        camera.zoom = origZoom + (shrinkZoom - origZoom) * ease;
        camera.position.x = origPos.x + (center.x - origPos.x) * ease;
        camera.position.y = origPos.y + (center.y - origPos.y) * ease;
        camera.updateProjectionMatrix();
        if (t > 0.5 && !fadeStarted) {
          fadeStarted = true;
          animateProperty(fadePlane.material, 'opacity', 0, 1, duration/2);
        }
        if (t < 1) {
          requestAnimationFrame(step);
        } else {
          // End state: fully faded, shrunken
          camera.zoom = origZoom;
          camera.position.x = origPos.x;
          camera.position.y = origPos.y;
          camera.updateProjectionMatrix();
          if (onComplete) onComplete();
          // Fade back in
          setTimeout(function() {
            animateProperty(fadePlane.material, 'opacity', 1, 0, 180, null, function() {
              scene.remove(fadePlane);
            });
          }, 100);
        }
      }
      step();
    }
    animateBoth();
    return;
  }

  // Default: legacy behavior (zoom in to mesh, fade, restore)
  // 1. Draw border
  var border = mesh ? createBorderRectangle(mesh) : null;
  if (border) scene.add(border);

  // 2. Blink border (2x)
  var blinkCount = 0;
  function blink() {
    if (border) border.visible = !border.visible;
    blinkCount++;
    if (blinkCount < 4) {
      setTimeout(blink, 120);
    } else {
      if (border) border.visible = true;
      // 3. Camera pan and zoom
      var origZoom = camera.zoom;
      var origPos = camera.position.clone();
      var targetZoom = origZoom * 2.2;
      var targetPos = mesh ? mesh.position.clone() : origPos.clone();
      targetPos.z = camera.position.z;
      animateCameraPositionAndZoom(camera, origPos, targetPos, origZoom, targetZoom, 400, null, function() {
        // 4. Fade to black
        var fadePlane = createFadePlane(camera);
        scene.add(fadePlane);
        animateProperty(fadePlane.material, 'opacity', 0, 1, 320, null, function() {
          // 5. Restore camera zoom and position BEFORE switching view
          camera.zoom = origZoom;
          camera.position.x = origPos.x;
          camera.position.y = origPos.y;
          camera.updateProjectionMatrix();
          // 6. Switch view
          if (onComplete) onComplete();
          // Remove border immediately after switching view
          if (border) scene.remove(border);
          // 7. Fade in
          setTimeout(function() {
            animateProperty(fadePlane.material, 'opacity', 1, 0, 320, null, function() {
              scene.remove(fadePlane);
              // (border removal here is now redundant)
            });
          }, 200);
        });
      });
    }
  }
  blink();
}

window.playThreeJsMapTransitionEffect = playThreeJsMapTransitionEffect;

// Utility: Unified transition type logic
function getMapTransitionType(fromView, toView) {
  // Always use the toggle
  var useSimple = (typeof window.useSimpleTransitions === 'function') ? window.useSimpleTransitions() : false;
  if (useSimple) return 'fade-only';
  // Forward navigation: world->zone, zone->locale, world->locale
  if ((fromView === 'worlds' && toView === 'zones') ||
      (fromView === 'zones' && toView === 'locales') ||
      (fromView === 'worlds' && toView === 'locales')) {
    return 'zoom-in';
  }
  // Backward navigation: locale->zone, zone->world, locale->world
  if ((fromView === 'locales' && toView === 'zones') ||
      (fromView === 'zones' && toView === 'worlds') ||
      (fromView === 'locales' && toView === 'worlds')) {
    return 'zoom-out-fade';
  }
  // Fallback
  return 'fade-only';
}
window.getMapTransitionType = getMapTransitionType; 