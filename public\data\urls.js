var domain_url = "https://express-crushie.herokuapp.com";
var url_ipfs = "https://ipfs.io/ipfs/";
//var wax_endpoint = 'https://wax.api.atomicassets.io';

// List of Atomic Assets API nodes
const atomicNodes = [
  { id: 17, name: '3DKRender', url: 'https://atomic.3dkrender.com' },
  { id: 9, name: 'EOSAmsterdam', url: 'https://wax-aa.eu.eosamsterdam.net' },
  { id: 44, name: 'A-DEX BP', url: 'https://atomic-wax.a-dex.xyz' },
  { id: 19, name: 'AlcorExchange', url: 'https://wax-atomic.alcor.exchange' },
  { id: 28, name: 'We<PERSON>an', url: 'https://atomic-wax-mainnet.wecan.dev' },
  { id: 24, name: 'CryptoLions🦁', url: 'https://atomic-api.wax.cryptolions.io' },
  { id: 12, name: 'dapplica', url: 'https://aa.dapplica.io' },
  { id: 15, name: 'EOS Authority', url: 'https://aa-api-wax.eosauthority.com' },
  { id: 7, name: 'eosDAC', url: 'https://wax-aa.eosdac.io' },
  { id: 4, name: 'EOSphere Guild', url: 'https://wax-atomic-api.eosphere.io' },
  { id: 8, name: 'EOS Rio 💙', url: 'https://atomic.wax.eosrio.io' },
  { id: 10, name: 'NeftyGuild', url: 'https://aa-wax-public1.neftyblocks.com' },
  { id: 16, name: 'Taco', url: 'https://atomic-wax.tacocrypto.io' },
  { id: 18, name: 'WaxDAO BP', url: 'https://aa.waxdaobp.io' },
  { id: 6, name: 'WAXUSA', url: 'https://wax.eosusa.io' },
  { id: 42, name: 'QaraqolBlock', url: 'https://atomic-wax.qaraqol.com' },
  { id: 27, name: 'WAX.Eastern', url: 'https://api.waxeastern.cn' },
  { id: 11, name: 'Hive BP', url: 'https://atomic.hivebp.io' }
];

var wax_endpoint = 'https://aa.dapplica.io/'; // fallback default, will be set dynamically
var wax_api = wax_endpoint + '/atomicassets/v1/assets?collection_name=cutecrushies&schema_name=';
var wax_api2 = '&page=1&limit=100&order=desc&sort=asset_id';
var url_get_asset = wax_endpoint + '/atomicassets/v1/assets/';
var url_vehicles, url_houses, url_creatures, url_items, url_food, url_coins, url_dust, url_player;
var selectedSchema;

async function createUrls(wallet) {
  url_vehicles = wax_api + 'vehicles&owner=' + wallet + wax_api2;
  url_houses = wax_api + 'houses&owner=' + wallet + wax_api2;
  url_creatures = wax_api + 'creature&owner=' + wallet + wax_api2;
  url_items = wax_api + 'items&owner=' + wallet + wax_api2;
  url_food = wax_api + 'foods&owner=' + wallet + wax_api2;
  url_coins = wax_api + 'coins&owner=' + wallet + wax_api2;
  url_dust = 'https://api.wax.alohaeos.com/v2/state/get_tokens?limit=250&account=' + wallet;
  url_player = domain_url + '/players/' + wallet;
}

function setWaxEndpointFromPlayerOptions() {
  // Use playerOptions.atomicNodeUrl if available, else fallback
  if (typeof playerOptions !== 'undefined' && playerOptions.atomicNodeUrl) {
    wax_endpoint = playerOptions.atomicNodeUrl.endsWith('/') ? playerOptions.atomicNodeUrl : playerOptions.atomicNodeUrl + '/';
  } else {
    wax_endpoint = 'https://aa.dapplica.io/';
  }
  wax_api = wax_endpoint + '/atomicassets/v1/assets?collection_name=cutecrushies&schema_name=';
  url_get_asset = wax_endpoint + '/atomicassets/v1/assets/';
  // If wallet is set, update all URLs
  if (typeof wax !== 'undefined' && wax.userAccount) {
    createUrls(wax.userAccount);
  }
}
// On load, set endpoint from player options if available
if (typeof playerOptions !== 'undefined' && playerOptions.atomicNodeUrl) {
  setWaxEndpointFromPlayerOptions();
}
