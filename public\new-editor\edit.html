<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Map Editor 3D - Cute Crushies</title>
  <link href="../css/animations.css" rel="stylesheet">
  <link href="../css/style.css" rel="stylesheet">
  <link href="../css/tiles.css" rel="stylesheet">
  <link href="../css/info-modal.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/common-pixel" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/ttvtechprecomput" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/commodore-64-pixelized" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="editor.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.153.0/build/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
</head>
<body>
<div class="editor-flex-container">
  <div id="threejs-map-panel">
    <div id="threejs-map-container">
      <!-- Navigation label will be added here by JavaScript -->
    </div>
  </div>
  <div id="editor-console">
    <div class="editor-console-section">
      <h3>Tile Editor</h3>
      
      <!-- Mode Selection -->
      <div class="mode-selector mb-3">
        <label class="form-label"><strong>Editor Mode:</strong></label>
        <div class="mode-buttons">
          <button type="button" class="mode-button btn-blue active" id="paintModeBtn" data-mode="paint">Paint Mode</button>
          <button type="button" class="mode-button btn-unselected" id="editModeBtn" data-mode="edit">Edit Mode</button>
        </div>
      </div>
      
      <div id="paintModeInfo" class="alert alert-info" style="display: block;">
        <small>Paint Mode: Set tile properties below, then click tiles to apply them.</small>
      </div>
      <div id="editModeInfo" class="alert alert-warning" style="display: none;">
        <small>Edit Mode: Click any tile to load its data for editing.</small>
      </div>
      
      <label for="tileName">Tile Name</label>
      <input type="text" id="tileName" class="form-control" placeholder="Enter tile name">
      <!-- Tile preview image -->
      <div style="text-align:center; margin: 10px 0;">
        <img id="tileTypePreview" src="../images/forest_world/grass.png" alt="Tile Preview" style="width:48px;height:48px;border:2px solid #00fff0;background:#222;border-radius:6px;box-shadow:0 2px 8px #0002;" />
      </div>
      <label for="tileType">Tile Type</label>
      <select id="tileType" class="form-select">
        <option value="grassplains">Grass Plains</option>
        <option value="water">Water</option>
        <option value="forest">Forest</option>
        <option value="crushieforest">Crushie Forest</option>
        <option value="castle">Castle</option>
        <option value="ruins">Ruins</option>
        <option value="town">Town</option>
        <option value="tr_water">Tropical Water</option>
        <option value="tr_castle">Tropical Castle</option>
        <option value="tr_island">Tropical Island</option>
        <option value="tr_waterland">Tropical Waterland</option>
        <option value="ds_dirt">Desert Dirt</option>
        <option value="ds_castle">Desert Castle</option>
        <option value="ds_dunes">Desert Dunes</option>
        <option value="ds_ruins">Desert Ruins</option>
        <option value="ds_town">Desert Town</option>
        <option value="sp_normal">Space Normal</option>
        <option value="sp_gas1">Space Gas</option>
        <option value="sp_debris">Space Debris</option>
        <option value="sp_station1">Space Station</option>
        <option value="sp_gplanet1">Space Green Planet</option>
        <option value="sp_dplanet1">Space Desert Planet</option>
        <option value="sp_iplanet1">Space Ice Planet</option>
        <option value="sp_rplanet1">Space Red Planet</option>
      </select>
      <label for="tileTerrain">Terrain Type</label>
      <select id="tileTerrain" class="form-select">
        <option value="land">Land</option>
        <option value="water">Water</option>
        <option value="space">Space</option>
      </select>
      <div id="editModeUpdateBtnContainer" style="display:none; margin-bottom:10px;">
        <button type="button" class="btn btn-blue" id="updateTileBtn">Update This Tile</button>
      </div>
      <div id="editor-changes-btns" style="display: flex; flex-wrap: wrap; gap: 8px;">
        <button class="btn btn-unselected" id="clearSelectionBtn">Clear Selection</button>
        <button class="btn btn-green" id="showChangesBtn">Review Changes</button>
        <button class="btn btn-unselected" id="saveJsonBtn">Save as JSON</button>
        <button class="btn btn-unselected" id="saveAllZonesBackupBtn">Backup All Zones</button>
        <button class="btn btn-red" id="clearAllChangesBtn">Clear All</button>
      </div>
    </div>
    <div class="editor-console-section">
      <h4>Selected Tile</h4>
      <div id="selectedTileInfo">No tile selected.</div>
    </div>
    <div class="editor-console-section">
      <h4>Pending Changes</h4>
      <div id="pendingChangesList" style="max-height: 120px; overflow-y: auto; border: 1px solid #444; border-radius: 4px; padding: 8px; background: #181a1b;">No changes yet.</div>
    </div>
  </div>
</div>

<!-- Modal for reviewing changes -->
<div class="modal fade" id="editor-changes-modal" tabindex="-1" aria-labelledby="editor-changes-modal-label" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editor-changes-modal-label">Review Map Changes</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="editor-changes-modal-content">
          
        </div>
        <div id="editor-changes-row" class="mt-3">
          <h6>Row to be updated:</h6>
          <pre id="editor-changes-row-json"></pre>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-unselected" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-blue" id="confirmChangesBtn">Confirm Changes</button>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="mapUtils.js"></script>
<script src="mapData.js"></script>
<script src="mapRenderer.js"></script>
<script src="mapUI-core.js"></script>
<script src="mapUI-ui.js"></script>
<script src="edit.js"></script>
</body>
</html> 