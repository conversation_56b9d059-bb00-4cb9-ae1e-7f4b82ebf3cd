class ForestScavengerGoblinManager {
  constructor(game) {
    this.game = game;
    this.goblins = [];
    this.sprites = [];
    this.isPursuing = true;
  }

  init() {
    this.goblins = [];
    this.sprites = [];
    this.isPursuing = true;
    
    const levelConfig = this.game.levelConfigs[this.game.level] || this.game.levelConfigs[1];
    const numGoblins = levelConfig.goblins;
    
    // Place goblins away from player and safe zones
    for (let i = 0; i < numGoblins; i++) {
      let position = this.getValidGoblinPosition();
      this.goblins.push({ 
        x: position.x, 
        y: position.y, 
        dir: 'down',
        lastKnownPlayerPos: null,
        wanderDirection: this.getRandomDirection()
      });
    }
  }

  getValidGoblinPosition() {
    let attempts = 0;
    const maxAttempts = 50;
    
    while (attempts < maxAttempts) {
      let x = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
      let y = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
      
      // Check if position is valid (not in safe zone, not occupied, not too close to player)
      if (this.game.gridManager.isWalkable(x, y) && 
          !this.game.safeZoneManager.isInSafeZone(x, y) &&
          !this.game.gridManager.isOccupied(x, y, true, true, true)) {
        
        const playerPos = this.game.playerManager.getPosition();
        const distanceToPlayer = this.game.gridManager.getDistance({ x, y }, playerPos);
        
        // Ensure goblins start at least 3 tiles away from player
        if (distanceToPlayer >= 3) {
          return { x, y };
        }
      }
      attempts++;
    }
    
    // Fallback: place in corner
    return { x: 1, y: 1 };
  }

  getRandomDirection() {
    const directions = ['up', 'down', 'left', 'right'];
    return directions[Math.floor(Math.random() * directions.length)];
  }

  drawGoblins() {
    // Remove only goblin sprites, not all stage children
    this.sprites.forEach(sprite => {
      if (sprite && sprite.parent) {
        sprite.parent.removeChild(sprite);
      }
    });
    this.sprites = [];
    
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const goblinSize = 10 * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    
    // Draw new sprites
    this.goblins.forEach(goblin => {
      let gobSprite = new PIXI.Sprite(this.game.images.goblin[goblin.dir]);
      gobSprite.x = goblin.x * scaledTileSize + (scaledTileSize - goblinSize) / 2 + gridOffsetX;
      gobSprite.y = goblin.y * scaledTileSize + this.game.hudHeight + (scaledTileSize - goblinSize) / 2 + gridOffsetY;
      gobSprite.width = goblinSize;
      gobSprite.height = goblinSize;
      this.game.app.stage.addChild(gobSprite);
      this.sprites.push(gobSprite);
    });
  }

  moveGoblins() {
    if (this.game.gameOver || this.game.isCountdownActive) return;
    
    this.goblins.forEach((goblin, idx) => {
      // Check if goblin is adjacent to player
      const dx = Math.abs(goblin.x - this.game.playerManager.player.x);
      const dy = Math.abs(goblin.y - this.game.playerManager.player.y);
      
      if (dx <= 1 && dy <= 1) {
        // Goblin caught player
        this.handleGoblinEncounter(goblin);
        return;
      }
      
      // Move goblin based on current state
      if (this.isPursuing && !this.game.isInSafeZone) {
        this.moveTowardsPlayer(goblin);
      } else {
        this.wander(goblin);
      }
    });
    
    // Use optimized update to preserve pulse effects
    this.game.updateMovingEntities();
  }

  moveTowardsPlayer(goblin) {
    const playerPos = this.game.playerManager.getPosition();
    const dirs = [
      { dx: 0, dy: -1, dir: 'up' },
      { dx: 0, dy: 1, dir: 'down' },
      { dx: -1, dy: 0, dir: 'left' },
      { dx: 1, dy: 0, dir: 'right' }
    ];
    
    // Calculate distance to player for each possible move
    let bestMove = null;
    let bestDistance = Infinity;
    let validMoves = [];
    
    dirs.forEach(d => {
      let nx = goblin.x + d.dx, ny = goblin.y + d.dy;
      if (this.game.gridManager.isWalkable(nx, ny) && 
          !this.game.safeZoneManager.isInSafeZone(nx, ny) &&
          !this.game.gridManager.isOccupied(nx, ny, true, true, false)) { // Do not exclude materials
        
        let distanceToPlayer = this.game.gridManager.getDistance({ x: nx, y: ny }, playerPos);
        validMoves.push({ ...d, distance: distanceToPlayer });
        
        if (distanceToPlayer < bestDistance) {
          bestDistance = distanceToPlayer;
          bestMove = d;
        }
      }
    });
    
    // 90% chance to move toward player, 10% chance for random movement
    if (bestMove && Math.random() < 0.9) {
      goblin.x += bestMove.dx;
      goblin.y += bestMove.dy;
      goblin.dir = bestMove.dir;
    } else if (validMoves.length > 0) {
      // Random movement as fallback
      let randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];
      goblin.x += randomMove.dx;
      goblin.y += randomMove.dy;
      goblin.dir = randomMove.dir;
    }
  }

  wander(goblin) {
    const dirs = [
      { dx: 0, dy: -1, dir: 'up' },
      { dx: 0, dy: 1, dir: 'down' },
      { dx: -1, dy: 0, dir: 'left' },
      { dx: 1, dy: 0, dir: 'right' }
    ];
    
    // Try to move in current wander direction
    let currentDir = dirs.find(d => d.dir === goblin.wanderDirection);
    if (currentDir) {
      let nx = goblin.x + currentDir.dx, ny = goblin.y + currentDir.dy;
      if (this.game.gridManager.isWalkable(nx, ny) && 
          !this.game.safeZoneManager.isInSafeZone(nx, ny) &&
          !this.game.gridManager.isOccupied(nx, ny, true, true, false)) { // Do not exclude materials
        goblin.x = nx;
        goblin.y = ny;
        goblin.dir = currentDir.dir;
      } else {
        // Change direction if blocked
        goblin.wanderDirection = this.getRandomDirection();
      }
    }
    
    // Occasionally change direction randomly
    if (Math.random() < 0.1) {
      goblin.wanderDirection = this.getRandomDirection();
    }
  }

  handleGoblinEncounter(goblin) {
    // Make goblin face the player
    if (goblin.x < this.game.playerManager.player.x) goblin.dir = 'right';
    else if (goblin.x > this.game.playerManager.player.x) goblin.dir = 'left';
    else if (goblin.y < this.game.playerManager.player.y) goblin.dir = 'down';
    else if (goblin.y > this.game.playerManager.player.y) goblin.dir = 'up';
    
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    const centerX = this.game.playerManager.player.x * scaledTileSize + scaledTileSize/2 + gridOffsetX;
    const centerY = this.game.playerManager.player.y * scaledTileSize + this.game.hudHeight + scaledTileSize/2 + gridOffsetY;
    
    if (window.VisualEffects) {
      // Create dramatic red explosion effect
      window.VisualEffects.createExplosion(centerX, centerY, 0xFF0000, 20, this.game.app.stage);
      
      // Add ripple effect in red
      window.VisualEffects.createRipple(centerX, centerY, this.game.app.stage, 0xFF0000);
      
      // Add game over effect
      window.VisualEffects.createGameOverEffect(this.game.app.stage, 'CAUGHT!');
    }
    
    this.game.gameOver = true;
    this.game.win = false;
    
    // Delay the game over modal to allow effects to be enjoyed
    setTimeout(() => {
      this.game.endGame('Caught by a goblin!');
    }, 1500);
  }

  checkGoblinEncounter() {
    for (let goblin of this.goblins) {
      const dx = Math.abs(goblin.x - this.game.playerManager.player.x);
      const dy = Math.abs(goblin.y - this.game.playerManager.player.y);
      if (dx === 0 && dy === 0) {
        this.handleGoblinEncounter(goblin);
        return;
      }
    }
  }

  resumePursuit() {
    this.isPursuing = true;
    // Goblins will immediately start moving toward the player
  }

  stopPursuit() {
    this.isPursuing = false;
    // Goblins will start wandering
  }
}

window.ForestScavengerGoblinManager = ForestScavengerGoblinManager; 