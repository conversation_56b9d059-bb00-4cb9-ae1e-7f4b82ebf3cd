@font-face {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: 'CrushieFont';
    src: url('../data/fonts/crushie-font.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

.dialog-container {
    position: fixed;
    z-index: 9999;
    display: none;
    pointer-events: none;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
}

.dialog-window {
    background-color: #fee28a;
    min-width: 250px;
    max-width: 700px;
    pointer-events: auto;
    font-family: 'CrushieFont', monospace;
    font-size: 16px;
    color: #333;
    line-height: 1.5;
    transform: scale(0.8);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;
    z-index: 10000;
    border-width: 30px;
    border-style: solid;
    border-image: url('../images/ui/bg/dialog.png') 30 round;
    padding: 0;
    box-shadow: none;
    border-radius: 0;
    display: flex;
    flex-direction: column;
}

.dialog-window.active {
    transform: scale(1);
    opacity: 1;
}

.dialog-header {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    gap: 20px;
    position: relative;
    z-index: 2;
}

.dialog-character-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 120px;
}

.dialog-character-image {
    width: 80px;
    height: 80px;
    border: 2px solid #75450f;
    object-fit: cover;
    background: #fff;
}

.dialog-character-name { 
    font-size: 14px;
    color: #333; 
    text-align: center; 
    padding: 4px 8px; 
}

.dialog-content {
    flex: 1;
    padding: 20px;
    padding-left: 0;
    min-height: 80px;
    position: relative;
    z-index: 2;
}

.dialog-text {
    font-size: 14px;
    line-height: 1.6;
    word-wrap: break-word;
    color: #333;
    font-weight: 500; 
}

.dialog-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: -3em;
    position: relative;
    z-index: 2;
}

.dialog-next-btn {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border: 2px solid #333;
    border-radius: 4px;
    color: white;
    padding: 6px 12px;
    font-family: 'CrushieFont', monospace;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dialog-next-btn:hover {
    background: linear-gradient(135deg, #5ba0f2 0%, #4a90e2 100%);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.dialog-next-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.dialog-close-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: 2px solid #333;
    border-radius: 4px;
    color: white;
    padding: 6px 12px;
    font-family: 'CrushieFont', monospace;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dialog-close-btn:hover {
    background: linear-gradient(135deg, #f75c4c 0%, #e74c3c 100%);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.dialog-close-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Animation classes for different entry directions */
.dialog-slide-from-top {
    transform: translateY(-100vh) scale(0.8);
}

.dialog-slide-from-bottom {
    transform: translateY(100vh) scale(0.8);
}

.dialog-slide-from-left {
    transform: translateX(-100vw) scale(0.8);
}

.dialog-slide-from-right {
    transform: translateX(100vw) scale(0.8);
}

/* Position classes */
.dialog-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.dialog-top-left {
    top: 20px;
    left: 20px;
}

.dialog-top-right {
    top: 20px;
    right: 20px;
}

.dialog-bottom-left {
    bottom: 20px;
    left: 20px;
}

.dialog-bottom-right {
    bottom: 20px;
    right: 20px;
}

/* Multiple dialog positioning */
.dialog-multiple {
    margin: 10px;
}

.dialog-multiple:nth-child(1) {
    transform: translate(-50%, -50%) scale(0.9);
}

.dialog-multiple:nth-child(2) {
    transform: translate(-50%, -50%) scale(0.85);
}

.dialog-multiple:nth-child(3) {
    transform: translate(-50%, -50%) scale(0.8);
}

/* Responsive design */
@media (max-width: 768px) {
    .dialog-window {
        min-width: 250px;
        max-width: 90vw;
        margin: 10px;
    }
    
    .dialog-character-image {
        width: 40px;
        height: 40px;
    }
    
    .dialog-text {
        font-size: 12px;
    }
}

/* Loading animation for typing effect */
.dialog-text.typing::after {
    content: '|';
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}