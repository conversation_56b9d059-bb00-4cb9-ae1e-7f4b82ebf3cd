const xpTable = {
    "1": 100,
    "2": 180,
    "3": 380,
    "4": 560,
    "5": 640,
    "6": 850,
    "7": 980,
    "8": 1010,
    "9": 1170,
    "10": 1240,
    "11": 1500,
    "12": 2250,
    "13": 3375,
    "14": 5063,
    "15": 7594,
    "16": 11391,
    "17": 17086,
    "18": 25629,
    "19": 38444,
    "20": 57666,
    "21": 86499,
    "22": 129748,
    "23": 194623,
    "24": 291934,
    "25": 437790,
    "26": 656685,
    "27": 985027,
    "28": 1477541,
    "29": 2216302,
    "30": 3316253
  };

  const rewardTable = [
  {
    level: 1,
    xp_required: 100,
    nectar_max: 3,
    credits_max: 3,
  },
  {
    level: 2,
    xp_required: 180,
    nectar_max: 4,
    credits_max: 3,
  },
  {
    level: 3,
    xp_required: 380,
    nectar_max: 4,
    credits_max: 4,
    token_reward: ["GXP", 30],
  },
  {
    level: 4,
    xp_required: 560,
    nectar_max: 4,
    credits_max: 4,
    adventure_gxp_bonus: 1,
    nap_rate_bonus: 0.1,
  },
  {
    level: 5,
    xp_required: 640,
    nectar_max: 5,
    credits_max: 5,
    adventure_gxp_bonus: 3,
    nap_rate_bonus: 0.1,
    nft_reward: ["rare"],
  },
  {
    level: 6,
    xp_required: 850,
    nectar_max: 5,
    credits_max: 5,
    adventure_gxp_bonus: 3,
    nap_rate_bonus: 0.1,
    token_reward: ["DUST", 100],
  },
  {
    level: 7,
    xp_required: 980,
    nectar_max: 5,
    credits_max: 6,
    adventure_gxp_bonus: 5,
    nap_rate_bonus: 0.1,
  },
  {
    level: 8,
    xp_required: 1010,
    nectar_max: 6,
    credits_max: 6,
    adventure_gxp_bonus: 5,
    nap_rate_bonus: 0.1,
  },
  {
    level: 9,
    xp_required: 1170,
    nectar_max: 6,
    credits_max: 7,
    adventure_gxp_bonus: 5,
    nap_rate_bonus: 0.1,
  },
  {
    level: 10,
    xp_required: 1240,
    nectar_max: 6,
    credits_max: 7,
    adventure_gxp_bonus: 10,
    nap_rate_bonus: 0.25,
    nft_reward: ["rare"],
  },
  {
    level: 11,
    xp_required: 1500,
    nectar_max: 7,
    credits_max: 7,
    adventure_gxp_bonus: 10,
    nap_rate_bonus: 0.25,
  },
  {
    level: 12,
    xp_required: 2250,
    nectar_max: 7,
    credits_max: 7,
    adventure_gxp_bonus: 10,
    nap_rate_bonus: 0.25,
    token_reward: ["GXP", 1500],
    nft_reward: ["rare"],
  },
  {
    level: 13,
    xp_required: 3375,
    nectar_max: 7,
    credits_max: 8,
    adventure_gxp_bonus: 10,
    nap_rate_bonus: 0.25,
  },
  {
    level: 14,
    xp_required: 5063,
    nectar_max: 8,
    credits_max: 8,
    adventure_gxp_bonus: 10,
    nap_rate_bonus: 0.25,
  },
  {
    level: 15,
    xp_required: 7594,
    nectar_max: 8,
    credits_max: 8,
    adventure_gxp_bonus: 15,
    nap_rate_bonus: 0.25,
    token_reward: ["DUST", 500],
    nft_reward: ["rare"],
  },
  {
    level: 16,
    xp_required: 11391,
    nectar_max: 8,
    credits_max: 9,
    adventure_gxp_bonus: 15,
    nap_rate_bonus: 0.25,
  },
  {
    level: 17,
    xp_required: 17086,
    nectar_max: 9,
    credits_max: 9,
    adventure_gxp_bonus: 15,
    nap_rate_bonus: 0.25,
    token_reward: ["GXP", 1500],
  },
  {
    level: 18,
    xp_required: 25629,
    nectar_max: 9,
    credits_max: 9,
    adventure_gxp_bonus: 15,
    nap_rate_bonus: 0.72,
  },
  {
    level: 19,
    xp_required: 38444,
    nectar_max: 9,
    credits_max: 10,
    adventure_gxp_bonus: 15,
    nap_rate_bonus: 0.72,
  },
  {
    level: 20,
    xp_required: 57666,
    nectar_max: 10,
    credits_max: 10,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.8,
    nft_reward: ["rare"],
  },
  {
    level: 21,
    xp_required: 86499,
    nectar_max: 10,
    credits_max: 11,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.8,
  },
  {
    level: 22,
    xp_required: 129748,
    nectar_max: 10,
    credits_max: 11,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.81,
    token_reward: ["GXP", 1500],
  },
  {
    level: 23,
    xp_required: 194623,
    nectar_max: 10,
    credits_max: 11,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.81,
  },
  {
    level: 24,
    xp_required: 291934,
    nectar_max: 11,
    credits_max: 11,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.81,
  },
  {
    level: 25,
    xp_required: 437790,
    nectar_max: 11,
    credits_max: 12,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.82,
    nft_reward: ["rare"],
  },
  {
    level: 26,
    xp_required: 656685,
    nectar_max: 12,
    credits_max: 12,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.82,
  },
  {
    level: 27,
    xp_required: 985027,
    nectar_max: 13,
    credits_max: 13,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.82,
    token_reward: ["GXP", 5000],
  },
  {
    level: 28,
    xp_required: 1477541,
    nectar_max: 14,
    credits_max: 13,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.84,
    nft_reward: ["rare"],
  },
  {
    level: 29,
    xp_required: 2216302,
    nectar_max: 14,
    credits_max: 15,
    adventure_gxp_bonus: 30,
    nap_rate_bonus: 0.84,
    nft_reward: ["rare"],
  },
  {
    level: 30,
    xp_required: 3316253,
    nectar_max: 15,
    credits_max: 15,
    adventure_gxp_bonus: 50,
    nap_rate_bonus: 0.88,
    token_reward: ["DUST", 2500],
    nft_reward: ["rare"],
  },
];


function getRequiredXP(level){
  if (level < 1) {
     return 1;
   }
  if (level < 1 || level > 30) {
     return 30;
   }
   return xpTable[level];
}

function getRewardForLevel(level){
  const levelRewards = rewardTable.find((entry) => entry.level === level);
  return levelRewards;
}

function getPlayerLevelByXP(xp) {
  let level = 1;  // Default to level 1 if xp is below the first level's XP requirement.
  for (let xpNeeded in xpTable) {
    if (xp >= xpTable[xpNeeded]) {
      level = parseInt(xpNeeded); // Update the level if the XP requirement is met.
    }
  }
  console.log('Level: ' + level) 
  return level;
}


module.exports = {
  getRequiredXP,
  getPlayerLevelByXP,
  getRewardForLevel
}
