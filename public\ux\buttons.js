const ICONS = {
  VIEW: '../images/ui/view_icon_small.png',
  CANCEL: '../images/ui/cancel_small_icon.png',
  REWARD: 'images/ui/reward_icon.png',
  PLUS: '../images/ui/plus.png',
  START: '../images/ui/start_icon.png',
  HOUSE: 'images/ui/house_icon_sm.png',
  DIALOG: '../images/ui/chat_icon.png'
};

// View Team Button
function createViewButton(team, displayTeamViewModalFunc) {
  return createStandardButton({
    text: 'View Team',
    icon: ICONS.VIEW,
    dataAttr: 'team-id',
    dataValue: team.team_id,
    onClick: function(e) {
      e.stopPropagation();
      displayTeamViewModalFunc($(this).attr('data-team-id'));
    }
  });
}

// Adventure Cancel Button
function createAdventureCancelButton(adventure_id, deleteFunction) {
  return createStandardButton({
    text: 'Cancel Adventure',
    icon: ICONS.CANCEL,
    dataAttr: 'adventure-id',
    dataValue: adventure_id,
    onClick: function() {
      const adventureId = $(this).attr('data-adventure-id');
      // Show confirmation modal instead of immediately deleting
      showCancelAdventureConfirmation(adventureId, deleteFunction);
    }
  });
}

// Claim Reward Button
function createClaimButton(adventure_id, updateReward, deleteAdv) {
  return createStandardButton({
    text: 'Claim Reward',
    icon: ICONS.REWARD,
    iconProps: { width: '16' },
    dataAttr: 'adventure-id',
    dataValue: adventure_id,
    onClick: function() {
      const adventureId = $(this).attr('data-adventure-id');
      $(this).prop('disabled', true);
      $(this).closest('.inventory-item').remove();
      updateReward(adventureId);
      deleteAdv(adventureId);
      setTimeout(() => $(this).prop('disabled', false), 5000);
    }
  });
}

// Create Team Button
function createTeamButton(item, div, addToTeam) {
  const button = createStandardButton({
    text: 'Create Team',
    icon: ICONS.PLUS,
    iconProps: {
      width: '12px',
      height: '12px'
    }
  });

  div.on("click", () => {
    const id = div.attr("id");
    addToTeam(item.schema.schema_name, item, id);
    button.remove();
  });

  return button;
}

// Update Adventure Button (special case - modifies existing button)
function updateAdventureButton(textContent) {
  const button = $(`button[data-start-btn][data-team-id="${textContent}"]`);
  button.html(
    $('<img>').attr('src', ICONS.START).prop('outerHTML') +
    'Start Adventure'
  ).removeClass('btn-danger').addClass('btn-success');
}

// Disband Team Button
function createDisbandButton(team) {
  return createStandardButton({
    text: 'Disband Team',
    icon: ICONS.CANCEL,
    dataAttr: 'team-id',
    dataValue: team.team_id,
    onClick: function() {
      const teamId = $(this).attr('data-team-id');
      // Show confirmation modal instead of immediately disbanding
      showDisbandTeamConfirmation(teamId, deleteTeam, reloadPlayerTeamData);
    }
  });
}

// Select House Button
function createSelectHouseButton(team) {
  return createStandardButton({
    text: 'Move In',
    icon: ICONS.HOUSE,
    iconProps: { width: '50px' },
    dataAttr: 'team-id',
    dataValue: team.team_id,
    onClick: function() {
      displayHouseAssignModal($(this).attr('data-team-id'), displayModalInventory);
    }
  });
}

function createAdventureButton(team) {
  return $('<button>', {
    class: "btn-secondary m-1 btn-green",
    html: '<img src="../images/ui/start_icon.png">Start Adventure'
  })
  .attr('data-team-id', team.team_id)
  .attr('data-start-btn', team.team_id)
  .on('click', function() {
    var team_id = $(this).attr('data-team-id');

    $('button[data-start-btn]').not(this).removeClass('btn-orange').addClass('btn-green');
    $('button[data-start-btn]').not(this).html('<img src="../images/ui/start_icon.png">Start Adventure');

    teamSelectedForAdventure = team_id;

    if (!enableSetAdventure) {
      enableSetAdventure = true;
      $(this).html('<img src="../images/ui/start_icon.png">Team Selected!').removeClass('btn-green').addClass('btn-orange');
      $('button[data-start-btn]').not(this).removeClass('btn-orange').addClass('btn-green');
    } else {
      enableSetAdventure = false;
      $(this).html('<img src="../images/ui/start_icon.png">Start Adventure').removeClass('btn-orange').addClass('btn-green');
    }
    
    // Refresh ThreeJS map if it's active and we're in locale view
    if (typeof refreshLocaleView === 'function') {
      refreshLocaleView();
    }
  });
}

function createMoveOutHouseButton(team, updateTeamHouse, updateTeamData) {
  var img = $('<img>').attr('src', 'images/ui/move_out_icon.png');
  const moveHouseButton = $('<button class="btn-secondary m-1">').append(img)
    .append('Move Out')
    .attr('data-team-id', team.team_id)
    .attr('data-house-assigned', team.data.house)
    .on('click', async function() {
      const team_id = Number($(this).attr('data-team-id'));
      const house_id = $(this).attr('data-house-assigned');
      showAlert(`Moved Out! Team #${team_id} has moved out of House ID#${house_id}`);
      await updateTeamHouse(team_id, 'None', showAlert);
      $('#assign-house').css('display', 'none');
      await reloadPlayerTeamData('Ready');
    });
  return moveHouseButton;
}


function createHouseMoveInButton(assetId, teamId) {
  return $("<button>", { class: "btn-secondary m-1" })
    .html("<img src='images/ui/house_icon_sm.png' width='12' height='12'>Move In")
    .attr({ "id": assetId, "data-teamid": teamId })
    .on("click", async (event) => {
      const assetId = event.target.getAttribute("id");
      const team = event.target.getAttribute("data-teamid");
      await updateTeamHouse(team, assetId, showAlert);
      $('#assign-house').css('display', 'none');
      await reloadPlayerTeamData('Ready');
    });
}
// Corrected basicGreetings object with proper syntax
// const basicGreetings = {
//   species: 'any', // Fixed: String value needs quotes
//   dialog: {
//     greeting: (creatureName) => `Hello, my name is ${creatureName}`, // Fixed: Proper string template and function
//     ready: "I'm ready to go adventuring!",
//     busy: "I'm a little busy right now.",
//     fun: "I'm having a lot of fun!",
//     seeking: "I'm looking for something to do."
//   }
// }

// function generateDialog(item) {
//   // Dialog options with explicit ready and napping states for each species
//   const dialogOptions = {
//     cat: {
//       ready: "I'm ready for an adventure!",
//       napping: "Zzzzz....."
//     },
//     dog: {
//       ready: "Okay, let's go explore some place new!",
//       napping: "I'm napping, come back later!"
//     },
//     robot: {
//       ready: "Beep boop I'm ready to travel.",
//       napping: "In standby mode...zzzz."
//     }
//   }; 
//   const species = item.data.species ? item.data.species.toLowerCase() : null; 
//   // Return dialog based on species, assuming all creatures are ready (in a team)
//   switch (species) {
//     case 'cat':
//     case 'dog':
//     case 'robot':
//       return dialogOptions[species].ready;
//     default:
//       // Default dialog for unknown species
//       return "What a good day it is!";
//   }
// }
 
// function createDialogButton(item) {
//   var dialogCreated=generateDialog(item);
//   return createStandardButton({
//     text: 'Talk',
//     icon: ICONS.DIALOG,
//     iconProps: { width: '12px', height: '12px' },
//     dataAttr: 'creature-id',
//     dataValue: item.asset_id,
//     onClick: function(e) {
//       e.stopPropagation();   
//       const creatureId = $(this).attr('data-creature-id');
//       const creatureImage = `https://ipfs.io/ipfs/${item.data.img}`;
//       const creatureName = item.name; 
//       if (window.dialogSystem) { 
//         try {
//           window.dialogSystem.show({
//             text: [
//               basicGreetings.dialog.greeting(creatureName) // Using the greeting from basicGreetings
//               // "What do you think of Starchips? He's kinda fun, right?" 
//             ],
//             characterName: creatureName,
//             characterImage: creatureImage,
//             position: "center",
//             entryDirection: "bottom",
//             timeout: 25,
//             typingSpeed: 50,
//             typingMode: "letter"
//           }); 
//         } catch (error) {  
//         }
//       } 
//     }
//   });
// }

// Unified dialog options for all species, including a generic fallback
const dialogOptions = {
  any: {
    greeting: (creatureName) => `Hello, my name is ${creatureName}!`,
    ready: "I'm ready to go adventuring!",
    busy: "I'm a little busy right now.",
    fun: "I'm having a lot of fun!",
    seeking: "I'm looking for something to do."
  },
  cat: {
    greeting: (creatureName) => `Hello, my name is ${creatureName}!`,
    ready: "I'm ready for an adventure!",
    napping: "Zzzzz....."
  },
  dog: {
    greeting: (creatureName) => `Hello, my name is ${creatureName}!`,
    ready: "Okay, let's go explore some place new!",
    napping: "I'm napping, come back later!"
  }, 
  bird: {
    greeting: (creatureName) => `Hello, I'm ${creatureName}!`,
    ready: "...",
    napping: "..."
  },
  monkey: {
    greeting: (creatureName) => `Hello, I'm ${creatureName}!`,
    ready: "Let's get going!",
    napping: "Zzzzz....."
  },
  dream_jelly: { // Changed from 'dream jelly' to 'dream_jelly'
    greeting: (creatureName) => `(My name is ${creatureName}).`,
    ready: "...*singing*...",
    napping: "..."
  },
  robot: {
    greeting: (creatureName) => `Greetings, I am ${creatureName}!`,
    ready: "Beep boop I'm ready to travel.",
    napping: "Snooze mode activated..."
  },
  ent: {
    greeting: (creatureName) => `Fare travels, I am ${creatureName}!`,
    ready: "It would delight me to go among the hills, for adventure's reward, perhaps.",
    napping: "Zzzz..."
  }
};

// Generate dialog based on species and state
function generateDialog(item, state = 'ready') {
  // Map 'dream jelly' to 'dream_jelly' for lookup
  const species = item.data.species ? item.data.species.toLowerCase().replace('dream jelly', 'dream_jelly') : 'any';
  const creatureName = item.name;

  // Get dialog options for the species, fallback to 'any' if species not found
  const dialogs = dialogOptions[species] || dialogOptions.any;

  // Return the greeting plus the state-specific dialog
  return [
    dialogs.greeting(creatureName),
    dialogs[state] || dialogOptions.any.ready // Fallback to generic ready state
  ];
}

// Create a dialog button for a creature
function createDialogButton(item, state = 'ready') {
  const dialog = generateDialog(item, state);
  return createStandardButton({
    text: 'Talk',
    icon: ICONS.DIALOG,
    iconProps: { width: '12px', height: '12px' },
    dataAttr: 'creature-id',
    dataValue: item.asset_id,
    onClick: function(e) {
      e.stopPropagation();
      const creatureId = $(this).attr('data-creature-id');
      const creatureImage = `https://ipfs.io/ipfs/${item.data.img}`;
      const creatureName = item.name;

      if (window.dialogSystem) {
        try {
          window.dialogSystem.show({
            text: dialog, // Use the generated dialog array
            characterName: creatureName,
            characterImage: creatureImage,
            position: "center",
            entryDirection: "bottom",
            timeout: 25,
            typingSpeed: 50,
            typingMode: "letter"
          });
        } catch (error) {
          console.error('Error displaying dialog:', error);
        }
      }
    }
  });
}