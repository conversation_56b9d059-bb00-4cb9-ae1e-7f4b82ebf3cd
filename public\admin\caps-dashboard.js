// Global variables
let globalCapsData = [];
let playerCapsData = [];
let systemLogsData = [];
let currentLogIndex = 0;

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
  // Load all data
  refreshData();

  // Set up event listeners
  document.getElementById('player-search').addEventListener('input', filterPlayerCaps);
  document.getElementById('log-type-filter').addEventListener('change', filterSystemLogs);
  document.getElementById('log-category-filter').addEventListener('change', filterSystemLogs);
  document.getElementById('log-reward-filter').addEventListener('change', filterSystemLogs);
});

// Function to refresh all data
function refreshData() {
  fetchGlobalCaps();
  fetchPlayerCaps();
  fetchSystemLogs();
  fetchRecentLog();
}

// Tab switching function
function openTab(tabName) {
  // Hide all tab contents
  const tabContents = document.getElementsByClassName('tab-content');
  for (let i = 0; i < tabContents.length; i++) {
    tabContents[i].classList.remove('active');
  }

  // Remove active class from all tab buttons
  const tabButtons = document.getElementsByClassName('tab-button');
  for (let i = 0; i < tabButtons.length; i++) {
    tabButtons[i].classList.remove('active');
  }

  // Show the selected tab content and mark the button as active
  document.getElementById(tabName).classList.add('active');
  event.currentTarget.classList.add('active');
}

// Fetch global caps data
async function fetchGlobalCaps() {
  try {
    const response = await fetch(`${domain_url}/global-limits/`);
    if (!response.ok) {
      throw new Error('Failed to fetch global caps data');
    }

    globalCapsData = await response.json();
    renderGlobalCaps();
  } catch (error) {
    console.error('Error fetching global caps:', error);
    document.getElementById('global-caps-body').innerHTML = `
      <tr>
        <td colspan="7">Error loading data: ${error.message}</td>
      </tr>
    `;
  }
}

// Render global caps table
function renderGlobalCaps() {
  const tableBody = document.getElementById('global-caps-body');

  if (globalCapsData.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="7">No global caps data found</td></tr>';
    return;
  }

  let html = '';

  globalCapsData.forEach(cap => {
    const currentCount = parseFloat(cap.current_count);
    const maxLimit = parseFloat(cap.max_limit);
    const percentUsed = (currentCount / maxLimit) * 100;
    const isLimitReached = currentCount >= maxLimit;
    const rowClass = isLimitReached ? 'row-red' : 'row-green';
    const statusClass = isLimitReached ? 'status-red' : 'status-green';
    const alertIcon = isLimitReached ? '<i class="fas fa-exclamation-triangle alert-icon"></i>' : '';
    const expiresAt = new Date(cap.expires_at).toLocaleString();

    html += `
      <tr class="${rowClass}">
        <td><span class="status-indicator ${statusClass}"></span>${isLimitReached ? 'Limit Reached' : 'Active'} ${alertIcon}</td>
        <td>${cap.reward_type}</td>
        <td>${currentCount}</td>
        <td>${maxLimit}</td>
        <td>
          <div class="progress">
            <div class="progress-bar ${isLimitReached ? 'bg-danger' : 'bg-success'}"
                 role="progressbar"
                 style="width: ${percentUsed}%"
                 aria-valuenow="${percentUsed}"
                 aria-valuemin="0"
                 aria-valuemax="100"></div>
          </div>
          <small>${percentUsed.toFixed(1)}%</small>
        </td>
        <td>${expiresAt}</td>
        <td>${cap.duration_hours}</td>
      </tr>
    `;
  });

  tableBody.innerHTML = html;
}

// Fetch player caps data
async function fetchPlayerCaps() {
  try {
    const response = await fetch(`${domain_url}/player-limits/`);
    if (!response.ok) {
      throw new Error('Failed to fetch player caps data');
    }

    playerCapsData = await response.json();
    renderPlayerCaps();
  } catch (error) {
    console.error('Error fetching player caps:', error);
    document.getElementById('player-caps-body').innerHTML = `
      <tr>
        <td colspan="7">Error loading data: ${error.message}</td>
      </tr>
    `;
  }
}

// Render player caps table
function renderPlayerCaps(filteredData = null) {
  const tableBody = document.getElementById('player-caps-body');
  const dataToRender = filteredData || playerCapsData;

  if (dataToRender.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="7">No player caps data found</td></tr>';
    return;
  }

  let html = '';

  dataToRender.forEach(cap => {
    const currentCount = parseFloat(cap.current_count);
    const maxLimit = parseFloat(cap.max_limit);
    const percentUsed = (currentCount / maxLimit) * 100;
    const isLimitReached = currentCount >= maxLimit;
    const rowClass = isLimitReached ? 'row-red' : 'row-green';
    const statusClass = isLimitReached ? 'status-red' : 'status-green';
    const alertIcon = isLimitReached ? '<i class="fas fa-exclamation-triangle alert-icon"></i>' : '';
    const expiresAt = new Date(cap.expires_at).toLocaleString();

    html += `
      <tr class="${rowClass}">
        <td><span class="status-indicator ${statusClass}"></span>${isLimitReached ? 'Limit Reached' : 'Active'} ${alertIcon}</td>
        <td>${cap.wax_id}</td>
        <td>${cap.reward_type}</td>
        <td>${currentCount}</td>
        <td>${maxLimit}</td>
        <td>
          <div class="progress">
            <div class="progress-bar ${isLimitReached ? 'bg-danger' : 'bg-success'}"
                 role="progressbar"
                 style="width: ${percentUsed}%"
                 aria-valuenow="${percentUsed}"
                 aria-valuemin="0"
                 aria-valuemax="100"></div>
          </div>
          <small>${percentUsed.toFixed(1)}%</small>
        </td>
        <td>${expiresAt}</td>
      </tr>
    `;
  });

  tableBody.innerHTML = html;
}

// Filter player caps based on search input
function filterPlayerCaps() {
  const searchTerm = document.getElementById('player-search').value.toLowerCase();

  if (!searchTerm) {
    renderPlayerCaps();
    return;
  }

  const filteredData = playerCapsData.filter(cap =>
    cap.wax_id.toLowerCase().includes(searchTerm)
  );

  renderPlayerCaps(filteredData);
}

// Fetch system logs data
async function fetchSystemLogs() {
  try {
    const response = await fetch(`${domain_url}/system-logs/`);
    if (!response.ok) {
      throw new Error('Failed to fetch system logs data');
    }

    systemLogsData = await response.json();
    filterSystemLogs();
  } catch (error) {
    console.error('Error fetching system logs:', error);
    document.getElementById('system-logs-body').innerHTML = `
      <tr>
        <td colspan="5">Error loading data: ${error.message}</td>
      </tr>
    `;
  }
}

// Filter system logs based on dropdown selections
function filterSystemLogs() {
  const typeFilter = document.getElementById('log-type-filter').value;
  const categoryFilter = document.getElementById('log-category-filter').value;
  const rewardFilter = document.getElementById('log-reward-filter').value;

  let filteredData = systemLogsData;

  if (typeFilter) {
    filteredData = filteredData.filter(log => log.msg_type === typeFilter);
  }

  if (categoryFilter) {
    filteredData = filteredData.filter(log => log.category === categoryFilter);
  }

  if (rewardFilter) {
    filteredData = filteredData.filter(log => log.reward_type === rewardFilter);
  }

  renderSystemLogs(filteredData);
}

// Render system logs table
function renderSystemLogs(filteredData) {
  const tableBody = document.getElementById('system-logs-body');

  if (!filteredData || filteredData.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="5">No system logs found</td></tr>';
    return;
  }

  let html = '';

  filteredData.forEach(log => {
    const createdAt = new Date(log.created_at).toLocaleString();
    const typeClass = log.msg_type === 'ERROR' || log.msg_type === 'CRITICAL' ? 'text-danger' :
                     log.msg_type === 'WARNING' ? 'text-warning' : 'text-info';

    html += `
      <tr>
        <td class="${typeClass}">${log.msg_type}</td>
        <td>${log.category}</td>
        <td>${log.reward_type || '-'}</td>
        <td>${log.message}</td>
        <td>${createdAt}</td>
      </tr>
    `;
  });

  tableBody.innerHTML = html;
}

// Fetch recent log for the top display
async function fetchRecentLog() {
  try {
    // If we already have system logs data, use that
    if (systemLogsData && systemLogsData.length > 0) {
      currentLogIndex = 0;
      updateLogDisplay(systemLogsData);
      return;
    }

    // Otherwise fetch recent logs
    const response = await fetch(`${domain_url}/system-logs/recent`);
    if (!response.ok) {
      throw new Error('Failed to fetch recent logs');
    }

    const recentLogs = await response.json();

    if (recentLogs.length === 0) {
      document.getElementById('current-log').textContent = 'No recent logs found';
      document.getElementById('log-counter').textContent = 'Log 0 of 0';
      document.getElementById('prev-log').disabled = true;
      document.getElementById('next-log').disabled = true;
      return;
    }

    // Set up log navigation
    currentLogIndex = 0;
    updateLogDisplay(recentLogs);
  } catch (error) {
    console.error('Error fetching recent logs:', error);
    document.getElementById('current-log').textContent = `Error loading recent logs: ${error.message}`;
  }
}

// Update the log display
function updateLogDisplay(logs) {
  const currentLog = logs[currentLogIndex];
  const logElement = document.getElementById('current-log');
  const counterElement = document.getElementById('log-counter');

  // Format the log message
  const createdAt = new Date(currentLog.created_at).toLocaleString();
  const typeClass = currentLog.msg_type === 'ERROR' || currentLog.msg_type === 'CRITICAL' ? 'text-danger' :
                   currentLog.msg_type === 'WARNING' ? 'text-warning' : 'text-info';

  logElement.innerHTML = `
    <span class="${typeClass}">[${currentLog.msg_type}]</span>
    <strong>${currentLog.category}</strong>
    ${currentLog.reward_type ? `(${currentLog.reward_type})` : ''}:
    ${currentLog.message}
    <div class="text-muted small">${createdAt}</div>
  `;

  // Update counter and button states
  counterElement.textContent = `Log ${currentLogIndex + 1} of ${logs.length}`;
  document.getElementById('prev-log').disabled = currentLogIndex === 0;
  document.getElementById('next-log').disabled = currentLogIndex === logs.length - 1;
}

// Navigate through logs
function navigateLog(direction) {
  const newIndex = currentLogIndex + direction;

  if (newIndex >= 0 && newIndex < systemLogsData.length) {
    currentLogIndex = newIndex;
    updateLogDisplay(systemLogsData);
  }
}
