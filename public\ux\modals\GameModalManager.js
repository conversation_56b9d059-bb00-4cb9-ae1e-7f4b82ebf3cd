// GameModalManager.js
// Handles all UI logic for the game modal (open/close, update instructions, DOM management)

class GameModalManager {
    // Creates the Game modal for mini-games if it doesn't exist
    static createModal() {
        if (document.getElementById('game-modal')) return;
        const gameContent = `
            <div id="game-container">
                <div id="start-screen">
                    <h1 id="start-title">Game Title</h1>
                    <p>Game instructions will appear here.</p>
                    <button id="start-button">
                        <img src="http://cutecrushies.com/images/games/ux/start_icon.png" alt="Start Icon"> Start
                    </button>
                </div>
                <div id="game-over-screen">
                    <h2 id="result-message">Game Over!</h2>
                    <p class="hud-item">
                        <img src="http://cutecrushies.com/images/games/ux/time_icon.png" alt="Time Icon"> Time: <span id="end-time">0</span>
                    </p>
                    <p class="hud-item">
                        <img src="http://cutecrushies.com/images/games/ux/level_icon.png" alt="Level Icon"> Highest Level: <span id="end-level">0</span>
                    </p>
                    <p class="hud-item">
                        <img src="http://cutecrushies.com/images/games/ux/score_icon.png" alt="Score Icon"> Score: <span id="end-score">0</span>
                    </p>
                    <button id="play-again-button">Play Again</button>
                    <button id="main-menu-button">Main Menu</button>
                </div>
                <div id="game-area">
                    <div class="game-header">
                        <div class="hud">
                            <div class="hud-item">
                                <img src="http://cutecrushies.com/images/games/ux/time_icon.png" alt="Time Icon"> Time: <span id="game-timer">0</span>
                            </div>
                            <div class="hud-item">
                                <img src="http://cutecrushies.com/images/games/ux/level_icon.png" alt="Level Icon"> Level: <span id="game-level">0</span>
                            </div>
                            <div class="hud-item">
                                <img src="http://cutecrushies.com/images/games/ux/score_icon.png" alt="Score Icon"> Score: <span id="game-score">0</span>
                            </div>
                        </div>
                        <div id="game-instructions">
                            <img src="http://cutecrushies.com/images/games/ux/info_icon.png" alt="Info Icon"> Game instructions will appear here.
                        </div>
                        <div class="hud-background">
                            <img src="images/games/hud/mg_hud_top.png" alt="HUD Background" class="hud-bg-image">
                        </div>
                    </div>
                    <div id="game-canvas"></div>
                </div>
            </div>
        `;
        // Assumes createModal is globally available
        if (typeof createModal === 'function') {
            createModal("game-modal", { body: gameContent, footer: '' }, "main-content");
        } else {
            // Fallback: direct DOM injection
            const modalDiv = document.createElement('div');
            modalDiv.id = 'game-modal';
            modalDiv.innerHTML = gameContent;
            document.body.appendChild(modalDiv);
        }
    }

    static open(game) {
        this.createModal();
        const gameModal = document.getElementById('game-modal');
        if (!gameModal) return;
        gameModal.style.display = 'block';
        const startScreen = document.getElementById('start-screen');
        const gameOverScreen = document.getElementById('game-over-screen');
        if (startScreen) startScreen.style.display = 'flex';
        if (gameOverScreen) gameOverScreen.style.display = 'none';
        this.updateTitles(game);
        this.updateInstructions(game);
    }

    static close() {
        const gameModal = document.getElementById('game-modal');
        if (gameModal) gameModal.style.display = 'none';
    }

    static updateTitles(game) {
        const modalTitle = document.getElementById('game-modal-title');
        const startTitle = document.getElementById('start-title');
        if (modalTitle) modalTitle.textContent = game.name;
        if (startTitle) startTitle.textContent = game.name;
    }

    static updateInstructions(game) {
        // Assumes GameUtils.updateGameInstructions exists
        if (typeof GameUtils !== 'undefined' && GameUtils.updateGameInstructions) {
            GameUtils.updateGameInstructions(game);
        } else {
            const instructions = document.getElementById('game-instructions');
            if (instructions) {
                instructions.textContent = game.instructions || '';
            }
        }
    }

    static showGameOver(result, stats) {
        const gameOverScreen = document.getElementById('game-over-screen');
        if (gameOverScreen) gameOverScreen.style.display = 'block';
        const startScreen = document.getElementById('start-screen');
        if (startScreen) startScreen.style.display = 'none';
        const resultMessage = document.getElementById('result-message');
        if (resultMessage) resultMessage.textContent = result;
        if (stats) {
            if (stats.time !== undefined) {
                const endTime = document.getElementById('end-time');
                if (endTime) endTime.textContent = stats.time;
            }
            if (stats.level !== undefined) {
                const endLevel = document.getElementById('end-level');
                if (endLevel) endLevel.textContent = stats.level;
            }
            if (stats.score !== undefined) {
                const endScore = document.getElementById('end-score');
                if (endScore) endScore.textContent = stats.score;
            }
        }
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GameModalManager };
} 