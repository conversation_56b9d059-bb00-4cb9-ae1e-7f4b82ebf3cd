// BrushPhysics.js - Matter.js physics for brush effects
class BrushPhysics {
    constructor(options = {}) {
        this.engine = options.engine || Matter.Engine.create();
        this.world = this.engine.world;
        this.particles = [];
        this.particleGroups = [];
        this.flowRate = options.flowRate || 0.1;
        this.maxParticles = options.maxParticles || 100;
        this.gravity = options.gravity || { x: 0, y: 0.5 };
        
        // Set world gravity
        this.world.gravity.y = this.gravity.y;
        
        // Physics properties for different brush sizes
        this.brushPhysics = {
            small: {
                particleSize: 2,
                flowRate: 0.05,
                viscosity: 0.98,
                maxParticles: 50
            },
            large: {
                particleSize: 4,
                flowRate: 0.15,
                viscosity: 0.95,
                maxParticles: 150
            }
        };
        
        this.currentBrushType = 'small';
        this.isActive = false;
    }

    setBrushType(brushType) {
        if (!this.brushPhysics[brushType]) {
            brushType = 'small';
        }
        this.currentBrushType = brushType;
        this.flowRate = this.brushPhysics[brushType].flowRate;
    }

    startFlow(x, y, color) {
        this.isActive = true;
        this.flowStartX = x;
        this.flowStartY = y;
        this.flowColor = color;
        this.lastParticleTime = 0;
    }

    stopFlow() {
        this.isActive = false;
    }

    updateFlow(currentX, currentY, deltaTime) {
        if (!this.isActive) return;

        const physics = this.brushPhysics[this.currentBrushType];
        const timeSinceLastParticle = deltaTime - this.lastParticleTime;
        
        if (timeSinceLastParticle > (1 / (physics.flowRate * 60))) {
            this.createParticle(currentX, currentY);
            this.lastParticleTime = deltaTime;
        }
    }

    createParticle(x, y) {
        const physics = this.brushPhysics[this.currentBrushType];
        
        if (this.particles.length >= physics.maxParticles) {
            this.removeOldestParticle();
        }

        // Create particle body
        const particle = Matter.Bodies.circle(x, y, physics.particleSize, {
            restitution: 0.3,
            friction: 0.8,
            density: 0.1,
            render: {
                fillStyle: this.flowColor
            }
        });

        // Add random velocity for natural flow
        const angle = Math.random() * Math.PI * 2;
        const speed = Math.random() * 2 + 1;
        Matter.Body.setVelocity(particle, {
            x: Math.cos(angle) * speed,
            y: Math.sin(angle) * speed
        });

        // Add to world
        Matter.World.add(this.world, particle);

        // Create PIXI graphics for rendering
        const graphics = new PIXI.Graphics();
        graphics.beginFill(this.flowColor);
        graphics.drawCircle(0, 0, physics.particleSize);
        graphics.endFill();

        // Store particle data
        const particleData = {
            body: particle,
            graphics: graphics,
            color: this.flowColor,
            life: 1.0,
            maxLife: 1.0
        };

        this.particles.push(particleData);
        return particleData;
    }

    removeOldestParticle() {
        if (this.particles.length > 0) {
            const particle = this.particles.shift();
            Matter.World.remove(this.world, particle.body);
            if (particle.graphics.parent) {
                particle.graphics.parent.removeChild(particle.graphics);
            }
        }
    }

    update(deltaTime) {
        // Update physics engine
        Matter.Engine.update(this.engine, deltaTime);

        // Update particle graphics positions
        this.particles.forEach((particle, index) => {
            const pos = particle.body.position;
            particle.graphics.x = pos.x;
            particle.graphics.y = pos.y;
            
            // Apply viscosity
            Matter.Body.setVelocity(particle.body, {
                x: particle.body.velocity.x * this.brushPhysics[this.currentBrushType].viscosity,
                y: particle.body.velocity.y * this.brushPhysics[this.currentBrushType].viscosity
            });

            // Fade out particles over time
            particle.life -= 0.01;
            if (particle.life <= 0) {
                this.removeParticle(index);
            } else {
                particle.graphics.alpha = particle.life;
            }
        });
    }

    removeParticle(index) {
        if (index >= 0 && index < this.particles.length) {
            const particle = this.particles[index];
            Matter.World.remove(this.world, particle.body);
            if (particle.graphics.parent) {
                particle.graphics.parent.removeChild(particle.graphics);
            }
            this.particles.splice(index, 1);
        }
    }

    clearParticles() {
        this.particles.forEach(particle => {
            Matter.World.remove(this.world, particle.body);
            if (particle.graphics.parent) {
                particle.graphics.parent.removeChild(particle.graphics);
            }
        });
        this.particles = [];
    }

    getParticleGraphics() {
        return this.particles.map(p => p.graphics);
    }

    setGravity(x, y) {
        this.world.gravity.x = x;
        this.world.gravity.y = y;
    }

    setColor(color) {
        this.flowColor = color;
    }

    addConstraint(bodyA, bodyB, options = {}) {
        const constraint = Matter.Constraint.create({
            bodyA: bodyA,
            bodyB: bodyB,
            ...options
        });
        Matter.World.add(this.world, constraint);
        return constraint;
    }

    createParticleGroup(x, y, count, color) {
        const group = [];
        for (let i = 0; i < count; i++) {
            const offsetX = (Math.random() - 0.5) * 20;
            const offsetY = (Math.random() - 0.5) * 20;
            const particle = this.createParticle(x + offsetX, y + offsetY);
            if (particle) {
                group.push(particle);
            }
        }
        this.particleGroups.push(group);
        return group;
    }
}

window.BrushPhysics = BrushPhysics; 