const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addTeam = (req, res) => {
    const {
        owner_id,
        team_name,
        mapgrid_4,
        mapgrid_16,
        mapgrid_256,
        nap_current,
        nap_total,
        status,
        data
    } = req.body;

    // Parse the JSON data
    const parsedData = data;
		console.log(parsedData);
    const duplicateAssets = [];

    // Check vehicle assets
    parsedData.vehicles.forEach((vehicleId) => {
			console.log("Checking vehicles before adding this team's assets to a NEW TEAM")
        pool.query(queries.getby.getTeamByVehicle, [vehicleId], (error, results) => {
            if (results.rows.length > 0) {
                duplicateAssets.push(`Vehicle ${vehicleId} already used in another team: ${results.rows[0].id}`);
            }
        });
    });

    // Check creature assets
    parsedData.creatures.forEach((creatureId) => {
				console.log("Checking creatures before adding this team's assets to a NEW TEAM")
        pool.query(queries.getby.getTeamByCreature, [creatureId], (error, results) => {
            if (results.rows.length > 0) {
                duplicateAssets.push(`Creature ${creatureId} already used in another team: ${results.rows[0].id}`);
            }
        });
    });

    // If there are any duplicate assets, return an error message
    if (duplicateAssets.length > 0) {
				console.log("Checking for any DUPLICATES in this team")
        res.status(400).send({
            error: duplicateAssets.join('; ')
        });
        return;
    }

    // If there are no duplicate assets, insert the new team
    pool.query(queries.add.addTeam, [owner_id, team_name, mapgrid_4, mapgrid_16, mapgrid_256, nap_current, nap_total, status, data], (error, results) => {
        if (error) {
            res.status(500).send({
                error: 'Unable to create team'
            });
            return;
        }
        const team_id = results.rows[0].team_id; // Retrieve the team_id from the query results
        res.send(results.rows[0]);

        // Add game log entry
        const logData = {
            desc: "You created a new team.",
            team_name: team_name,
            team_id: team_id
        };
        pool.query(queries.add.addGameLog, [owner_id, "new", "team", JSON.stringify(logData)], (error, results) => {
            if (error) {
                console.error(error);
                return;
            }
            console.log("Game log entry added for new team.");
        });
    });
};


const getTeams = (req, res) => {
	pool.query(queries.get.getTeams, (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getTeamsByOwnerId = (req, res) => {
	var owner_id = req.params.owner_id;
	pool.query(queries.getby.getTeamsByOwnerId, [owner_id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};
const getTeamByTeamId = (req, res) => {
	var owner_id = req.params.owner_id;
	var team_id = req.params.team_id;
	pool.query(queries.getby.getTeamByTeamId, [owner_id, team_id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const removeTeam = (req, res) => {
    const team_id = req.params.team_id;
    pool.query(queries.remove.removeTeam, [team_id], (error, results) => {
        if (error) {
            console.log(error);
            if (error.code === '23503' && error.constraint === 'team_id') {
                return res.status(400).send("You cannot disband team because there is an adventure in progress, or a reward must be claimed for this team.");
            }
            return res.status(500).send("Error removing team: " + error.message);
        }

        // Success response
        res.status(200).send("Team removed successfully.");
    });
};

const updateTeam = (req, res) => {
	const team_id = req.params.team_id;
	const {
		team_name,
		mapgrid_4,
		mapgrid_16,
		mapgrid_256,
		status,
		data
	} = req.body;
	pool.query(queries.up.updateTeam, [team_id], (error, results) => {
		res.status(200).send("ITEM updated successfully.");
	});
};

const updateTeamHouse = (req, res) => {
  const team_id = req.params.team_id;
  const { data } = req.body;
  // subquery to check if the house has already been assigned to another team
  const subquery = `SELECT COUNT(*) FROM teams WHERE data->>'house' = $1 AND team_id != $2`;
	var query = "UPDATE teams SET data = jsonb_set(data, '{house}', to_jsonb($1::text), true) WHERE team_id = $2";
	if(data.house==='None'){
		  query = "UPDATE teams SET data = jsonb_set(data, '{house}', to_jsonb($1::text), true) WHERE team_id = $2";
	} else{
		  query = `UPDATE teams SET data = jsonb_set(data, '{house}', to_jsonb($1::text), true) WHERE team_id = $2 AND (SELECT COUNT(*) FROM teams WHERE data->>'house' = $1 AND team_id != $2) = 0`;
 	}
	pool.query(query, [data.house, team_id], (error, results) => {
     if (error) {
       if (error.code === '23505') {
         // Duplicate key error
         res.status(400).send(`House ${data.house} is already assigned to another team`);
       } else {
         res.status(400).send("Team house update error: " + error);
       }
     } else {
       res.status(200).send("Team house updated successfully.");
     }
   });
};

const updateTeamLocation = (req, res) => {
	const team_id = req.params.team_id;
	const {
		mapgrid_4,
		mapgrid_16,
		mapgrid_256,
		nap_current,
		status
	} = req.body;
	pool.query(queries.up.updateTeamLocation, [mapgrid_4, mapgrid_16, mapgrid_256, nap_current, status, team_id], (error, results) => {
		res.status(200).send("ITEM updated successfully.");
	});
};

const updateTeamStatus = (req, res) => {
	const team_id = req.params.team_id;
	const {
		status
	} = req.body;
	pool.query(queries.up.updateTeamStatus, [team_id], (error, results) => {
		res.status(200).send("ITEM updated successfully.");
	});
};

const updateTeamNap = (req, res) => {
	const team_id = req.params.team_id;
	const {
		nap_current,
		status
	} = req.body;
	pool.query(queries.up.updateTeamNap, [nap_current, status, team_id], (error, results) => {
		if (error) {
			console.error(`Error updating team ${team_id} nap:`, error);
			return res.status(500).send(`Error updating team NAP: ${error.message}`);
		}
		res.status(200).send("Team NAP updated successfully.");
	});
};
 
const updateTeamNapBatch = async (req, res) => {
	const { teams } = req.body;

	if (!teams || !Array.isArray(teams) || teams.length === 0) {
		return res.status(400).send("Invalid request: 'teams' must be a non-empty array");
	}

	try {
		// Use a transaction to ensure all updates succeed or fail together
		await pool.query('BEGIN'); 
		for (const team of teams) {
			const { team_id, nap_current, status } = team;

			if (!team_id || nap_current === undefined || !status) {
				throw new Error(`Invalid team data: ${JSON.stringify(team)}`);
			}

			await pool.query(
				queries.up.updateTeamNap,
				[nap_current, status, team_id]
			);
		}

		// Commit the transaction
		await pool.query('COMMIT');

		res.status(200).send(`Successfully updated ${teams.length} teams`);
	} catch (error) {
		// Rollback on error
		await pool.query('ROLLBACK');
		console.error('Error in batch update:', error);
		res.status(500).send(`Error updating teams in batch: ${error.message}`);
	}
};

module.exports = {
  addTeam,
  getTeams,
  getTeamsByOwnerId,
  getTeamByTeamId,
  removeTeam, 
  updateTeam,
  updateTeamHouse,
  updateTeamLocation,
  updateTeamStatus,
  updateTeamNap,
  updateTeamNapBatch
};
