class LaserDefenderTurretMovement {
    constructor(turret<PERSON>anager, enemyManager, game) {
        this.turretManager = turretManager;
        this.enemyManager = enemyManager;
        this.game = game;
        this.targetX = turretManager.getTurretX();
        this.speed = 4;
        this.avoidDistance = 60;
        this.predictionTime = 60;
        this.velX = 0;
        this.accel = 0.6;
        this.decel = 0.5;
        this.maxVel = 7;
    }

    update() {
        if (!this.turretManager || !this.enemyManager) return;
        const turretY = this.turretManager.getTurretY();
        const turretX = this.turretManager.getTurretX();
        const turretWidth = this.turretManager.getTurretWidth();
        const minX = 30;
        const maxX = this.game.app.screen.width - 30;
        let threats = [];
        for (let i = 0; i < this.enemyManager.enemies.length; i++) {
            const enemy = this.enemyManager.enemies[i];
            if (!enemy) continue;
            const body = this.enemyManager.enemyBodies[i];
            if (!body) continue;
            const futureX = body.position.x + body.velocity.x * (this.predictionTime / 16);
            const futureY = body.position.y + body.velocity.y * (this.predictionTime / 16);
            if (futureY < turretY - 10) continue;
            if (Math.abs(futureX - turretX) < (turretWidth / 2 + this.avoidDistance)) {
                threats.push({ x: futureX, y: futureY });
            }
        }
        let moveDir = 0;
        if (threats.length > 0) {
            const avgThreatX = threats.reduce((sum, t) => sum + t.x, 0) / threats.length;
            if (avgThreatX > turretX) moveDir = -1;
            else moveDir = 1;
        } else {
            const centerX = (minX + maxX) / 2;
            if (Math.abs(turretX - centerX) > 10) {
                moveDir = turretX < centerX ? 1 : -1;
            } else {
                moveDir = 0;
            }
        }
        // Ebb/physics: accelerate/decelerate
        if (moveDir !== 0) {
            this.velX += this.accel * moveDir;
        } else {
            // Decelerate to zero
            if (this.velX > 0) {
                this.velX = Math.max(0, this.velX - this.decel);
            } else if (this.velX < 0) {
                this.velX = Math.min(0, this.velX + this.decel);
            }
        }
        // Clamp velocity
        this.velX = Math.max(-this.maxVel, Math.min(this.maxVel, this.velX));
        // Update position
        let newX = turretX + this.velX;
        newX = Math.max(minX, Math.min(maxX, newX));
        this.turretManager.setTurretX(newX);
    }
}

window.LaserDefenderTurretMovement = LaserDefenderTurretMovement;