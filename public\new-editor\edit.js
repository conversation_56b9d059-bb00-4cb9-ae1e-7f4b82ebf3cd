// edit-new.js - Modular Map Editor 3D (Grid + Textures + Real Data + Navigation + Dual Mode)
// This file loads and coordinates between the modular JS files

// Global variables that need to be accessible across modules
let navigationLabel = null;

// Use global domain_url if available, else fallback
const domain_url = (typeof window !== 'undefined' && window.domain_url) ? window.domain_url : 'https://express-crushie.herokuapp.com';
console.log('Using domain URL:', domain_url);

// Make domain_url globally accessible
window.domain_url = domain_url;

// Navigation label functions
function createNavigationLabel() {
  const container = document.getElementById('threejs-map-container');
  if (!container) return;
  
  // Remove existing label if any
  if (navigationLabel) {
    navigationLabel.remove();
  }
  
  navigationLabel = document.createElement('div');
  navigationLabel.className = 'navigation-label';
  navigationLabel.innerHTML = getNavigationLabelText();
  container.appendChild(navigationLabel);
}

function updateNavigationLabel() {
  if (navigationLabel) {
    navigationLabel.innerHTML = getNavigationLabelText();
  }
}

function getNavigationLabelText() {
  const nav = window.getNavigation();
  let text = '';
  
  if (nav.view === 'worlds') {
    text = '<div class="world-info">WORLD VIEW</div>';
  } else if (nav.view === 'zones') {
    text = '<div class="world-info">WORLD ' + (nav.world + 1) + '</div>' +
           '<div class="zone-info">ZONE VIEW</div>';
  } else if (nav.view === 'locales') {
    text = '<div class="world-info">WORLD ' + (nav.world + 1) + '</div>' +
           '<div class="zone-info">ZONE ' + (nav.zone + 1) + '</div>' +
           '<div class="locale-info">LOCALE VIEW</div>';
  }
  
  return text;
}

function updateNavigationLabelForTile(tileIndex) {
  if (navigationLabel) {
    const nav = window.getNavigation();
    if (nav.view === 'locales') {
      const text = '<div class="world-info">WORLD ' + (nav.world + 1) + '</div>' +
                   '<div class="zone-info">ZONE ' + (nav.zone + 1) + '</div>' +
                   '<div class="locale-info">LOCALE ' + (tileIndex + 1) + '</div>';
      navigationLabel.innerHTML = text;
    }
  }
}

// Render functions that coordinate between modules
async function renderWorlds() {
  console.log('renderWorlds called');
  if (!window.scene) {
    console.error('Scene not initialized, cannot render worlds');
    return;
  }
  
  if (window.clearScene) {
    window.clearScene();
  }
  
  if (window.resetCameraForView) {
    window.resetCameraForView('worlds');
  }
  
  if (window.showLoading) {
    window.showLoading('Loading worlds...');
  }
  
  const worldSize = 120; // Smaller size to fit all 4 worlds
  const gap = 0;
  const gridSize = 2;
  
  // Define world order to match desired system: top-left=0, top-right=1, bottom-left=2, bottom-right=3
  const worldOrder = [
    [0, 1],  // Row 0: World 1, World 2 (top row)
    [2, 3]   // Row 1: World 3, World 4 (bottom row)
  ];
  
  for (let row = 0; row < gridSize; row++) {
    for (let col = 0; col < gridSize; col++) {
      const worldId = worldOrder[gridSize - 1 - row][col];
      const x = (col - 0.5) * (worldSize + gap);
      const y = (row - 0.5) * (worldSize + gap);
      
      const nav = window.getNavigation();
      const isSelected = nav.world === worldId;
      const worldBgImage = isSelected ? '../images/ui/map-viewer/World_Icon_On.png' : '../images/ui/map-viewer/World_Icon_Off.png';
      
      try {
        console.log('Creating clickable mesh for world ' + worldId);
        if (window.createClickableMesh) {
          await window.createClickableMesh(worldBgImage, x, y, worldSize, 10, { 
            type: 'world', 
            id: worldId 
          });
        }
      } catch (error) {
        console.warn('Failed to load world image for world ' + worldId + ':', error);
        if (window.createFallbackMesh) {
          window.createFallbackMesh(x, y, worldSize, 10, { 
            type: 'world', 
            id: worldId 
          }, isSelected ? 0x4CAF50 : 0x9E9E9E);
        }
      }
      
      // Add world label - positioned above the world tile
      if (window.addLabel) {
        window.addLabel('WORLD ' + (worldId + 1), x, y - 8, worldSize, '#00fff0', 8, window.scene);
      }
    }
  }
  
  if (window.addNavigationButtons) {
    window.addNavigationButtons();
  }
  if (window.updateNavButtons) {
    window.updateNavButtons('world');
  }
  createNavigationLabel();
  console.log('renderWorlds completed');
}

async function renderZones() {
  if (!window.scene) {
    console.error('Scene not initialized, cannot render zones');
    return;
  }
  
  if (window.resetCameraForView) {
    window.resetCameraForView('zones');
  }
  
  const allZones = window.getAllZones();
  if (!allZones || !Array.isArray(allZones)) {
    console.error('Zones data not loaded');
    return;
  }
  
  if (window.clearScene) {
    window.clearScene();
  }
  if (window.showLoading) {
    window.showLoading('Loading zones...');
  }
  
  const zoneSize = 80; // Size to fit all 16 zones in 4x4 grid
  const gap = 0;
  const gridSize = 4;
  
  // Get zones for current world
  const nav = window.getNavigation();
  const worldZones = window.getWorldZones(nav.world);
  
  for (let row = 0; row < gridSize; row++) {
    for (let col = 0; col < gridSize; col++) {
      const idx = row * gridSize + col;
      const x = (col - 1.5) * (zoneSize + gap);
      const y = (row - 1.5) * (zoneSize + gap);
      
      // Find zone data
      const zone = worldZones.find(function(z) { return z.mapgrid_16 === idx; });
      const locked = !zone || zone.status === "locked";
      
      // Use appropriate texture
      const texturePath = locked ? '../images/ui/map-viewer/Locked_Zone_Off.png' : '../images/ui/map-viewer/World1_UnlockedTile.png';
      
      try {
        if (window.createClickableMesh) {
          await window.createClickableMesh(texturePath, x, y, zoneSize, 10, { 
            type: 'zone', 
            id: zone ? zone.mapgrid_16 : idx, 
            locked: locked, 
            zone: zone 
          });
        }
      } catch (error) {
        console.warn('Failed to load zone image for zone ' + idx + ':', error);
        if (window.createFallbackMesh) {
          const fallbackColor = locked ? 0x666666 : 0x4CAF50;
          window.createFallbackMesh(x, y, zoneSize, 10, { 
            type: 'zone', 
            id: zone ? zone.mapgrid_16 : idx, 
            locked: locked, 
            zone: zone 
          }, fallbackColor);
        }
      }
      
      // Add zone label - positioned in the center of the zone tile
      const label = zone ? zone.zone_name : 'Zone ' + (idx + 1);
      if (window.addLabel) {
        window.addLabel(label, x, y, zoneSize, '#fff', 8, window.scene);
      }
    }
  }
  
  if (window.addNavigationButtons) {
    window.addNavigationButtons();
  }
  if (window.updateNavButtons) {
    window.updateNavButtons('zone');
  }
  updateNavigationLabel();
}

async function renderLocales() {
  if (!window.scene) {
    console.error('Scene not initialized, cannot render locales');
    return;
  }

  if (window.resetCameraForView) {
    window.resetCameraForView('locales');
  }

  const allZones = window.getAllZones();
  if (!allZones || !Array.isArray(allZones)) {
    console.error('Zones data not loaded');
    return;
  }

  if (window.clearScene) {
    window.clearScene();
  }
  if (window.showLoading) {
    window.showLoading('Loading locales...');
  }

  // Remove any world/zone labels (sprites) from previous views
  if (window.scene && window.scene.children) {
    window.scene.children = window.scene.children.filter(function(obj) {
      // Remove THREE.Sprite objects (labels) but keep non-labels
      return !(obj.type === 'Sprite');
    });
  }

  // Find the zone for current world/zone
  const nav = window.getNavigation();
  const zone = window.getZoneData(nav.world, nav.zone);
  if (!zone || !zone.data || !zone.data.locales) {
    if (window.showLoading) {
      window.showLoading('No map data found for this world/zone.');
    }
    return;
  }

  // Load zone data
  if (window.loadZoneData) {
    window.loadZoneData(nav.world, nav.zone);
  }

  // Load textures and create grid
  if (window.loadTileTextures && window.createTileGrid) {
    window.loadTileTextures().then(function(textures) {
      window.createTileGrid(textures);
    });
  }

  if (window.addNavigationButtons) {
    window.addNavigationButtons();
  }
  if (window.updateNavButtons) {
    window.updateNavButtons('locale');
  }
  updateNavigationLabel();
}

// Make render functions globally accessible
window.renderWorlds = renderWorlds;
window.renderZones = renderZones;
window.renderLocales = renderLocales;
window.createNavigationLabel = createNavigationLabel;
window.updateNavigationLabel = updateNavigationLabel;
window.updateNavigationLabelForTile = updateNavigationLabelForTile;

// Initialize the editor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM Content Loaded - Initializing modular editor');
  
  // Check if required elements exist
  const container = document.getElementById('threejs-map-container');
  if (!container) {
    console.error('Required container threejs-map-container not found');
    return;
  }
  
  // Load map data and initialize
  if (window.loadMapDataAndInit) {
    window.loadMapDataAndInit();
  }
  
  // UI initialization is handled in mapUI.js
  
  // Mode switching is handled in mapUI.js
  
  // Button handlers are handled in mapUI.js
  
  console.log('Modular editor initialization complete');
}); 