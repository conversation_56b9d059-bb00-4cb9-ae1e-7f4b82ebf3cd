function getClaimableKey(world, zoneId, dateStr) {
  return `claimable_found_${world}_${zoneId}_${dateStr}`;
}

function getClaimableTileKey(world, zoneId, dateStr) {
  return `claimable_tile_${world}_${zoneId}_${dateStr}`;
}

function getTodayStr() {
  return (new Date()).toISOString().slice(0, 10);
}

function getClaimableState(world, zoneId) {
  const dateStr = getTodayStr();
  const found = localStorage.getItem(getClaimableKey(world, zoneId, dateStr));
  let tile = localStorage.getItem(getClaimableTileKey(world, zoneId, dateStr));
  if (!found && tile === null) {
    tile = Math.floor(Math.random() * 256);
    localStorage.setItem(getClaimableTileKey(world, zoneId, dateStr), tile);
  }
  return {
    found: !!found,
    tile: Number(tile)
  };
}

function setClaimableFound(world, zoneId) {
  const dateStr = getTodayStr();
  localStorage.setItem(getClaimableKey(world, zoneId, dateStr), 'true');
}

function maybeRenderClaimable(localeSquare, i, claimableState, onClaim) {
  if (!claimableState.found && Number(i) === Number(claimableState.tile)) {
    var claimableIcon = $('<img>', {
      src: 'images/ui/fx/claimable.gif',
      class: 'claimable-icon',
      css: {
        position: 'absolute',
        left: '50%',
        top: '50%',
        width: '8px',
        height: '8px',
        transform: 'translate(-50%, -50%)',
        zIndex: 10
      },
      title: 'Click to claim!'
    });
    localeSquare.css('position', 'relative');
    localeSquare.append(claimableIcon);
    claimableIcon.on('click', function(e) {
      e.stopPropagation();
      if (claimableState.found) return;
      onClaim && onClaim(claimableIcon);
    });
  }
}

window.claimable = {
  getClaimableState,
  setClaimableFound,
  maybeRenderClaimable
};