const messages = [
    "Enter the mysterious world of the Cute Crushies!",
    "Cute Crushies are animated, expressive creatures that love naps, adventures, and teamwork.",
    "Earn rewards like NFTs and Dust tokens as you play.",
    "Trade, collect, and play with Cute Crushies using your WAX Cloud Wallet!"
];

let currentMessageIndex = 0;

function updateMessage() {
    const messageElement = document.getElementById("rotating-message");
    messageElement.textContent = messages[currentMessageIndex];
}

function nextMessage() {
    currentMessageIndex = (currentMessageIndex + 1) % messages.length;
    updateMessage();
}

function prevMessage() {
    currentMessageIndex = (currentMessageIndex - 1 + messages.length) % messages.length;
    updateMessage();
}


document.querySelector('.nes-logo.floating').addEventListener('mouseenter', function() {
  AudioManager.playUISound('logo');
});

document.addEventListener('DOMContentLoaded', () => {
  copyrightTime();

});

// Initialize the first message
updateMessage();
