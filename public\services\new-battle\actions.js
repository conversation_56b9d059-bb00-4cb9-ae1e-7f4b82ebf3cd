var Actions = (function() {
    'use strict';
    
    const AP_CONFIG = {
        apUpdateInterval: 100,
        baseAPGeneration: 50,
        spdMultiplier: 10,
        minAPGeneration: 20,
        maxAPGeneration: 150,
        maxAP: 100
    };
    
    function calculateAPGenerationRate(spd) {
        const baseRate = AP_CONFIG.baseAPGeneration;
        const spdBonus = spd * AP_CONFIG.spdMultiplier;
        const totalRate = baseRate + spdBonus;
        return Math.max(AP_CONFIG.minAPGeneration, Math.min(AP_CONFIG.maxAPGeneration, totalRate));
    }
    
    function initializeAPSystem(creatures) {
        var battleState = Battle.getBattleState();
        
        battleState.creatures = creatures.map(function(creature, index) {
            var apGenerationRate = calculateAPGenerationRate(creature.stats.speed);
            var startingAP = parseInt(creature.id.toString().slice(-2)) % 100;
            
            return {
                id: creature.id,
                index: index,
                currentAP: startingAP,
                maxAP: AP_CONFIG.maxAP,
                generationRate: apGenerationRate,
                speed: creature.stats.speed
            };
        });
        
        battleState.enemyAP = 25;
        battleState.enemyGenerationRate = calculateAPGenerationRate(2);
        
        updateTeamAP();
        
        console.log('AP System initialized:', {
            creatures: battleState.creatures,
            enemyAP: battleState.enemyAP,
            teamAP: battleState.teamAP
        });
    }
    
    function updateTeamAP() {
        var battleState = Battle.getBattleState();
        
        if (battleState.creatures.length === 0) {
            battleState.teamAP = 0;
            return;
        }
        
        var totalAP = battleState.creatures.reduce(function(sum, creature) {
            return sum + creature.currentAP;
        }, 0);
        battleState.teamAP = Math.round(totalAP / battleState.creatures.length);
    }
    
    function updateAPBars() {
        var battleState = Battle.getBattleState();
        
        battleState.creatures.forEach(function(creature) {
            var apBar = document.getElementById('creature-ap-' + creature.index);
            var apText = document.getElementById('creature-ap-text-' + creature.index);
            
            if (apBar && apText) {
                apBar.style.width = creature.currentAP + '%';
                apText.textContent = Math.round(creature.currentAP) + '%';
            }
        });
        
        var enemyAPBar = document.getElementById('enemy-ap');
        var enemyAPText = document.getElementById('enemy-ap-text');
        if (enemyAPBar && enemyAPText) {
            enemyAPBar.style.width = battleState.enemyAP + '%';
            enemyAPText.textContent = Math.round(battleState.enemyAP) + '%';
        }
        
        var teamAPBar = document.getElementById('team-ap-bar');
        var teamAPText = document.getElementById('team-ap-text');
        if (teamAPBar && teamAPText) {
            teamAPBar.style.width = battleState.teamAP + '%';
            teamAPText.textContent = Math.round(battleState.teamAP) + '%';
        }
    }
    
    function generateAP() {
        var battleState = Battle.getBattleState();
        
        if (battleState.paused) return;
        
        battleState.creatures.forEach(function(creature) {
            if (creature.currentAP < creature.maxAP) {
                var apGain = (creature.generationRate / 1000) * AP_CONFIG.apUpdateInterval;
                creature.currentAP = Math.min(creature.maxAP, creature.currentAP + apGain);
            }
        });
        
        if (battleState.enemyAP < AP_CONFIG.maxAP) {
            var enemyAPGain = (battleState.enemyGenerationRate / 1000) * AP_CONFIG.apUpdateInterval;
            battleState.enemyAP = Math.min(AP_CONFIG.maxAP, battleState.enemyAP + enemyAPGain);
        }
        
        updateTeamAP();
        updateAPBars();
        checkReadyActions();
    }
    
    function checkReadyActions() {
        var battleState = Battle.getBattleState();
        
        battleState.creatures.forEach(function(creature) {
            if (creature.currentAP >= creature.maxAP) {
                var creatureCard = document.querySelector('[data-creature-id="' + creature.id + '"]');
                if (creatureCard) {
                    creatureCard.classList.add('ready-to-act');
                }
            } else {
                var creatureCard = document.querySelector('[data-creature-id="' + creature.id + '"]');
                if (creatureCard) {
                    creatureCard.classList.remove('ready-to-act');
                }
            }
        });
        
        if (battleState.enemyAP >= AP_CONFIG.maxAP) {
            $('#boss-img').addClass('ready-to-act');
            triggerEnemyAction();
        } else {
            $('#boss-img').removeClass('ready-to-act');
        }
    }
    
    function triggerEnemyAction() {
        var battleState = Battle.getBattleState();
        
        battleState.enemyAP = 0;
        
        var damage = 15 + Math.floor(Math.random() * 10);
        UI.addLogEntry('Enemy attacks for ' + damage + ' damage!', 'enemy');
        
        var currentTeamHP = parseInt($('#team-hp-text').text());
        var newTeamHP = Math.max(0, currentTeamHP - damage);
        $('#team-hp-text').text(newTeamHP + '%');
        $('#team-hp-bar').css('width', newTeamHP + '%');
        
        if (newTeamHP <= 0) {
            setTimeout(function() {
                showResult(false);
            }, 1000);
        }
    }
    
    function useCreatureAP(creatureIndex, apCost) {
        apCost = apCost || 25;
        var battleState = Battle.getBattleState();
        var creature = battleState.creatures[creatureIndex];
        
        if (!creature || creature.currentAP < apCost) {
            return false;
        }
        
        creature.currentAP -= apCost;
        updateTeamAP();
        updateAPBars();
        return true;
    }
    
    function triggerCreatureAction(creatureIndex) {
        var battleState = Battle.getBattleState();
        var creature = battleState.creatures[creatureIndex];
        
        if (!creature || creature.currentAP < 25) {
            return false;
        }
        
        if (useCreatureAP(creatureIndex, 25)) {
            var damage = 20 + Math.floor(Math.random() * 15);
            UI.addLogEntry('Creature attacks for ' + damage + ' damage!', 'ally');
            
            updateBossHP(-damage);
            
            var creatureCard = document.querySelector('[data-creature-id="' + creature.id + '"]');
            if (creatureCard) {
                creatureCard.classList.add('attack-animation');
                setTimeout(function() {
                    creatureCard.classList.remove('attack-animation');
                }, 400);
            }
            
            return true;
        }
        
        return false;
    }
    
    function startAPSystem() {
        setInterval(generateAP, AP_CONFIG.apUpdateInterval);
    }
    
    function useAction(action) {
        var battleState = Battle.getBattleState();
        var cost = parseInt($('[data-action="' + action + '"]').data('cost')) || 0;
        
        if (battleState.actionPoints < cost) {
            UI.showNotification('Not enough action points!', 'error');
            return false;
        }
        
        battleState.actionPoints -= cost;
        UI.updateBattleUI();
        
        switch (action) {
            case 'ability':
                UI.addLogEntry('Used Ability Charge!', 'system');
                UI.addBattleEffect({ type: 'buff', message: 'Ability Charged!' });
                break;
            case 'breaker':
                UI.addLogEntry('Used Boss Breaker!', 'system');
                updateBossHP(-30);
                UI.addBattleEffect({ type: 'special', message: 'Boss Breaker!' });
                break;
        }
        
        return true;
    }
    
    function useCreatureAction(index, action) {
        var battleState = Battle.getBattleState();
        var creature = battleState.creatures[index];
        
        if (!creature) {
            UI.showNotification('Invalid creature!', 'error');
            return false;
        }
        
        if (creature.currentAP < 25) {
            UI.showNotification('Creature not ready!', 'error');
            return false;
        }
        
        return triggerCreatureAction(index);
    }
    
    function useVehicleSkill(skill) {
        var battleState = Battle.getBattleState();
        
        if (battleState.actionPoints < 1) {
            UI.showNotification('Not enough action points!', 'error');
            return false;
        }
        
        battleState.actionPoints -= 1;
        UI.updateBattleUI();
        
        switch (skill) {
            case 'ground_assault':
                UI.addLogEntry('Used Ground Assault!', 'system');
                updateBossHP(-50);
                break;
            case 'tidal_wave':
                UI.addLogEntry('Used Tidal Wave!', 'system');
                updateBossHP(-60);
                break;
            case 'cosmic_blast':
                UI.addLogEntry('Used Cosmic Blast!', 'system');
                updateBossHP(-80);
                break;
        }
        
        Battle.hideModal('vehicle-modal');
        return true;
    }
    
    function updateBossHP(change) {
        var battleState = Battle.getBattleState();
        battleState.bossHP = Math.max(0, Math.min(100, battleState.bossHP + change));
        
        $('#boss-hp').css('width', battleState.bossHP + '%');
        $('#boss-hp-text').text(battleState.bossHP + '%');
        
        if (battleState.bossHP <= 0) {
            setTimeout(function() {
                showResult(true);
            }, 1000);
        }
    }
    
    function selectCreature(index) {
        $('.creature-card').removeClass('selected');
        $('[data-index="' + index + '"]').addClass('selected');
        
        var battleState = Battle.getBattleState();
        var creature = battleState.creatures[index];
        
        if (creature && creature.currentAP >= 25) {
            triggerCreatureAction(index);
        }
    }
    
    function swapCreature(creatureId) {
        UI.addLogEntry('Swapped creature: ' + creatureId, 'system');
        Battle.hideModal('team-modal');
        UI.showNotification('Creature swapped!', 'success');
    }
    
    function showResult(victory) {
        if (victory) {
            $('#result-title').html('<i class="fas fa-trophy"></i> Victory! <button class="btn-close">×</button>');
            $('#result-content .result-summary h4').text('Victory!');
            $('#result-content .result-summary p').text('You defeated the boss!');
        } else {
            $('#result-title').html('<i class="fas fa-skull"></i> Defeat <button class="btn-close">×</button>');
            $('#result-content .result-summary h4').text('Defeat');
            $('#result-content .result-summary p').text('Your team was defeated!');
        }
        
        Battle.showModal('result-modal');
    }
    
    function autoBattle() {
        var battleState = Battle.getBattleState();
        battleState.autoBattle = !battleState.autoBattle;
        
        if (battleState.autoBattle) {
            UI.addLogEntry('Auto battle enabled!', 'system');
            UI.showNotification('Auto battle enabled!', 'info');
        } else {
            UI.addLogEntry('Auto battle disabled!', 'system');
            UI.showNotification('Auto battle disabled!', 'info');
        }
    }
    
    function pauseBattle() {
        var battleState = Battle.getBattleState();
        battleState.paused = !battleState.paused;
        
        if (battleState.paused) {
            UI.addLogEntry('Battle paused!', 'system');
            UI.showNotification('Battle paused!', 'info');
        } else {
            UI.addLogEntry('Battle resumed!', 'system');
            UI.showNotification('Battle resumed!', 'info');
        }
    }
    
    function resetBattle() {
        var battleState = Battle.getBattleState();
        battleState.bossHP = 70;
        battleState.turn = 1;
        battleState.phase = 1;
        battleState.actionPoints = 3;
        battleState.autoBattle = false;
        battleState.paused = false;
        battleState.effects = [];
        
        UI.updateBattleUI();
        UI.addLogEntry('Battle reset!', 'system');
        UI.showNotification('Battle reset!', 'info');
    }
    
    function clearLog() {
        $('#battle-log').empty();
        UI.addLogEntry('Battle log cleared!', 'system');
    }
    
    return {
        initializeAPSystem: initializeAPSystem,
        startAPSystem: startAPSystem,
        useAction: useAction,
        useCreatureAction: useCreatureAction,
        useVehicleSkill: useVehicleSkill,
        selectCreature: selectCreature,
        swapCreature: swapCreature,
        autoBattle: autoBattle,
        pauseBattle: pauseBattle,
        resetBattle: resetBattle,
        clearLog: clearLog
    };
})(); 