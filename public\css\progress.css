#playerLevelBar{
  background: #e2e2e2;
  z-index: 1;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

#player-level-progress-bar {
    background: #83ccb7;
    font-family: inherit;
    height: 100%;
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    z-index: -1;
    color: #747474;
}

.progress-bar-container{
  margin-top:10px;
}

 .progress {
   height: 100%;
   margin-bottom: 10px;
   overflow: hidden;
   border-radius: 5px;
   width:0px;
 }

 .progress-bar {
  position: absolute;
  /* bottom: 0;
  left: 0; */
  width: 100%;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: width 0.6s ease;
  height:20px;
  font-weight: bold;
  color: black;
  font-size: 12px;
  line-height:20px;
  text-align: center;
 }

 .progress-bar-purple {
  background-image: url('../images/ui/hud/progress-bar-purple.png');
  background-repeat: repeat-x;
  background-size: contain;
}

.progress-bar-gold {
 background-image: url('../images/ui/hud/progress-bar-gold.png');
 background-repeat: repeat-x;
 background-size: contain;
}

.progress-bar-blue {
  background-image: url('../images/ui/hud/progress-bar-blue.png');
  background-repeat: repeat-x;
  background-size: contain;
}

.progress-bar-green {
  background-image: url('../images/ui/hud/progress-bar-green.png');
  background-repeat: repeat-x;
  background-size: contain;
}

/* .progress-bar-background{
  width: 100%;
  height: 20px !important;
  background-image: url('../images/ui/hud/progress-bar-background.png') !important;
  border-radius: 12px !important;
} */
