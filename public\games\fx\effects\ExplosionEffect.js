// Explosion Effect
class ExplosionEffect {
    constructor(visualEffects) {
        this.visualEffects = visualEffects;
    }

    // Create explosion effect
    create(x, y, color = 0xFFD700, particleCount = 15, stage) {
        for (let i = 0; i < particleCount; i++) {
            const particle = this.visualEffects.getParticle();
            particle.beginFill(color);
            particle.drawCircle(0, 0, Math.random() * 3 + 2);
            particle.endFill();
            
            particle.x = x;
            particle.y = y;
            
            const angle = (Math.PI * 2 * i) / particleCount;
            const speed = Math.random() * 3 + 2;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            
            stage.addChild(particle);
            
            // Animate particle
            this.animateParticle(particle, vx, vy, stage);
        }
    }

    // Animate particle movement
    animateParticle(particle, vx, vy, stage) {
        let alpha = 1;
        const fadeSpeed = 0.02;
        
        const animate = () => {
            particle.x += vx;
            particle.y += vy;
            particle.alpha = alpha;
            
            alpha -= fadeSpeed;
            
            if (alpha <= 0) {
                stage.removeChild(particle);
                this.visualEffects.returnParticle(particle);
            } else {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
} 