// mapZoneApi.js
// Handles API interactions for map zone data

// Assumes global variable: domain_url
// Assumes axios is available

async function updateMapZoneData(world, zone, jdata) {
    const url = `${domain_url}/players/zones/updatedata/${world}/${zone}`;
    const change = {
      mapgrid_4: world,
      mapgrid_16: zone,
      data: jdata,
    };

    try {
      const response = await axios.put(url, change, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      console.log('success');
      return response;
    } catch (error) {
      console.error('Error:', error);
      throw error;
    }
  }

async function addZone(grid4, grid16, name, type, paid, required, status, data) {
    try {
      const newZone = {
        mapgrid_4: grid4,
        mapgrid_16: grid16,
        zone_name: name,
        zone_type: type,
        gxp_paid: paid,
        gxp_required: required,
        status: status,
        data: data,
      };
      const response = await axios.post(`${domain_url}/players/zones/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newZone),
      });
      console.log('success');
      return response;
    } catch (error) {
      console.error(error);
      throw error;
    }
}

// Expose functions to global scope
window.updateMapZoneData = updateMapZoneData;
window.addZone = addZone;