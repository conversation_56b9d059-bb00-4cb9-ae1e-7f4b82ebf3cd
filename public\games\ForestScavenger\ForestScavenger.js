class ForestScavenger extends BaseGame {
  constructor() {
    super();
    this.TILE_SIZE = 32;
    this.GRID_SIZE = 16;
    this.CANVAS_SIZE = this.TILE_SIZE * this.GRID_SIZE;
    
    // Resize the canvas to fit within the standard game area (500x500)
    // The grid is 512x512, so we need to scale it down slightly to fit
    if (this.app) {
      // Calculate scale to fit the grid within the available game area
      const gameAreaWidth = 500;
      const gameAreaHeight = 500 - this.hudHeight; // Subtract HUD height
      const scaleX = gameAreaWidth / this.CANVAS_SIZE;
      const scaleY = gameAreaHeight / this.CANVAS_SIZE;
      this.scale = Math.min(scaleX, scaleY); // Use the smaller scale to ensure everything fits
      
      // Calculate the actual scaled grid dimensions
      this.scaledGridWidth = this.CANVAS_SIZE * this.scale;
      this.scaledGridHeight = this.CANVAS_SIZE * this.scale;
      
      // Calculate centering offsets to eliminate gaps
      this.gridOffsetX = (gameAreaWidth - this.scaledGridWidth) / 2;
      this.gridOffsetY = -1; // Start exactly at the HUD boundary (no gap)
      
          // Resize canvas to standard game area size
    this.app.renderer.resize(gameAreaWidth, gameAreaHeight + this.hudHeight);
    
    // Set background color to black
    this.app.renderer.backgroundColor = 0x000000;
    }
    
    // Initialize game modules
    this.gridManager = new ForestScavengerGridManager(this);
    this.playerManager = new ForestScavengerPlayerManager(this);
    this.goblinManager = new ForestScavengerGoblinManager(this);
    this.materialManager = new ForestScavengerMaterialManager(this);
    this.safeZoneManager = new ForestScavengerSafeZoneManager(this);
    
    // Game state
    this.gameOver = false;
    this.win = false;
    this.goblinsMoveInterval = null;
    this.gxpAwarded = false;
    this.isInSafeZone = false;
    
    // Level configurations - extended beyond 5 levels
    this.levelConfigs = {
      1: { wood: 1, metal: 1, glass: 0, stone: 0, coin: 0, goblins: 1, safeZones: 2 },
      2: { wood: 2, metal: 1, glass: 1, stone: 0, coin: 0, goblins: 2, safeZones: 2 },
      3: { wood: 2, metal: 2, glass: 1, stone: 1, coin: 0, goblins: 2, safeZones: 3 },
      4: { wood: 3, metal: 2, glass: 2, stone: 1, coin: 1, goblins: 3, safeZones: 3 },
      5: { wood: 3, metal: 3, glass: 2, stone: 2, coin: 2, goblins: 3, safeZones: 4 },
      6: { wood: 4, metal: 3, glass: 3, stone: 2, coin: 2, goblins: 4, safeZones: 4 },
      7: { wood: 4, metal: 4, glass: 3, stone: 3, coin: 3, goblins: 4, safeZones: 5 },
      8: { wood: 5, metal: 4, glass: 4, stone: 3, coin: 3, goblins: 5, safeZones: 5 },
      9: { wood: 5, metal: 5, glass: 4, stone: 4, coin: 4, goblins: 5, safeZones: 6 },
      10: { wood: 6, metal: 5, glass: 5, stone: 4, coin: 4, goblins: 6, safeZones: 6 }
    };
    
    this.images = {
      forest: PIXI.Texture.from('../images/games/forestscavenge/forest.png'),
      grass: PIXI.Texture.from('../images/games/forestscavenge/grass.png'),
      house: PIXI.Texture.from('../images/forest_world/c_forest.png'),
      player: {
        up: PIXI.Texture.from('images/games/forestscavenge/starchips-up.png'),
        down: PIXI.Texture.from('images/games/forestscavenge/starchips-down.png'),
        left: PIXI.Texture.from('images/games/forestscavenge/starchips-left.png'),
        right: PIXI.Texture.from('images/games/forestscavenge/starchips-right.png'),
      },
      goblin: {
        up: PIXI.Texture.from('images/games/forestscavenge/goblin-up.png'),
        down: PIXI.Texture.from('images/games/forestscavenge/goblin-down.png'),
        left: PIXI.Texture.from('images/games/forestscavenge/goblin-left.png'),
        right: PIXI.Texture.from('images/games/forestscavenge/goblin-right.png'),
      },
      materials: {
        wood: PIXI.Texture.from('images/games/forestscavenge/wood.png'),
        metal: PIXI.Texture.from('images/games/forestscavenge/metal.png'),
        glass: PIXI.Texture.from('images/games/forestscavenge/glass.png'),
        stone: PIXI.Texture.from('images/games/forestscavenge/stone.png'),
        coin: PIXI.Texture.from('images/games/forestscavenge/coin.png'),
      }
    };
    
    this.handleKeyDown = this.handleKeyDown.bind(this);
  }

  async startGame() {
    await super.startGame();
  }

  initGame(resetLevel = true) {
    super.initGame(resetLevel);
    
    // Initialize all managers
    this.gridManager.init();
    this.playerManager.init();
    this.goblinManager.init();
    this.materialManager.init();
    this.safeZoneManager.init();
    
    // Create material list HUD element using the new CollectList component
    if (!this.materialListHUD) {
      this.materialListHUD = HUDConfig.createHudElement('collectibles', 'hud_med_position_1', {
        layout: 'horizontal',
        title: 'COLLECT',
        showTitle: false, // Hide title to save space
        fontSize: 10, // Smaller font to fit better
        textColor: 0xFFFFFF,
        strokeColor: 0x000000,
        strokeThickness: 2,
        iconSize: 12, // 12x12 pixel icons
        spacing: 6, // Tighter spacing
        itemSpacing: 3, // Tighter spacing between icon and text
        titleSpacing: 8,
        padding: { x: 8, y: 8 }, // Adjusted padding for medium HUD
        animateUpdates: true,
        updateDuration: 300
      });
    }
    
    // Add material list to stage
    HUDConfig.addToStage(this.app.stage, this.materialListHUD);
    
    // Initialize material list with current requirements and icons
    this.updateMaterialListDisplay();
    
    this.gameOver = false;
    this.win = false;
    this.isInSafeZone = false;
    
    this.redrawAll();
    this.addGameEventListener('keydown', this.handleKeyDown, window);
    if (this.goblinsMoveInterval) clearInterval(this.goblinsMoveInterval);
    this.goblinsMoveInterval = setInterval(() => this.goblinManager.moveGoblins(), 1000);
  }

  onCountdownComplete() {
    this.startGameTimer();
  }

  stopGame() {
    super.stopGame();
    this.removeGameEventListener('keydown', this.handleKeyDown, window);
    if (this.goblinsMoveInterval) clearInterval(this.goblinsMoveInterval);
    this.goblinsMoveInterval = null;
    
    // Clean up sparkles when game stops
    if (this.safeZoneManager && this.safeZoneManager.cleanupSparkles) {
      this.safeZoneManager.cleanupSparkles();
    }
  }

  endGame(result) {
    super.endGame(result);
    this.removeGameEventListener('keydown', this.handleKeyDown, window);
    if (this.goblinsMoveInterval) clearInterval(this.goblinsMoveInterval);
    this.goblinsMoveInterval = null;
    
    // Clean up sparkles when game ends
    if (this.safeZoneManager && this.safeZoneManager.cleanupSparkles) {
      this.safeZoneManager.cleanupSparkles();
    }
  }

  updateHUD() {
    super.updateHUD();
    // Material counts are now updated automatically via the HUD system
  }

  updateMaterialListDisplay() {
    if (!this.materialListHUD || !this.materialManager) return;
    
    // Create collectibles data with icons and shorter labels
    const collectibles = {};
    const materialLabels = {
      wood: 'WOOD',
      metal: 'METAL', 
      glass: 'GLASS',
      stone: 'STONE',
      coin: 'COIN'
    };
    
    Object.keys(this.materialManager.requirements).forEach(materialType => {
      const required = this.materialManager.requirements[materialType];
      const collected = this.materialManager.collected[materialType] || 0;
      
      if (required > 0) {
        collectibles[materialType] = {
          collected: collected,
          required: required,
          icon: this.images.materials[materialType] || 'images/games/forestscavenge/' + materialType + '.png',
          label: materialLabels[materialType] || materialType.toUpperCase()
        };
      }
    });
    
    this.materialListHUD.updateCollectibles(collectibles);
  }

  drawAll() {
    this.app.stage.removeChildren();
    
    // Draw in order: grid, safe zones, materials, goblins, player
    this.gridManager.drawGrid();
    this.safeZoneManager.drawSafeZones();
    this.materialManager.drawMaterials();
    this.goblinManager.drawGoblins();
    this.playerManager.drawPlayer();
    
    // Re-add HUD elements after clearing stage
    if (this.materialListHUD) {
      HUDConfig.addToStage(this.app.stage, this.materialListHUD);
    }
  }

  // Optimized method to update only moving entities without affecting pulse effects
  updateMovingEntities() {
    // Only redraw goblins and player, preserving materials and safe zones
    this.goblinManager.drawGoblins();
    this.playerManager.drawPlayer();
  }

  // Method to redraw everything when needed (for level changes, etc.)
  redrawAll() {
    this.drawAll();
  }

  handleKeyDown(e) {
    if (this.gameOver || this.isCountdownActive) return;
    
    let dx = 0, dy = 0, dir = this.playerManager.player.dir;
    if (e.key === 'w' || e.key === 'ArrowUp') { dy = -1; dir = 'up'; }
    if (e.key === 's' || e.key === 'ArrowDown') { dy = 1; dir = 'down'; }
    if (e.key === 'a' || e.key === 'ArrowLeft') { dx = -1; dir = 'left'; }
    if (e.key === 'd' || e.key === 'ArrowRight') { dx = 1; dir = 'right'; }
    
    if (dx !== 0 || dy !== 0) {
      this.playerManager.movePlayer(dx, dy, dir);
    }
  }

  checkWin() {
    if (this.materialManager.checkWinCondition()) {
      const scale = this.scale || 1;
      const scaledTileSize = this.TILE_SIZE * scale;
      const gridOffsetX = this.gridOffsetX || 0;
      const gridOffsetY = this.gridOffsetY || 0;
      const centerX = this.playerManager.player.x * scaledTileSize + scaledTileSize/2 + gridOffsetX;
      const centerY = this.playerManager.player.y * scaledTileSize + this.hudHeight + scaledTileSize/2 + gridOffsetY;
      
      if (window.VisualEffects) {
        // Create multiple golden explosions for celebration
        window.VisualEffects.createExplosion(centerX, centerY, 0xFFD700, 25, this.app.stage);
        setTimeout(() => {
          window.VisualEffects.createExplosion(centerX - 20, centerY - 20, 0xFFFF00, 15, this.app.stage);
        }, 100);
        setTimeout(() => {
          window.VisualEffects.createExplosion(centerX + 20, centerY + 20, 0xFFA500, 15, this.app.stage);
        }, 200);
        
        // Add multiple golden sparkles in a celebration pattern
        for (let j = 0; j < 12; j++) {
          setTimeout(() => {
            const angle = (Math.PI * 2 * j) / 12;
            const sparkleX = centerX + Math.cos(angle) * 40;
            const sparkleY = centerY + Math.sin(angle) * 40;
            window.VisualEffects.createSparkle(sparkleX, sparkleY, this.app.stage, 0xFFD700);
          }, j * 40);
        }
        
        // Add ripple effects
        window.VisualEffects.createRipple(centerX, centerY, this.app.stage, 0xFFD700);
        setTimeout(() => {
          window.VisualEffects.createRipple(centerX, centerY, this.app.stage, 0xFFFF00);
        }, 150);
        
        // Add score popup
        window.VisualEffects.createScorePopup(centerX, centerY, 'LEVEL COMPLETE!', this.app.stage, 0xFFD700);
      }
      
      // Handle level completion
      if (this.level < 10) {
        // Award GXP for completing level 3
        if (this.level === 3 && !this.gxpAwarded) {
          this.gxpAwarded = true;
          if (typeof transactResource === 'function') {
            transactResource('gxp', 10, 'add', showAlert, updatePlayerBalances);
          }
          showAlert('Victory! You earned GXP for reaching level 3!');
        }
        
        this.level++; // Increment level first
        this.gameOver = true;
        
        // Create standardized level up effect with pause functionality
        if (window.VisualEffects && typeof window.VisualEffects.createLevelUpEffect === 'function') {
          try {
            console.log('Creating level up effect for level:', this.level);
            
            window.VisualEffects.createLevelUpEffect(this.app.stage, this.level, () => {
              console.log('Level up effect completed, resuming game');
              // Resume game and start new level
              this.gameOver = false;
              this.initGame(false); // Don't reset level, we just incremented it
            });
          } catch (error) {
            console.warn('VisualEffects.createLevelUpEffect failed:', error);
            // Fallback: start new level immediately
            this.gameOver = false;
            this.initGame(false);
          }
        } else {
          console.warn('VisualEffects or createLevelUpEffect not available');
          // Fallback: start new level immediately
          this.gameOver = false;
          this.initGame(false);
        }
      } else {
        // Final level completed
        if (window.VisualEffects) {
          const scale = this.scale || 1;
          const scaledTileSize = this.TILE_SIZE * scale;
          const gridOffsetX = this.gridOffsetX || 0;
          const gridOffsetY = this.gridOffsetY || 0;
          VisualEffects.createExplosion(
            this.playerManager.player.x * scaledTileSize + scaledTileSize/2 + gridOffsetX, 
            this.playerManager.player.y * scaledTileSize + this.hudHeight + scaledTileSize/2 + gridOffsetY, 
            0xFFD700, 30, this.app.stage
          );
        }
        this.gameOver = true;
        this.win = true;
        this.victory('Victory! You completed all levels!');
      }
    }
  }

  nextLevel() {
    // This method is now only used for countdown completion
    // Level progression is handled in checkWin()
    this.initGame(false);
  }
}

window.ForestScavenger = ForestScavenger; 