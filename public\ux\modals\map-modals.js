/**
 * map-modals.js
 *
 * Contains map and zone-related modals for the Cute Crushies game.
 * These modals are used for map navigation, zone unlocking, and location setting.
 */

// Initialize all modals when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Create all modals used in Cute Crushies Blockchain Game
  createPayZoneModal();
  createTeamViewModal();
  createSetLocationModal();
  createHouseAssignModal();
  createHowToPlayModal();
  createMainGameMenuModal();
  createWorldDescriptionModal();
  createTeamModal();
  // Mini game elements
  GameModalManager.createModal();
});

/**
 * Creates the Pay Zone modal for unlocking zones
 */
function createPayZoneModal() {
  var content = {'body':'this is a zone modal body', 'footer':''};
  createModal("map-unlock", content, "main-content");
}

/**
 * Creates the Set Location modal for setting adventure destinations
 */
function createSetLocationModal() {
  var content = {
    'body': 'Your new location is set modal.',
    'footer': ''
  };

  const modal = createSpecialModal("set-location", content, "main-content");
  const cancelButton = modal.querySelector('.btn-secondary');
  cancelButton.addEventListener("click", () => {
    modal.style.display = "none";
    updateAdventureButton(teamSelectedForAdventure);
  });

  let confirmButton = document.createElement("button");
  confirmButton.classList.add("btn-secondary");
  confirmButton.innerText = "Confirm";
  confirmButton.addEventListener("click", function() {
    // First check if we have enough nectar
    if (playerData.nectar < 1) {
      console.error('ERROR: Not enough nectar to start adventure');
      showAlert('Not enough nectar to start adventure');
      return;
    }
    // Ensure newDestination properties are numbers
    if (newDestination) {
      if (typeof newDestination.world === 'string') {
        newDestination.world = Number(newDestination.world);
      }
      if (typeof newDestination.zone === 'string') {
        newDestination.zone = Number(newDestination.zone);
      }
      if (typeof newDestination.locale === 'string') {
        newDestination.locale = Number(newDestination.locale);
      }
    }

    // Proceed with transaction and adventure creation
    try {
      transactResource('nectar', 1, 'subtract', showAlert, updatePlayerBalances);
      addAdventure(teamSelectedForAdventure);
      const teamElement = document.querySelector('.inventory-item[data-team-id="' + teamSelectedForAdventure + '"]');
      if (teamElement) {
        teamElement.remove();
      }
      $('#set-location').css('display', 'none');
    } catch (error) {
      showAlert('An error occurred: ' + error.message);
    }
  });

  $('#set-location-footer').append(confirmButton);
}
 
async function displaySetLocationModal(title, body) {
  $('#set-location-title').text(title);
  let costTag = `<p class="text-muted d-flex align-items-center"><img src="../images/ui/nectar_icon.png" width="12px" class="mr-1" alt="Nectar"> Cost: 1 Nectar</p>`;
  $('#set-location-body').html(body + costTag);
  $('#set-location').css('display', 'block');
}
 
function updatePayZoneModal(gxp_paid, gxp_required, status) {
  var gxpContentDiv = $('<div class="map-gxp-bal"></div>');
  var img = $('<img>', {
    src: '../images/nav/lock_icon.png'
  });
  var gxp_paid_abbr = gxp_paid < 100001 ? abbreviateNumber(gxp_paid) : gxp_paid;
  var gxp_required_abbr = gxp_required > 100000 ? abbreviateNumber(gxp_required) : gxp_required;
  var gxpContent = " " + gxp_paid_abbr + " / " + gxp_required_abbr + " GXP";
  gxpContentDiv.append(img)
               .append(gxpContent);
  return gxpContentDiv;
}
 
async function displayPayZoneModal() {
  $("#map-unlock-body").empty();
  $("#map-unlock-footer").empty();
  $('#map-unlock').css('display', 'block');
  var a = numberWithCommas(currentzonesJson[nav.zone].gxp_paid);
  var b = numberWithCommas(currentzonesJson[nav.zone].gxp_required);
  var c = numberWithCommas(playerData.gxp.toFixed(2));
  var areaNumber = Number(nav.zone) + 1;
  var formElement = $("<form>").append(
    $("<fieldset>").addClass("uk-fieldset").append(
      $("<div>").addClass("uk-margin").append(
        $("<input>").addClass("uk-input").attr({id: "gxp_input", onfocus: "this.value=''", placeholder: "Enter GXP amount", type: "text"})
      )
    )
  );
  var gxpRequiredSpan = $("<span>").attr("id", "map-unlock-gxp-required").html("<img src='../images/nav/lock_icon.png' width='16'>Required to Unlock: " + a + " / " + b + " GXP");
  var gxpBalanceSpan = $("<span>").attr("id", "map-unlock-gxp-balance").html("<img src='../images/ui/gxp_icon.png' width='16' alt='GXP' title='GXP is used to unlock zones.'>Your Balance: " + c + " GXP");
  var payButton = $("<button>").attr("type", "button").text("Pay GXP");
  payButton.attr("id", "payButton");
  payButton.addClass("btn-secondary");
  var cancelButton = $("<button>").addClass("btn-secondary").text("Cancel");
  cancelButton.on("click", function() {
    $('#map-unlock').hide();
  });

  payButton.on("click", function() {
    var amt_to_pay = $("#gxp_input").val();
    amt_to_pay = Number(amt_to_pay); 
    if (amt_to_pay > 0) {
      // Get the zone number for display in the confirmation modal
      var areaNumber = Number(nav.zone) + 1;  
      showPayGXPConfirmation(amt_to_pay, areaNumber, function() {
        // Pass all required functions to confirmZonePayment along with the amount
        confirmZonePayment(
          getZoneInfo,         // getZoneInfoFunction
          transactResource,    // transactResourceFunction
          updateZoneBalance,   // updateZoneFunction
          getMapData,          // getMapDataFunction
          amt_to_pay           // customAmount parameter
        );
        $('#map-unlock').css('display', 'none');
        // Display zones for the current world instead of going back to worlds view
        displayZones(nav.world);
      });
    } else {
      showAlert("Payment amount must be greater than 0 GXP.");
    }
  });

  $("#map-unlock-title").text('Unlock Zone ' + areaNumber);
  $("#map-unlock-body").append(gxpRequiredSpan, gxpBalanceSpan, formElement);
  $('#map-unlock-footer').append(cancelButton);
  $('#map-unlock-footer').append(payButton);
}
 
function setupUnlockedZone(zoneSquare, zone) {
  zoneSquare.attr('onclick', 'displayLocales(this.id, allZones)');
  zoneSquare.addClass("uw_" + (nav.world + 1));

  if (nav.zone == zone.mapgrid_16) {
    zoneSquare.addClass("z_on");
    var overlayImage = $('<div class="overlay-zone"></div>');
    zoneSquare.append(overlayImage);
  } else {
    zoneSquare.addClass("z_normal");
    var overlayImage = $('<div class="overlay-border"></div>');
    zoneSquare.append(overlayImage);
  }
}
 
function setupLockedZone(zoneSquare, zone) {
  zoneSquare.addClass("locked");
  zoneSquare.append(updatePayZoneModal(zone.gxp_paid, zone.gxp_required, zone.status));

  zoneSquare.on("click", function() {
    nav.zone = Number(this.id);
    displayPayZoneModal();
  });
}
