issues:    
 
11. threejsmap canvas: instead of using a different image when we hover over a zone that is locked, just increase brightness a bit on hover over. this brightness should also be shown on open zones when we hover over them.

12. threejsmap canvas: remove the world title and zone number from the mini tool tip that shows when we hover over locale tiles. instead, make the nav buttons within the canvas show what world we are in what zone we are in and what locale we are hovering over.  the mini tool tip should only contain:
Dewdrop Forest #56
Type: Land 

13. threejsmap canvas: learn from our legacy map and make our vehicles show up on the locale view. 

14. threejsmap canvas: the mesh textures in world view and locale view seem a little too bright. zone view textures look just right. learn from why and make the other views also display the textures on the meshes with the same 'brightness'?

15. threejsmap canvas: the styling of where the vehicle icons are shown in locale view has very large text. we do not want text to be shown just the vehicle when we create adventures - when we hover over a locale it will show in the mini tooltip the usual information plus 'teams travelling here: ___team name, wax account' show only two team names max. if the player clicks on the tile, the show alert will display the teams present there (all of them)

16. '1 Team Ready' with flag icon on the locale view 
should not have any text in it or any background color. just the icon on the locale tile should appear indicating there is a team there. 
remove any background color or text in the other icons that appear in our locale liek vehicles or teams present, etc

17. review the order in which world squares, zones, and locales are displayed in our legacy map. make sure the order is the same in our threejsmap. 

[ ] for space locale tiles we should use a slightly purple tone shift brightening effect as whitening is not working on blackish tiles    