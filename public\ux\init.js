// Wrap everything in a DOMContentLoaded event listener to ensure the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize game select functionality
    initializeGameSelect();
    // Create game buttons
    GameManager.createGameButtons();
    // Initialize button sound effects
    initializeButtonSounds();
});

function initializeGameSelect() {
    const select = document.getElementById('game-select');
    if (select) {
        select.addEventListener('change', function() {
            goToGame(select.value);
        });
    }
}

function initializeButtonSounds() {
    var buttons = document.querySelectorAll('button');
    buttons.forEach(function(button) {
        button.addEventListener('mouseenter', function() {
            AudioManager.playUISound('hover');
        });
        button.addEventListener('click', function() {
            AudioManager.playUISound('startButton');
        });
    });
}

$(document).ready(function() {
  // Any jQuery-specific initialization can go here
});
