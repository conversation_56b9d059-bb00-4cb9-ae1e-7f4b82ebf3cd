function appendAssetImageAndId(div, css, assetType, assetId, img, content) {
  var $img = $('<img>').attr('src', img).css('width', '50px');
   if(assetType==='vehicle'){
    var $img = $('<img>').attr('src', img).css('width', '150px');
    }
  var $name = $('<div>').html(content);
  var $box = $('<div>').addClass(css).append($img, $name);
  var $container = $("<div title='" + assetId + "'>").append($box);
  $(div).append($container);
}

function getFixedImageUrl(assetId, templateId) {
  var imageFixes = [81425, 82553, 82708, 88926, 98081, 103771, 103772, 106368, 152219, 236092, 417077, 552415];
  if(imageFixes.includes(Number(templateId))) {
    console.log('Image fix applied for ' + templateId);
    var fixedUrl = '../images/fixes/' + templateId + '.gif';
    return fixedUrl;
  } else {
    return null;
  }
}

function getAssetImage(asset, data) {
  for(var i = 0; i < data.length; i++) {
    if(asset === data[i].asset_id) {
      var url = getFixedImageUrl(asset, data[i].template.template_id);
      if(url) {
        return url;
      } else {
        return url_ipfs + data[i].data.img;
      }
    }
  }
  return null;
}
