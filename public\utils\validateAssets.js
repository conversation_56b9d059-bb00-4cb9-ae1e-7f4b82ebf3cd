// validateAssets.js
// Checks if teams have all their assets in the player's wallet
// Usage: validateAssets(myTeams, vehiclesData, creaturesData, housesData)

function validateAssets(myTeams, vehiclesData, creaturesData, housesData) {
  // Convert wallet assets to sets for fast lookup
  const vehicleIds = new Set((vehiclesData || []).map(v => v.asset_id));
  const creatureIds = new Set((creaturesData || []).map(c => c.asset_id));
  const houseIds = new Set((housesData || []).map(h => h.asset_id));

  // Result: { [team_id]: { missing: { vehicle: [], creatures: [], house: false }, hasMissing: bool, onlyHouseMissing: bool } }
  const result = {};

  for (const team of myTeams) {
    const missing = { vehicle: [], creatures: [], house: false };
    // Check vehicle(s)
    for (const vid of (team.data.vehicles || [])) {
      if (!vehicleIds.has(vid)) missing.vehicle.push(vid);
    }
    // Check creatures
    for (const cid of (team.data.creatures || [])) {
      if (!creatureIds.has(cid)) missing.creatures.push(cid);
    }
    // Check house (can be 'None' or asset id)
    if (team.data.house && team.data.house !== 'None' && !houseIds.has(team.data.house)) {
      missing.house = true;
    }
    // Determine if any missing
    const hasMissing = missing.vehicle.length > 0 || missing.creatures.length > 0 || missing.house;
    // Only house missing (no missing vehicle/creature)
    const onlyHouseMissing = missing.house && missing.vehicle.length === 0 && missing.creatures.length === 0;
    result[team.team_id] = { missing, hasMissing, onlyHouseMissing };
  }
  return result;
}

if (typeof module !== 'undefined') module.exports = validateAssets;
window.validateAssets = validateAssets; 