body { 
  font-family: 'Segoe UI', sans-serif; 
  margin: 0;
  padding: 0;
  background-image: url('../images/ui/map-viewer/white-pattern2.png');
  background-repeat: repeat;
  background-size: 100% 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

.battle-container { 
  display: grid; 
  grid-template-columns: 1fr 1.618fr 1fr;
  gap: 20px; 
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

.battle-card {
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.2s ease;
  height: fit-content;
}

.battle-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.battle-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #ffe91b;
  background: linear-gradient(135deg, #fff9c4, #fff59d);
  border-radius: 6px;
  padding: 12px 15px;
}

.battle-header h3 {
  margin: 0;
  color: #333;
  font-weight: 600;
  font-size: 1.1em;
}

.battle-header img {
  width: 24px;
  height: 24px;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
}

.team-summary {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85em;
  color: #495057;
  font-weight: 500;
}

.summary-item i {
  color: #6c757d;
  width: 16px;
}

.creature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.creature-card { 
  aspect-ratio: 1;
  border: 1px solid #ddd; 
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.creature-card:hover {
  background: #f0f0f0;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: #ffe91b;
}

.creature-card.selected {
  border: 2px solid #ffe91b;
  background: #fff9c4;
  box-shadow: 0 0 15px rgba(255, 233, 27, 0.3);
}

.creature-card img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #fff;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.creature-info {
  text-align: center;
  width: 100%;
}

.creature-name {
  font-weight: bold;
  color: #333;
  font-size: 0.85em;
  margin-bottom: 4px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.creature-type {
  font-size: 0.75em;
  color: #666;
  text-transform: capitalize;
  margin-bottom: 6px;
}

.creature-hp {
  font-size: 0.8em;
  color: #333;
  font-weight: 500;
}

.creature-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.75em;
  color: #666;
}

.stat-hp, .stat-power, .stat-speed {
  font-weight: 500;
}

.stat-hp { color: #e74c3c; }
.stat-power { color: #f39c12; }
.stat-speed { color: #3498db; }

.atb-container {
  width: 100%;
  margin-top: 8px;
}

.atb-bar { 
  height: 6px; 
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.atb-fill { 
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.vehicle-display {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.vehicle-display img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 8px;
  background: #fff;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vehicle-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.vehicle-info p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 0.9em;
}

.vehicle-stats {
  display: flex;
  gap: 15px;
}

.vehicle-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85em;
  color: #495057;
  font-weight: 500;
}

.vehicle-stats .stat-item i {
  width: 16px;
  text-align: center;
}

.vehicle-stats .stat-item:nth-child(1) i { color: #e74c3c; }
.vehicle-stats .stat-item:nth-child(2) i { color: #f39c12; }

.battle-arena {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.battle-arena::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../images/ui/battle-bg.png') center/cover;
  opacity: 0.3;
  z-index: 1;
}

.battle-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.boss-health-container {
  margin-bottom: 20px;
}

.boss-health { 
  width: 100%;
  height: 20px;
  background: rgba(255,255,255,0.2);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.health-fill { 
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #f39c12);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.health-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.9em;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.boss-info h2 {
  margin: 0 0 10px 0;
  font-size: 1.8em;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.boss-info p {
  margin: 0 0 15px 0;
  font-size: 1.1em;
  opacity: 0.9;
}

.team-health-container {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0,0,0,0.7);
  padding: 10px 15px;
  border-radius: 8px;
  color: white;
  min-width: 200px;
}

.team-health-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.9em;
  font-weight: 500;
}

.team-health-label i {
  color: #e74c3c;
  width: 16px;
}

.team-health-bar {
  width: 100%;
  height: 12px;
  background: rgba(255,255,255,0.2);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.team-health-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.team-health-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: healthShine 2s infinite;
}

.team-health-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.8em;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

@keyframes healthShine {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.action-btn { 
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  border: none;
  border-radius: 8px;
  background: #f8f9fa;
  color: #495057;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.action-btn:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.action-btn:active {
  transform: translateY(0);
}

.action-btn img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.food-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #fff;
  color: #495057;
  font-size: 0.85em;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.food-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.food-btn:active {
  background: #e9ecef;
}

.food-btn i {
  width: 16px;
  text-align: center;
  color: #28a745;
}

.food-btn[title*="No food"] {
  opacity: 0.5;
  cursor: not-allowed;
}

.food-btn[title*="No food"]:hover {
  background: #fff;
  border-color: #dee2e6;
}

.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 300px;
}

.notification {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  animation: slideIn 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification.success {
  border-left: 4px solid #28a745;
  background: #d4edda;
}

.notification.error {
  border-left: 4px solid #dc3545;
  background: #f8d7da;
}

.notification.info {
  border-left: 4px solid #17a2b8;
  background: #d1ecf1;
}

.notification i {
  width: 20px;
  text-align: center;
}

.notification.success i {
  color: #28a745;
}

.notification.error i {
  color: #dc3545;
}

.notification.info i {
  color: #17a2b8;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.battle-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 15px 25px;
  border-radius: 8px;
  font-size: 1.2em;
  font-weight: bold;
  animation: fadeIn 0.5s ease;
  z-index: 10;
}

.battle-effect.buff {
  background: rgba(40, 167, 69, 0.9);
}

.battle-effect.special {
  background: rgba(255, 193, 7, 0.9);
}

.battle-effect.victory {
  background: rgba(220, 53, 69, 0.9);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.battle-log-container {
  margin-top: 20px;
}

.battle-log {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
  font-size: 0.85em;
  line-height: 1.4;
}

.battle-log div {
  margin-bottom: 5px;
  padding: 5px 8px;
  border-radius: 4px;
}

.battle-log div:before {
  content: '• ';
  color: #6c757d;
  font-weight: bold;
}

.modal { 
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
  align-items: center;
  justify-content: center;
}

.modal h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 20px 0;
  color: #333;
}

.btn-close {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background: #f8f9fa;
  color: #333;
}

.debug-info {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
  font-size: 0.8em;
  color: #6c757d;
  margin-top: 20px;
}

.debug-info strong {
  color: #495057;
  display: block;
  margin-bottom: 5px;
}

.defeat-animation { animation: defeat 0.5s ease-in-out; }
.take-damage { animation: damage 0.3s ease-in-out; }
.attack-animation { animation: attack 0.4s ease-in-out; }
.team-strike-animation { animation: teamStrike 0.6s ease-in-out; }
.critical-joint-animation { animation: criticalJoint 1.2s ease-in-out; }

@keyframes defeat { 0%,100% { transform: scale(1); } 50% { transform: scale(0.9) rotate(2deg); } }
@keyframes damage { 0%,100% { filter: brightness(1); } 50% { filter: brightness(1.5) saturate(2); } }
@keyframes attack { 0%,100% { transform: translateX(0); } 50% { transform: translateX(10px); } }
@keyframes teamStrike { 
  0%,100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-2deg); }
  75% { transform: scale(1.1) rotate(2deg); }
}
@keyframes criticalJoint { 
  0%,100% { transform: scale(1); filter: brightness(1); }
  25% { transform: scale(1.2); filter: brightness(1.3) saturate(1.5); }
  50% { transform: scale(1.1); filter: brightness(1.1) saturate(1.2); }
  75% { transform: scale(1.15); filter: brightness(1.2) saturate(1.4); }
}

@media (max-width: 1200px) {
  .battle-container {
    grid-template-columns: 1fr 1.5fr 1fr;
    gap: 15px;
  }
  .creature-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}

@media (max-width: 768px) {
  .battle-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  .creature-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
  .action-grid {
    grid-template-columns: 1fr;
  }
  .battle-arena {
    min-height: 300px;
    padding: 20px;
  }
}

.loading {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #dee2e6;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 