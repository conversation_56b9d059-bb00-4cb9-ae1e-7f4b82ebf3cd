// mapEditorSettings.js
// Handles map editor settings and UI-related logic

// Assumes global variables: nav, grasses, waters, forests, castles, towns, ruins, tr_water, tr_islands, tr_waterlands, tr_castles, ds_dirt, ds_dunes, ds_ruins, ds_castles, ds_towns, sp_space, sp_debris, sp_gasfields, sp_stations, sp_planets
// Assumes getRandomName is defined elsewhere

let setLocaleLandType = '';
let setLocaleName = '';
let setLocaleTile = '';

function setLandType() {
  if (document.getElementById('mapEditorTileTypeLand').checked) {
    setLocaleLandType = 'land';
  } else if (document.getElementById('mapEditorTileTypeWater').checked) {
    setLocaleLandType = 'water';
  } else if (document.getElementById('mapEditorTileTypeSpace').checked) {
    setLocaleLandType = 'space';
  }
}

function updateMapEditorSettings() {
  setLocaleName = document.querySelector('#mapEditorLocaleName').value;
  const randomNamesCheckbox = document.getElementById('mapEditorRandomNamesCheckbox');
  const tileOptionsWorldInput = document.querySelector('#tileOptionsWorld' + nav.world);
  setLocaleTile = tileOptionsWorldInput.value.toLowerCase();
  setLandType();

  if (randomNamesCheckbox.checked) {
    switch (setLocaleTile) {
      case 'grassplains':
        setLocaleName = getRandomName(grasses);
        break;
      case 'water':
        setLocaleName = getRandomName(waters);
        break;
      case 'forest':
        setLocaleName = getRandomName(forests) + ' Forest';
        break;
      case 'castle':
        setLocaleName = getRandomName(castles) + ' Castle';
        break;
      case 'town':
        setLocaleName = getRandomName(towns) + ' Town';
        break;
      case 'ruins':
        setLocaleName = getRandomName(ruins) + ' Ruins';
        break;
      case 'tr_water':
        setLocaleName = getRandomName(tr_water) + ' Waters';
        break;
      case 'tr_island':
        setLocaleName = getRandomName(tr_islands) + ' Island';
        break;
      case 'tr_waterland':
        setLocaleName = getRandomName(tr_waterlands) + ' Waterland';
        break;
      case 'tr_castle':
        setLocaleName = getRandomName(tr_castles) + ' Longhouse';
        break;
      case 'ds_dirt':
        setLocaleName = getRandomName(ds_dirt);
        break;
      case 'ds_dunes':
        setLocaleName = getRandomName(ds_dunes) + ' Dunes';
        break;
      case 'ds_ruins':
        setLocaleName = getRandomName(ds_ruins) + ' Ruins';
        break;
      case 'ds_castle':
        setLocaleName = getRandomName(ds_castles) + ' Fortress';
        break;
      case 'ds_town':
        setLocaleName = getRandomName(ds_towns) + ' Town';
        break;
      case 'sp_normal':
        setLocaleName = getRandomName(sp_space) + ' Space';
        break;
      case 'sp_debris':
        setLocaleName = getRandomName(sp_debris);
        break;
      case 'sp_gas1':
        setLocaleName = getRandomName(sp_gasfields) + ' Field';
        break;
      case 'sp_station1':
        setLocaleName = getRandomName(sp_stations) + ' Station';
        break;
      case 'sp_gplanet1':
      case 'sp_dplanet1':
      case 'sp_rplanet1':
      case 'sp_iplanet1':
        setLocaleName = getRandomName(sp_planets) + ' Planet';
        break;
    }
  }

  return { setLocaleName, setLocaleTile, setLocaleLandType };
}

// Expose functions to global scope
window.setLandType = setLandType;
window.updateMapEditorSettings = updateMapEditorSettings;