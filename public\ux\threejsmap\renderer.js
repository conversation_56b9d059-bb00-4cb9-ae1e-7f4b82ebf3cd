let scene, camera, renderer, raycaster, mouse;
let clickableTiles = [];
let overlayDivs = [];
let container;
let loader;
let textureCache = {};

function initializeRenderer(containerElement) {
  container = containerElement;
  
  scene = new THREE.Scene();
  camera = new THREE.OrthographicCamera(
    RENDERER_CONFIG.width / -2, RENDERER_CONFIG.width / 2,
    RENDERER_CONFIG.height / 2, RENDERER_CONFIG.height / -2, 0.1, 1000
  );
  camera.position.z = RENDERER_CONFIG.cameraZ; 
  renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
  renderer.setClearColor(0x000000, 1);
  renderer.setSize(RENDERER_CONFIG.width, RENDERER_CONFIG.height); 
  renderer.domElement.style.cursor = 'url(../images/ui/pointers/cursor-small-new.png), auto'; 
  container.appendChild(renderer.domElement); 
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2(); 
  loader = new THREE.TextureLoader(); 
  preloadTextures(); 
  if (typeof initializeMiniTooltip === 'function') {
    initializeMiniTooltip();
  } 
  addNavigationButtons();
}

function addNavigationButtons() {
  overlayDivs = overlayDivs.filter(function(div) {
    if (div.classList && div.classList.contains('threejs-nav-buttons')) {
      div.remove();
      return false;
    }
    if (div.classList && div.classList.contains('navigation-label')) {
      div.remove();
      return false;
    }
    return true;
  });

  const navLabel = document.createElement('div');
  navLabel.className = 'navigation-label';
  navLabel.style.cssText = 'position: absolute; top: 0px; left: 50%; transform: translateX(-50%); z-index: 1001; text-align: center;';
  const worldInfo = document.createElement('div');
  worldInfo.className = 'world-info';
  worldInfo.textContent = 'WORLD VIEW';
  navLabel.appendChild(worldInfo);
  container.appendChild(navLabel);
  overlayDivs.push(navLabel);

  const buttonContainer = document.createElement('div');
  buttonContainer.className = 'threejs-nav-buttons';
  buttonContainer.style.cssText = `position: absolute; top: 40px; left: 50%; transform: translateX(-50%); display: flex; gap: 5px; z-index: 1000; pointer-events: auto; background: rgba(35, 39, 43, 0.9); padding: 8px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.4) 0px 2px 8px;`;
  
  const worldBtn = createNavButton('World', 'world-icon', () => {
    nav.view = 'worlds';
    nav.world = 0;
    nav.zone = 0;
    renderWorlds();
    updateThreeJsNavButtons('world');
  });
  
  const zoneBtn = createNavButton('Zone', 'zone-icon', () => {
    if (nav.view === 'worlds') {
      nav.view = 'zones';
      renderZones();
    } else {
      nav.view = 'zones';
      renderZones();
    }
    updateThreeJsNavButtons('zone');
  });
  
  const localeBtn = createNavButton('Locale', 'locale-icon', () => {
    if (nav.view === 'worlds') {
      nav.view = 'zones';
      renderZones();
    } else if (nav.view === 'zones') {
      nav.view = 'locales';
      renderLocales();
    }
    updateThreeJsNavButtons('locale');
  }); 
  buttonContainer.appendChild(worldBtn);
  buttonContainer.appendChild(zoneBtn);
  buttonContainer.appendChild(localeBtn); 
  container.appendChild(buttonContainer);
  overlayDivs.push(buttonContainer); 
  updateThreeJsNavButtons('world');
}

function createNavButton(text, iconClass, onClick) {
  const button = document.createElement('button');
  button.className = 'threejs-nav-button';
  button.style.cssText = `
    background: none;
    border: none;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 4px;
    padding: 6px 18px;
    transition: background 0.2s, color 0.2s;
    font-family: inherit;
    text-transform: uppercase;
  `;
  button.textContent = text;
  button.addEventListener('click', onClick);
  return button;
}

function updateThreeJsNavButtons(activeButton) {
  const buttons = container.querySelectorAll('.threejs-nav-button');
  buttons.forEach(btn => {
    btn.classList.remove('active');
    btn.style.background = '#f8f9fa';
    btn.style.borderColor = '#ccc';
    btn.style.color = '#333';
  }); 
  const activeBtn = Array.from(buttons).find(btn => {
    const icon = btn.querySelector(`.${activeButton}-icon`);
    return icon !== null;
  }); 
  if (activeBtn) {
    activeBtn.classList.add('active');
    activeBtn.style.background = '#007bff';
    activeBtn.style.borderColor = '#0056b3';
    activeBtn.style.color = '#fff';
  } 
  syncWithHtmlNavButtons(activeButton);
}

function syncWithHtmlNavButtons(activeButton) {
  const htmlNavButtons = document.querySelectorAll('.world_nav_buttons');
  htmlNavButtons.forEach(btn => btn.classList.remove('active')); 
  let htmlButtonId = '';
  switch (activeButton) {
    case 'world':
      htmlButtonId = 'world_btn';
      break;
    case 'zone':
      htmlButtonId = 'zone_btn';
      break;
    case 'locale':
      htmlButtonId = 'locale_btn';
      break;
  }
  
  const htmlButton = document.getElementById(htmlButtonId);
  if (htmlButton) {
    htmlButton.classList.add('active');
  }
  
  if (typeof nav !== 'undefined') {
    nav.view = activeButton === 'world' ? 'worlds' : activeButton + 's';
  }
}

function clearScene() {
  var objectsToRemove = [];
  scene.children.forEach(function(child) {
    if (child.type === 'Mesh' || child.type === 'Group') {
      objectsToRemove.push(child);
    }
  });

  objectsToRemove.forEach(function(obj) {
    scene.remove(obj);
    if (obj.material) {
      if (obj.material.map) obj.material.map.dispose();
      obj.material.dispose();
    }
    if (obj.geometry) obj.geometry.dispose();
  });

  overlayDivs.forEach(function(obj) {
    if (obj && obj.type && (obj.type === 'Sprite' || obj.type === 'Mesh')) {
      scene.remove(obj);
      if (obj.material && obj.material.map) obj.material.map.dispose();
      if (obj.material) obj.material.dispose();
      if (obj.geometry) obj.geometry.dispose();
    } else if (obj && obj.remove) {
      obj.remove();
    }
  });
  overlayDivs = []; 
  clickableTiles = []; 
  if (typeof hideMiniTooltip === 'function') {
    hideMiniTooltip();
  }
}

function addOverlayImage(imgSrc, x, y, size, zIndex = 10, onClick) {
  var textureLoader = loader || new THREE.TextureLoader();
  textureLoader.load(imgSrc, function(texture) {
    var material = new THREE.SpriteMaterial({ map: texture, transparent: true });
    var sprite = new THREE.Sprite(material);
    sprite.position.set(x, y, zIndex);
    sprite.scale.set(size, size, 1);
    if (typeof onClick === 'function') {
      sprite.userData.onClick = onClick;
      sprite.cursor = 'pointer';
    }
    scene.add(sprite);
    overlayDivs.push(sprite);
  });
}

function addLabel(text, x, y, size, color, fontSize = 14, extraStyle = {}) {
  var canvas = document.createElement('canvas');
  var ctx = canvas.getContext('2d');
  var font = (extraStyle.fontFamily || "'Press Start 2P', cursive");
  var isPixelFont = font.includes('Press Start 2P') || font.includes('Pixel') || font.includes('pixel');
  var px = fontSize || 14;
  if (isPixelFont) {
    if (px <= 8) px = 8;
    else if (px <= 16) px = 16;
    else if (px <= 32) px = 32;
    else px = 64;
  }
  ctx.font = 'bold ' + px + 'px ' + font;
  var textWidth = ctx.measureText(text).width;
  canvas.width = Math.ceil(textWidth + 16);
  canvas.height = Math.ceil(px + 12);
  ctx.font = 'bold ' + px + 'px ' + font;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillStyle = color || '#fff';
  if (extraStyle.background) {
    ctx.fillStyle = extraStyle.background;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = color || '#fff';
  }
  if (isPixelFont) {
    ctx.imageSmoothingEnabled = false;
  }
  ctx.fillText(text, Math.round(canvas.width / 2), Math.round(canvas.height / 2));
  var texture = new THREE.CanvasTexture(canvas);
  if (isPixelFont) {
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
  }
  if (typeof THREE.LinearSRGBColorSpace !== 'undefined') {
    texture.colorSpace = THREE.LinearSRGBColorSpace;
  }
  var material = new THREE.SpriteMaterial({ map: texture, transparent: true });
  var sprite = new THREE.Sprite(material);
  if (isPixelFont) {
    sprite.position.set(Math.round(x), Math.round(y), 20);
  } else {
    sprite.position.set(x, y, 20);
  }
  sprite.scale.set(size, (canvas.height / canvas.width) * size, 1);
  scene.add(sprite);
  overlayDivs.push(sprite);
}

function addLeftAlignedLabel(text, x, y, size, color, fontSize = 14) {
  const label = document.createElement('div');
  label.textContent = text;
  label.style.position = 'absolute';
  label.style.left = `${container.offsetLeft + RENDERER_CONFIG.width / 2 + x}px`;
  label.style.top = `${container.offsetTop + RENDERER_CONFIG.height / 2 + y - fontSize / 2}px`;
  label.style.width = `${size}px`;
  label.style.textAlign = 'left';
  label.style.color = color;
  label.style.fontSize = fontSize + 'px';
  label.style.pointerEvents = 'none';
  label.style.fontWeight = 'normal';
  label.className = 'threejs-map-label';
  container.appendChild(label);
  overlayDivs.push(label);
}

function createClickableMesh(texturePath, x, y, size, depth = 10, userData = {}) {
  return new Promise((resolve, reject) => {  
    if (textureCache[texturePath]) { 
      const texture = textureCache[texturePath];
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.repeat.set(1, 1);
      if (typeof THREE.LinearSRGBColorSpace !== 'undefined') {
        texture.colorSpace = THREE.LinearSRGBColorSpace;
      } 
      const material = new THREE.MeshBasicMaterial({ 
        map: texture,
        transparent: true,
        opacity: 1.0
      });
      
      const geometry = new THREE.BoxGeometry(size, size, depth);
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.set(x, y, 0);
      mesh.userData = userData;
      scene.add(mesh);
      clickableTiles.push(mesh); 
      resolve(mesh);
      return;
    }
    
    loader.load(
      texturePath,
      (texture) => { 
        textureCache[texturePath] = texture;
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(1, 1);
        if (typeof THREE.LinearSRGBColorSpace !== 'undefined') {
          texture.colorSpace = THREE.LinearSRGBColorSpace;
        } 
        const material = new THREE.MeshBasicMaterial({ 
          map: texture,
          transparent: true,
          opacity: 1.0
        });
        
        const geometry = new THREE.BoxGeometry(size, size, depth);
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(x, y, 0);
        mesh.userData = userData;
        scene.add(mesh);
        clickableTiles.push(mesh); 
        resolve(mesh);
      },
      (progress) => { 
      },
      (error) => { 
        reject(error);
      }
    );
  });
}

function createClickableMeshWithTiling(texturePath, x, y, size, depth = 10, userData = {}) {
  return new Promise((resolve, reject) => {  
    if (textureCache[texturePath]) { 
      const texture = textureCache[texturePath];
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.repeat.set(4, 4);
      if (typeof THREE.LinearSRGBColorSpace !== 'undefined') {
        texture.colorSpace = THREE.LinearSRGBColorSpace;
      }
      
      const material = new THREE.MeshBasicMaterial({ 
        map: texture,
        transparent: true,
        opacity: 1.0
      });
      
      const geometry = new THREE.BoxGeometry(size, size, depth);
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.set(x, y, 0);
      mesh.userData = userData;
      scene.add(mesh);
      clickableTiles.push(mesh); 
      resolve(mesh);
      return;
    }
    
    loader.load(
      texturePath,
      (texture) => { 
        textureCache[texturePath] = texture;
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        if (typeof THREE.LinearSRGBColorSpace !== 'undefined') {
          texture.colorSpace = THREE.LinearSRGBColorSpace;
        }
        
        const material = new THREE.MeshBasicMaterial({ 
          map: texture,
          transparent: true,
          opacity: 1.0
        });
        
        const geometry = new THREE.BoxGeometry(size, size, depth);
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(x, y, 0);
        mesh.userData = userData;
        scene.add(mesh);
        clickableTiles.push(mesh); 
        resolve(mesh);
      },
      (progress) => {
        console.log('Loading progress:', texturePath, progress);
      },
      (error) => {
        console.error('Error loading texture:', texturePath, error);
        reject(error);
      }
    );
  });
}

function createFallbackMesh(x, y, size, depth = 10, userData = {}, color = 0x9E9E9E) {
  const material = new THREE.MeshBasicMaterial({ 
    color: color,
    transparent: true,
    opacity: 1.0
  });
  const geometry = new THREE.BoxGeometry(size, size, depth);
  const mesh = new THREE.Mesh(geometry, material);
  mesh.position.set(x, y, 0);
  mesh.userData = userData;
  scene.add(mesh);
  clickableTiles.push(mesh);
  return mesh;
}

function getRaycaster() {
  return { raycaster, mouse };
}

function getScene() {
  return scene;
}

function getCamera() {
  return camera;
}

function getRenderer() {
  return renderer;
}

function getClickableTiles() {
  return clickableTiles;
}

function getOverlayDivs() {
  return overlayDivs;
}

function animate() {
  requestAnimationFrame(animate);
  renderer.render(scene, camera);
}

function resetCameraForView(viewType) {
  if (!camera) return;
  switch (viewType) {
    case 'worlds':
      camera.zoom = 1;
      camera.position.set(0, 0, RENDERER_CONFIG.cameraZ);
      break;
    case 'zones':
      camera.zoom = 1;
      camera.position.set(0, 0, RENDERER_CONFIG.cameraZ);
      break;
    case 'locales':
      camera.zoom = 1;
      camera.position.set(0, 0, RENDERER_CONFIG.cameraZ);
      break;
  }
  camera.updateProjectionMatrix();
}

function preloadTextures() {
  const texturesToPreload = [
    IMAGES.zoneLockedHover,
    IMAGES.zoneLocked,
    IMAGES.world.off,
    IMAGES.world.on,
    IMAGES.overlayBorder,
    IMAGES.overlaySelect,
    IMAGES.lock
  ];
  
  IMAGES.zoneUnlocked.forEach(texturePath => {
    texturesToPreload.push(texturePath);
  });
  
  console.log('Preloading textures:', texturesToPreload);
  
  const loadPromises = texturesToPreload.map(texturePath => 
    loadTexture(texturePath).catch(error => {
      console.warn('Failed to preload texture:', texturePath, error);
    })
  );
  
  Promise.all(loadPromises).then(() => {
    console.log('Texture preloading completed');
  });
}

function loadTexture(texturePath) {
  return new Promise((resolve, reject) => {
    if (textureCache[texturePath]) {
      resolve(textureCache[texturePath]);
      return;
    }
    
    loader.load(
      texturePath,
      (texture) => {
        textureCache[texturePath] = texture;
        if (typeof THREE.LinearSRGBColorSpace !== 'undefined') {
          texture.colorSpace = THREE.LinearSRGBColorSpace;
        }
        resolve(texture);
      },
      undefined,
      (error) => { 
        reject(error);
      }
    );
  });
}

function getCachedTexture(texturePath) {
  return textureCache[texturePath];
}

function addProgressBar(x, y, width, height, percent, zIndex = 13) {
  percent = Math.max(0, Math.min(percent, 100));
  var canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  var ctx = canvas.getContext('2d');
  ctx.fillStyle = 'rgba(0,0,0,0.5)';
  ctx.fillRect(0, 0, width, height);
  ctx.fillStyle = '#fff';
  ctx.fillRect(0, 0, Math.round(width * percent / 100), height);
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.strokeRect(0.5, 0.5, width - 1, height - 1);
  var texture = new THREE.CanvasTexture(canvas);
  var material = new THREE.SpriteMaterial({ map: texture, transparent: true });
  var sprite = new THREE.Sprite(material);
  sprite.position.set(x, y, zIndex);
  sprite.scale.set(width, height, 1);
  scene.add(sprite);
  overlayDivs.push(sprite);
  return sprite;
}

window.getCachedTexture = getCachedTexture;