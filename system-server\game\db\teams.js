const axios = require("axios");
const fetch = require('node-fetch');
const { app_url } = require("../../config");

async function updateTeamStatus(team_id, status) {
  const url = `${app_url}/teams/setstatus/` + team_id;
  const change = { "status": status };

  try {
    const response = await axios.put(url, change, {
      headers: { "Content-Type": "application/json" }
    });

    console.log('success');
  } catch (error) {
    console.error(error);
  }
}

async function updateTeamLocation(teamid, status, grid4, grid16, grid256) {
  const url = `${app_url}/teams/setlocation/` + teamid;
  const change = {
    "mapgrid_4": grid4,
    "mapgrid_16": grid16,
    "mapgrid_256": grid256,
    "nap_current": 0,
    "status": status
  };

  try {
    const response = await axios.put(url, change, {
      headers: { "Content-Type": "application/json" }
    });

    console.log('Successfully updated team location (' + teamid + ')');
  } catch (error) {
    console.error(error);
  }
}

module.exports = {
	updateTeamStatus,
	updateTeamLocation
};
