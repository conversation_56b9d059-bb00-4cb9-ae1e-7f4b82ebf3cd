async function blendWithSmartContract(params) {
    try {
        const actor = window.userAccount;
        const assetIds = (params.asset_ids || []).map(id => String(id));
        
        const actions = [
            {
                account: 'blend.nefty',
                name: 'announcedepo',
                authorization: [{ actor: actor, permission: 'active' }],
                data: {
                    count: params.count,
                    owner: actor
                }
            },
            {
                account: 'atomicassets',
                name: 'transfer',
                authorization: [{ actor: actor, permission: 'active' }],
                data: {
                    from: actor,
                    to: 'blend.nefty',
                    asset_ids: assetIds,
                    memo: 'deposit'
                }
            },
            {
                account: 'blend.nefty',
                name: 'nosecfuse',
                authorization: [{ actor: actor, permission: 'active' }],
                data: {
                    blend_id: params.blend_id,
                    claimer: actor,
                    own_assets: params.own_assets || [],
                    transferred_assets: assetIds
                }
            }
        ];

        const result = await wax.api.transact({ actions }, { blocksBehind: 3, expireSeconds: 120 });
        return result;
    } catch (error) {
        throw error;
    }
}

if (typeof window !== 'undefined') {
    window.blendWithSmartContract = blendWithSmartContract;
}