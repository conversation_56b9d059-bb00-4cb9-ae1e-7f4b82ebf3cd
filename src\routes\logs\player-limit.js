/**
 * Routes for player reward limits
 */

const { Router } = require('express');
const playerLimitController = require("../../controls/logs/player-limit");

const router = Router();

// Get all player caps
router.get("/", playerLimitController.getPlayerCaps);

// Get player caps by wax_id
router.get("/player/:wax_id", playerLimitController.getPlayerCapsByWaxId);

// Get player cap by wax_id and reward type
router.get("/:wax_id/:reward_type", playerLimitController.getPlayerCapByWaxIdAndType);

// Update player cap count
router.put("/:wax_id/:reward_type", playerLimitController.updatePlayerCapCount);

// Reset player cap count
router.post("/reset/:wax_id/:reward_type", playerLimitController.resetPlayerCapCount);

// Create a new player cap
router.post("/", playerLimitController.createPlayerCap);

module.exports = router;