class ForestScavengerGridManager {
  constructor(game) {
    this.game = game;
    this.grid = [];
    this.sprites = [];
  }

  init() {
    this.grid = [];
    this.sprites = [];
    
    // Initialize grid with forest borders and grass interior
    for (let y = 0; y < this.game.GRID_SIZE; y++) {
      let row = [];
      for (let x = 0; x < this.game.GRID_SIZE; x++) {
        if (y === 0 || y === this.game.GRID_SIZE - 1 || x === 0 || x === this.game.GRID_SIZE - 1) {
          row.push('forest');
        } else {
          row.push('grass');
        }
      }
      this.grid.push(row);
    }
    
    // Place house in center
    this.grid[8][8] = 'house';
  }

  drawGrid() {
    const hudOffset = this.game.hudHeight;
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    
    for (let y = 0; y < this.game.GRID_SIZE; y++) {
      for (let x = 0; x < this.game.GRID_SIZE; x++) {
        let tile = this.grid[y][x];
        let sprite = new PIXI.Sprite(this.game.images[tile] || this.game.images.grass);
        sprite.x = x * scaledTileSize + gridOffsetX;
        sprite.y = y * scaledTileSize + hudOffset + gridOffsetY;
        sprite.width = scaledTileSize;
        sprite.height = scaledTileSize;
        this.game.app.stage.addChild(sprite);
        this.sprites.push(sprite);
      }
    }
    
    // Draw house
    let houseSprite = new PIXI.Sprite(this.game.images.house);
    houseSprite.x = 8 * scaledTileSize + gridOffsetX;
    houseSprite.y = 8 * scaledTileSize + hudOffset + gridOffsetY;
    houseSprite.width = scaledTileSize;
    houseSprite.height = scaledTileSize;
    this.game.app.stage.addChild(houseSprite);
    this.sprites.push(houseSprite);
  }

  isWalkable(x, y) {
    if (x < 0 || x >= this.game.GRID_SIZE || y < 0 || y >= this.game.GRID_SIZE) {
      return false;
    }
    return this.grid[y][x] === 'grass' || this.grid[y][x] === 'safeZone';
  }

  isOccupied(x, y, excludePlayer = false, excludeGoblins = false, excludeMaterials = false) {
    // Check if position is occupied by other entities
    if (!excludePlayer && this.game.playerManager.player.x === x && this.game.playerManager.player.y === y) {
      return true;
    }
    
    if (!excludeGoblins) {
      for (let goblin of this.game.goblinManager.goblins) {
        if (goblin.x === x && goblin.y === y) {
          return true;
        }
      }
    }
    
    if (!excludeMaterials) {
      for (let material of this.game.materialManager.materials) {
        if (material.x === x && material.y === y) {
          return true;
        }
      }
    }
    
    return false;
  }

  getRandomEmptyPosition(excludePlayer = false, excludeGoblins = false, excludeMaterials = false) {
    let attempts = 0;
    const maxAttempts = 100;
    
    while (attempts < maxAttempts) {
      let x = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
      let y = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
      
      if (this.isWalkable(x, y) && !this.isOccupied(x, y, excludePlayer, excludeGoblins, excludeMaterials)) {
        return { x, y };
      }
      attempts++;
    }
    
    // Fallback: return a position near the center
    return { x: 8, y: 8 };
  }

  getDistance(pos1, pos2) {
    return Math.sqrt((pos1.x - pos2.x) ** 2 + (pos1.y - pos2.y) ** 2);
  }

  getFarthestPositionFromGoblins() {
    let bestPosition = { x: 8, y: 8 };
    let maxMinDistance = 0;
    
    // Check all walkable positions
    for (let y = 1; y < this.game.GRID_SIZE - 1; y++) {
      for (let x = 1; x < this.game.GRID_SIZE - 1; x++) {
        if (this.isWalkable(x, y) && !this.isOccupied(x, y, true, true, true)) {
          // Find minimum distance to any goblin
          let minDistance = Infinity;
          for (let goblin of this.game.goblinManager.goblins) {
            let distance = this.getDistance({ x, y }, goblin);
            if (distance < minDistance) {
              minDistance = distance;
            }
          }
          
          // If this position has a larger minimum distance, it's better
          if (minDistance > maxMinDistance) {
            maxMinDistance = minDistance;
            bestPosition = { x, y };
          }
        }
      }
    }
    
    return bestPosition;
  }

  setTile(x, y, tileType) {
    if (x >= 0 && x < this.game.GRID_SIZE && y >= 0 && y < this.game.GRID_SIZE) {
      this.grid[y][x] = tileType;
    }
  }

  getTile(x, y) {
    if (x >= 0 && x < this.game.GRID_SIZE && y >= 0 && y < this.game.GRID_SIZE) {
      return this.grid[y][x];
    }
    return 'forest';
  }
}

window.ForestScavengerGridManager = ForestScavengerGridManager; 