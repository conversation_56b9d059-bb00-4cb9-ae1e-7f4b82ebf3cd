.inventory-container {
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.inventory-scroll{
  max-height: 400px;
  overflow-y: scroll;
}

/* Style for active inventory button */
.inventory-active-view {
  background-color: #ffe91b;
  color: black;
  border-bottom: 2px solid #d63030;
  font-weight: bold;
}

.inventory-container h2 {
  font-size: 1.5em;
  margin-bottom: 10px;
  text-align: center;
  width: 100%;
}

.inventory-container ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.inventory-container li {
  background-color: #4a4a4a;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  font-size: 0.9em;
  margin: 5px;
  padding: 5px;
  text-align: center;
  transition: background-color 0.2s ease;
  height:400px;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.inventory-container li:hover {
  background-color: #222;
}


.inventory-item {
    display: flex;
    padding: 1em;
    margin-bottom: 1em;
    position: relative;
    font-size: clamp(12px, 2vw, 16px);
    border: 1px solid #ccc;
    border-radius: 5px;
    width: 100%;
}


.inventory-item:hover {
    background: #f2f2f4;
    /* transform: translateY(-5px); */
}

.selected-inventory-item {
    border: 2px solid #3f0;
    background-color: rgb(197, 255, 195);
}
.selected-inventory-item:hover {
    border: 2px solid #3f0;
    background-color: rgb(197, 255, 195);
}

.inventory-item.status-message {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f8f8f8;
  border-radius: 4px;
  margin: 0.5rem 0;
}

.inventory-status-message {
  margin-left: 1rem;
  color: #666;
  font-style: italic;
}

.inventory-item .item-details {
  display: flex;
  flex-wrap: wrap; 
  margin-left: 1em;
  margin-right: 0.5em;
  min-height: 80px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  gap: 0.25em;
}

.inventory-item .item-details span {
    /* this gives it airyness but more info feels better */
  /* padding: 0.5em; */
  display: flex;
  align-items: center;
  margin-bottom: 0.25em;
  font-size: 0.9em;
}

.inventory-item .buttons img {
 display: inline-flex;
 width: 12px;
 margin-right: 5px;
}

.item-details img {
  margin-right: 8px;
  vertical-align: middle;
}

.grid-inventory {
  overflow: scroll;
  max-height: 20%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  border-radius: 5px;
  padding-top: 1em;
  background:none;
}

.grid-header{
  padding: 1em;
  border-bottom: 2px solid #E9E9ED;
  font-weight: bold;
  text-align: center;
}


.grid-item {
  padding: 1em;
  margin-bottom: 1em;
  position: relative;
  font-size: 12px;
  border: 1px solid #ccc;
  border-radius: 5px;
  height: fit-content;
  max-width: 100%;
}

/* Style for clickable creatures in the new team selection */
#new-team-selected-assets .grid-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

#new-team-selected-assets .grid-item:hover {
  background-color: #ffeeee;
  border-color: #ff6666;
  transform: scale(0.98);
}

.team-item {
  display: flex;
  align-items: center;
  padding: 1em;
  margin-bottom: 1em;
  position: relative;
  font-size: 12px;
  border: 1px solid #ccc;
  border-radius: 5px;
  height: fit-content;
  max-width: 100%;
}

/* Team Create Modal */
.team-create-list{
  height: 215px;
  overflow: auto;
  font-size: 12px;
  margin: 20px;
  padding: 10px;
}

 .in-team {
  background: #EEE;
  font-weight: normal;
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 5px;
  border-radius: 5px;
}

 .team-name{
   width: 100%; 
   background-repeat: no-repeat;
   background-size: contain;
   border-bottom:2px solid #EDEDED;
 }

.asset-name, .team-name, .adventure-id{
  font-weight:bold;
}

.team-id, .adventure-id{
  font-transform:uppercase;
}

.asset-id{
  width: 100%;
  display: flex;
  font-transform: uppercase;
}

.adventure-status{
  font-weight:bold;
  color: #22AA00;
}

.team-destination{
  border-top: 2px dashed #f2f2f4;
  margin-top: 2px;
}

.team-destination:hover{
  border-top: 2px dashed white;
}

.limit-message {
  color: #d9534f;
  font-weight: bold;
  background-color: #f8d7da;
  padding: 5px;
  border-radius: 3px;
  margin: 5px 0;
}

#team-view-body{
  height: 400px;
  overflow: auto;
}

#assign-house-body{
    height: 400px;
    overflow: auto;
}

/* Inventory Filter Styles */
.inventory-filter-container {
    display: flex;
    align-items: center; 
    padding: 10px;
}

.inventory-filter-container label {
    margin-right: 10px;
    font-weight: bold;
    font-size: 14px;
}

.inventory-filter-dropdown {
    padding: 5px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    cursor: pointer;
}

.inventory-filter-dropdown:hover {
    border-color: #999;
}

.inventory-filter-dropdown:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 3px rgba(0, 102, 204, 0.5);
}

 