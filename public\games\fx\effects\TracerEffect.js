// Tracer.js - Adds silhouette tracer effect to a PIXI sprite
// Usage: addTracer(sprite, { iterations: 2, color: 0xFFFFFF, alpha: 0.2 })

    if (!sprite || !sprite.parent) return;
    const parent = container || sprite.parent;
    // Try to use previous positions if available, otherwise offset by angle
    let prevAngle = sprite.rotation;
    for (let i = 1; i <= iterations; i++) {
        let tracer;
        if (sprite instanceof PIXI.Sprite) {
            tracer = new PIXI.Sprite(sprite.texture);
            tracer.anchor?.set?.(sprite.anchor?.x || 0.5, sprite.anchor?.y || 0.5);
            tracer.width = sprite.width;
            tracer.height = sprite.height;
        } else if (sprite instanceof PIXI.Graphics) {
            tracer = sprite.clone();
        } else {
            continue;
        }
        // Offset each tracer by a gap along the angle
        let angle = sprite.rotation;
        if (sprite.body && sprite.body.velocity) {
            angle = Math.atan2(sprite.body.velocity.y, sprite.body.velocity.x);
        }
        tracer.x = sprite.x - Math.cos(angle) * gap * i;
        tracer.y = sprite.y - Math.sin(angle) * gap * i;
        tracer.rotation = sprite.rotation;
        tracer.tint = color;
        tracer.alpha = alpha / i; // Fainter for further iterations
        tracer.zIndex = (sprite.zIndex || 0) - i - 1;
        parent.addChildAt(tracer, 0); // Place behind the sprite
    }
    // TODO: Add filter support for tracer effect
}

// For global access if needed
if (typeof window !== 'undefined') {
    window.TracerFX = { addTracer };
} 