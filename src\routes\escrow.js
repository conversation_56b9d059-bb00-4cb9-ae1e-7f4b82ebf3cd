// where the express routes are stored
const { Router } = require('express');
const allQuery = require("../controls/controller");
  
const router = Router();

router.get("/", allQuery.getEscrow);
router.get("/escrow/:id", allQuery.getEscrowByAssetId);
router.post("/escrow/:id", allQuery.addEscrow);
router.delete("/escrow/:id", allQuery.removeEscrow);
router.put("/router/:id", allQuery.updateEscrow);

module.exports = router;
