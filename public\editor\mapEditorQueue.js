// mapEditorQueue.js
// Manages the map editor queue for proposed changes

// Assumes global variables: nav, mapEditorQueue, allZones, updateMapEditorSettings, updateMapZoneData
// Assumes jQuery is available
// Assumes functions: getLocaleData, displayZones, getMapData

function insertLocaleChangesIntoQueue() {
  const { setLocaleName, setLocaleTile, setLocaleLandType } = updateMapEditorSettings();
  if (setLocaleName === 'undefined' || setLocaleTile === 'undefined' || setLocaleLandType === 'undefined') {
    alert('Something is still undefined, not pushing into mapEditorQueue');
  } else {
    mapEditorQueue.push({
      Tile: setLocaleTile,
      Locale: Number(nav.locale),
      Terrain: setLocaleLandType,
      Locale_Name: setLocaleName,
    });
    let queueText = '';
    $('#mapEditorDisplayText').html('');
    Object.keys(mapEditorQueue).forEach((key) => {
      queueText += `<li>${mapEditorQueue[key].Locale} - Name: ${mapEditorQueue[key].Locale_Name} Terrain: ${mapEditorQueue[key].Terrain} Tile: ${mapEditorQueue[key].Tile}</li>`;
    });
    $('#mapEditorDisplayText').html(`<strong>Proposed Tile changes: </strong><ul class="mapChangesList">${queueText}</ul>`);
    console.log('added to update queue: ' + mapEditorQueue);
  }
}

async function appendEditsToZone() {
  const newEdits = mapEditorQueue;
  const currentMap = getLocaleData(allZones, nav.world, nav.zone); // Assumes getLocaleData is defined
  console.log('current map contains: ');
  console.log(currentMap);
  for (const edit of newEdits) {
    currentMap.forEach((element, index) => {
      if (element.Locale === edit.Locale) {
        currentMap[index] = edit;
        console.log(`replacing ${JSON.stringify(currentMap[index])} with ${JSON.stringify(edit)}`);
      }
    });
  }
  const updatedMap = {
    locales: currentMap,
  };

  console.log('updated map contains:');
  console.log(updatedMap.locales);

  await updateMapZoneData(nav.world, nav.zone, updatedMap);
  mapEditorQueue = [];
  $('#mapEditorDisplayText').html('<strong>Display Text: </strong>Updating the zone with changes.');
  setTimeout(() => {
    displayZones(nav.zone); // Assumes displayZones is defined
  }, 5000);
}

function clearMapEditorQueue() {
  mapEditorQueue = [];
  $('#mapEditorDisplayText').html('Cleared Edit Queue!');
  displayZones(nav.zone); // Assumes displayZones is defined
}

async function reloadEditorMap() {
  allZones = await getMapData(); // Assumes getMapData is defined
  await displayZones(nav.zone); // Assumes displayZones is defined
}

// Expose functions to global scope
window.insertLocaleChangesIntoQueue = insertLocaleChangesIntoQueue;
window.appendEditsToZone = appendEditsToZone;
window.clearMapEditorQueue = clearMapEditorQueue;
window.reloadEditorMap = reloadEditorMap;