function displayHowToPlayModal() {
  var html = `
  <p>Youtube Video Embedded Here</p>
  <p>Translation buttons</p>
    <h5>1. Requirements</h5>
    <ul>
      <li>1 Vehicle - <a href="https://wax.atomichub.io/market?collection_name=cutecrushies&order=desc&schema_name=vehicles&sort=created&state=1&symbol=WAX">Purchase here</a></li>
      <li>1 Creature - <a href="https://wax.atomichub.io/market?collection_name=cutecrushies&order=desc&schema_name=creature&sort=created&state=1&symbol=WAX">Purchase here</a></li>
    </ul>
    <h5>2. Create a Team</h5>
    <ol>
      <li>Go to Vehicles, Click <strong>Create New Team</strong> button</li>
      <li>Click <strong>Add to Team</strong> to add creatures to the team</li>
      <li>Click <strong>Confirm Team</strong></li>
      <li>New teams will nap before they can be sent on adventures</li>
    </ol>
      <h5>3. Start an Adventure</h5>
    <ol>
      <li>Once new team is 'Ready', under Teams, select 'Ready'</li>
      <li>Click <strong>Start Adventure</strong> button on your team, then click the map to set destination</li>
      <li>Click <strong>Confirm</strong>, then view the progress under Adventure, select 'In Progress'</li>
      <li>Claim rewards under Adventure, select 'Complete'</li>
      <li>To reduce nap times, purchase a House and assign a team to the house</li>
    </ol>
  `;

  $('#how-to-play-title').text('How to Play');
  $('#how-to-play-body').html(html);
  $('#how-to-play').css('display', 'block');
}
 
function displayWorldDescriptionModal(data, num) {
  var title = data[num].title;
  var description = data[num].description;
  var html = `<div style="text-align: center;"><img src="images/ui/angel_lypsis.png" width="32" style="margin-bottom:15px;"></div><p>${description}</p><div style="text-align: center;"><img src="images/ui/angel_lypsis2.png" width="32"></div>`;
  $('#world-description-title').text(title);
  $('#world-description-body').html(html);
  $('#world-description').css('display', 'block');
}
 
function displayMainGameMenuModal() {
  var html = `
    <ul>
      <li><a href="https://cutecrushies.com/">Blog</a></li>
      <li><a href="https://neftyblocks.com/c/cutecrushies/blends">Blends</a></li>
      <li><a href="https://wax.atomichub.io/market?collection_name=cutecrushies">Shop</a></li>
      <li><a href="https://dash.cutecrushies.com/">Dash</a></li>
    </ul>
    <div class="main-game-menu-buttons">
      <div class="row">
        <button class="btn-secondary"><img src="../images/ui/cancel_small_icon.png" alt="Rotate UI">Rotate UI</button>
        <button class="btn-secondary"><img src="../images/ui/cancel_small_icon.png" alt="Radio On/Off">Radio On/Off</button>
      </div>
    </div>
  `;

  $('#main-game-menu-title').text('Main Game Menu');
  $('#main-game-menu-body').html(html);
  $('#main-game-menu').css('display', 'block');
}
 
function createHowToPlayModal() {
  var content = {'body':'How to play instructions will be shown here.', 'footer':''};
  createModal("how-to-play", content, "main-content");
}
 
function createMainGameMenuModal() {
  var content = {'body':'Game menu modal displays websites and options.', 'footer':''};
  createModal("main-game-menu", content, "main-content");
}

 
function createWorldDescriptionModal() {
  var content = {'body':'World descriptions will be shown here.', 'footer':''};
  createModal("world-description", content, "main-content");
}
