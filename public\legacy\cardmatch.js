
class CardMatch extends BaseGame {
    constructor() {
        super();
        this.cards = [];
        this.selectedCards = [];
        this.matchedPairs = 0;
        this.canClick = true;
        this.level = 1; // Initialize level

        // Array to hold the image paths
        this.imagePaths = [
            '../images/games/fruit/apple.png',
            '../images/games/fruit/bananas.png',
            '../images/games/fruit/blackberry.png',
            '../images/games/fruit/blueberry.png',
            '../images/games/fruit/candy1.png',
            '../images/games/fruit/candy2.png',
            '../images/games/fruit/candy3.png',
            '../images/games/fruit/green_apple.png',
            '../images/games/fruit/mocha3.png',
            '../images/games/fruit/peach.png',
            '../images/games/fruit/raspberry.png',
            '../images/games/fruit/strawberry.png'
        ];

        // Load the card front and back images
        this.cardFrontTexture = PIXI.Texture.from('../images/games/fruit/card-front.png');
        this.cardBackTexture = PIXI.Texture.from('../images/games/fruit/card-back.png');
    }

    async loadImages() {
        // Load all images and store them in an array
        this.images = await Promise.all(this.imagePaths.map(path => PIXI.Texture.from(path)));
    }

    createCard(texture, x, y) {
        const card = new PIXI.Container();
        card.x = x;
        card.y = y;

        // Create card front (using card-front.png)
        const front = new PIXI.Sprite(this.cardFrontTexture);
        front.width = 80;
        front.height = 100;

        // Create card back (using card-back.png)
        const back = new PIXI.Sprite(this.cardBackTexture);
        back.width = 80;
        back.height = 100;
        back.visible = false;

        // Create fruit image (hidden initially)
        const fruit = new PIXI.Sprite(texture);
        fruit.width = 80;
        fruit.height = 100;
        fruit.visible = false;

        card.addChild(front, back, fruit);

        card.interactive = true;
        card.buttonMode = true;
        card.texture = texture;
        card.front = front;
        card.back = back;
        card.fruit = fruit;

        card.on('pointerdown', () => this.onCardClick(card));

        return card;
    }

    onCardClick(card) {
        if (!this.canClick || card.fruit.visible || this.selectedCards.includes(card)) return;

        // Show the card back and the fruit image
        card.front.visible = false;
        card.back.visible = true;
        card.fruit.visible = true;

        this.selectedCards.push(card);

        if (this.selectedCards.length === 2) {
            this.canClick = false;
            this.checkMatch();
        }
    }

    checkMatch() {
        const [card1, card2] = this.selectedCards;

        if (card1.texture === card2.texture) {
            this.matchedPairs++;
            this.score += 50;
            this.selectedCards = [];
            this.canClick = true;

            // Check if all pairs are matched (6 pairs)
            if (this.matchedPairs === 6) {
                this.level++; // Increase level
                this.nextLevel(); // Proceed to the next level
            }
        } else {
            setTimeout(() => {
                // Hide the fruit images and show the card front again
                card1.front.visible = true;
                card1.back.visible = false;
                card1.fruit.visible = false;

                card2.front.visible = true;
                card2.back.visible = false;
                card2.fruit.visible = false;

                this.selectedCards = [];
                this.canClick = true;
            }, 1000);
        }
        this.updateHUD();
    }

    async startGame() {
        // First check if player has enough credits
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }

        try {
            // Attempt to subtract 1 credit from the player's account
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);

            // Continue with game initialization after successful transaction
            await this.loadImages();
            this.resetGame();
            this.initGame();
            const startScreen = document.getElementById('start-screen');
            const gameArea = document.getElementById('game-area');
            const gameOverScreen = document.getElementById('game-over-screen');
            if (startScreen) startScreen.style.display = 'none';
            if (gameArea) gameArea.style.display = 'block';
            if (gameOverScreen) gameOverScreen.style.display = 'none';
            this.nextLevel();
        } catch (error) {
            console.error('ERROR: Failed to start CardMatch game:', error);
            showAlert("Failed to start game. Please try again.");
        }
    }

    nextLevel() {
        // Clear the stage and reset game state for the next level
        this.resetGame();
        // Step 1: Randomly select 6 unique images from the loaded images
        const selectedImages = this.getRandomImages(6);
        // Step 2: Create pairs of cards using the selected images
        const allTextures = [...selectedImages, ...selectedImages]; // Each image is used twice
        // Step 3: Shuffle the card textures with increasing randomness based on level
        this.shuffleArray(allTextures, this.level);
        // Step 4: Position cards in a 4x3 grid (12 cards)
        for (let i = 0; i < 12; i++) {
            const x = (i % 4) * 100 + 60; // Fixed grid layout
            const y = Math.floor(i / 4) * 120 + 60;
            const card = this.createCard(allTextures[i], x, y);
            this.cards.push(card);
            this.app.stage.addChild(card);
        }
        // Start game timer based on level
        this.startTimer();
    }

    shuffleArray(array, level) {
        // Increase the number of shuffles based on the level
        const shuffleCount = level * 10; // More shuffles as level increases
        for (let i = 0; i < shuffleCount; i++) {
            const index1 = Math.floor(Math.random() * array.length);
            const index2 = Math.floor(Math.random() * array.length);
            [array[index1], array[index2]] = [array[index2], array[index1]]; // Swap elements
        }
    }

    startTimer() {
        // Clear any existing interval to prevent multiple timers from running
        if (this.gameInterval) {
            clearInterval(this.gameInterval);
        }

      let timeLimit;
      if (this.level === 1) {
        timeLimit = 120;
      } else if (this.level >= 2 && this.level <= 3) {
        timeLimit = 90;
      } else if (this.level >= 4 && this.level <= 6) {
        timeLimit = 60;
      } else if (this.level >= 7 && this.level <= 9) {
        timeLimit = 45;
      } else if (this.level >= 10 && this.level <= 15) {
        timeLimit = 30;
      } else {
        timeLimit = 30; // Default for levels beyond 15
      }
        this.time = timeLimit;
        this.updateHUD();
        // Start a new interval
        this.gameInterval = setInterval(() => {
            this.time--;
            if (this.time <= 0) {
                clearInterval(this.gameInterval);
                this.time = 0; // Ensure time is set to 0
                this.updateHUD(); // Update HUD to reflect time as 0
                this.endGame("Time's up!");
            }
            this.updateHUD();
        }, 1000);
    }

    resetGame() {
        // Clear the stage and remove all cards
        this.cards.forEach(card => this.app.stage.removeChild(card));
        this.cards = [];
        this.selectedCards = [];
        this.matchedPairs = 0; // Reset matched pairs counter
        this.canClick = true;
        //this.score = 0; // Reset score
        this.time = 0; // Reset timer
        this.updateHUD(); // Update the HUD to reflect the reset state
    }

    getRandomImages(count) {
        // Shuffle the images array and select the first `count` images
        const shuffledImages = [...this.images];
        this.shuffleArray(shuffledImages, 1); // Initial shuffle
        return shuffledImages.slice(0, count);
    }

    stopGame() {
        this.cards = [];
        this.selectedCards = [];
        this.matchedPairs = 0;
        super.stopGame();
    }
}
