const axios = require("axios");
const { app_url } = require("../../config");
const systemLogging = require("./system-logging");

async function addReward(wax_id, event_id, type, title, desc, schema, template, amt, created_date = new Date(), status = "Unclaimed") {
  try {
    const checkResponse = await axios.get(`${app_url}/rewards/eventid/${event_id}`);
    if (checkResponse.data && checkResponse.data.length > 0) {
      return false;
    }
  } catch (checkError) {
    systemLogging.error(`Error checking for existing reward for event_id: ${event_id}`, checkError);
  }

  const newDate = created_date || new Date();
  let newReward = {
    "wax_id": wax_id,
    "event_id": event_id,
    "type": type,
    "title": title,
    "description": desc,
    "schema": schema,
    "template_id": template,
    "amount": amt,
    "created_date": newDate,
    "status": status
  }

  const config = {
    headers: {
      "Content-Type": "application/json",
    },
  }

  try {
    const response = await axios.post(
      `${app_url}/rewards/`,
      newReward,
      config
    );

    try {
      const verifyResponse = await axios.get(`${app_url}/rewards/eventid/${event_id}`);
      if (verifyResponse.data && verifyResponse.data.length > 0) {
        return true;
      } else {
        systemLogging.error(`Reward verification failed for event_id: ${event_id}`);
        return false;
      }
    } catch (verifyError) {
      systemLogging.error(`Error verifying reward creation for event_id: ${event_id}`, verifyError);
      return false;
    }
  } catch (error) {
    systemLogging.error(`Failed to add reward for event_id: ${event_id}`, error);
    return false;
  }
}

async function updateRewardDisbStatus(event_id) {
  try {
    const checkResponse = await axios.get(`${app_url}/rewards/eventid/${event_id}`);
    if (!checkResponse.data || checkResponse.data.length === 0) {
      systemLogging.error(`No reward found for event_id: ${event_id}`);
      return false;
    }
  } catch (checkError) {
    systemLogging.error(`Error checking for reward for event_id: ${event_id}`, checkError);
    return false;
  }

  const url = `${app_url}/rewards/sys/${event_id}`;
  const disbursalDate = new Date();
  const change = {
    "event_id": event_id,
    "disbursed_date": disbursalDate,
    "status": "Disbursed"
  };

  const config = {
    headers: {
      "Content-Type": "application/json",
    },
  };

  try {
    const response = await axios.put(url, change, config);
    return true;
  } catch (error) {
    systemLogging.error(`Failed to update reward status for event_id: ${event_id}`, error);
    return false;
  }
}

async function updatePlayerBalances(ownerId, data) {
  const newDate = new Date();
  const url = `${app_url}/players/${ownerId}`;
  const change = {
    gxp: parseFloat(data.gxp).toFixed(2),
    last_online: newDate,
    nectar: data.nectar,
    credits: data.credits,
  };
  const config = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  try {
    const response = await axios.put(url, change, config);
  } catch (error) {
    systemLogging.error(`Failed to update player balances for ownerId: ${ownerId}`, error);
  }
}

async function updatePlayerXP(id, data) {
  const url = `${app_url}/xp/${id}`;
  const change = {
    xp: Number(data.xp),
  };
  const config = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  try {
    const response = await axios.put(url, change, config);
  } catch (error) {
    systemLogging.error(`Failed to update player XP for id: ${id}`, error);
  }
}

module.exports = {
  addReward,
  updateRewardDisbStatus,
  updatePlayerBalances,
  updatePlayerXP
};