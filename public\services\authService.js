class AuthService {
  static async login() {
    try {
      // Check if wax object is available
      if (typeof wax === 'undefined') {
        throw new Error('WAX wallet not initialized. Please refresh the page and try again.');
      }

      console.log('[AuthService] Starting WAX login process...');
      console.log('[AuthService] WAX object available:', typeof wax);
      console.log('[AuthService] WAX methods available:', Object.keys(wax));
      console.log('[AuthService] WAX state:', {
        userAccount: wax.userAccount,
        pubKeys: wax.pubKeys,
        rpcEndpoint: wax.rpcEndpoint,
        isAutoLoginAvailable: typeof wax.isAutoLoginAvailable === 'function' ? wax.isAutoLoginAvailable() : 'method not available'
      });

      // Test if WAX login method exists and is callable
      if (typeof wax.login !== 'function') {
        throw new Error('WAX login method is not available. WAX library may not be properly loaded.');
      }

      console.log('[AuthService] Calling wax.login() - this will show the WAX Cloud Wallet popup...');

      // Add a small delay to see if this helps with timing
      await new Promise(resolve => setTimeout(resolve, 100));

      // This is where the WAX Cloud Wallet popup should appear
      // The user must sign in to the popup before we get userAccount and pubKeys
      console.log('[AuthService] About to call wax.login()...');
      const userAccount = await wax.login();
      console.log('[AuthService] wax.login() completed successfully!');

      console.log('[AuthService] WAX login successful! User signed into popup.');
      console.log('[AuthService] userAccount:', userAccount);
      console.log('[AuthService] pubKeys after login:', wax.pubKeys);

      // Only NOW do we have the userAccount and pubKeys to validate the session
      window.userAccount = userAccount;

      console.log('[AuthService] Now validating session with server...');
      const isValidSession = await this.validateUser(userAccount);
      if (!isValidSession) {
        throw new Error(`Failed to validate session for user: ${userAccount}`);
      }

      console.log('[AuthService] Session validation successful!');
      return userAccount;
    } catch (error) {
      // Handle cases where error might not have a message property
      const errorMessage = error?.message || error?.toString() || 'Unknown authentication error';
      console.error("Authentication error:", errorMessage);
      console.error("Full error object:", error);

      // Don't show alert for user cancellation
      if (errorMessage.includes('user closed') || errorMessage.includes('User closed') || errorMessage.includes('cancelled')) {
        console.log('[AuthService] User cancelled login, not showing error alert');
        throw error; // Re-throw but don't show alert
      }

      throw new Error(errorMessage);
    }
  }

  static async validateUser(userAccount) {
    try {
      // Check if wax object and pubKeys are available
      if (typeof wax === 'undefined' || !wax.pubKeys || !wax.pubKeys[0]) {
        throw new Error('WAX wallet not properly initialized or no public keys available');
      }

      // Check if domain_url is available
      if (typeof domain_url === 'undefined') {
        throw new Error('Domain URL not configured. Please refresh the page and try again.');
      }

      const pubKey = wax.pubKeys[0];
      const url = `${domain_url}/sessions/user/${userAccount}`;
      const headers = {
        'Authorization': pubKey,
        'Content-Type': 'application/json',
      };
      console.debug('[validateUser] userAccount:', userAccount);
      console.debug('[validateUser] pubKey:', pubKey);
      console.debug('[validateUser] url:', url);
      console.debug('[validateUser] headers:', headers);
      console.debug('[validateUser] REQUEST: { method: GET, url:', url, ', headers:', headers, '}');

      const response = await axios.get(url, { headers });
      console.debug('[validateUser] response status:', response.status);
      console.debug('[validateUser] response data:', response.data);

      if (response.status === 200) {
        if (response.data && response.data[0] && response.data[0].token) {
          const rawToken = response.data[0].token;
          sessionToken = rawToken;
          console.debug('[validateUser] sessionToken set:', sessionToken);
          return true;
        } else {
          console.error('ERROR: validateUser - Token not found in response', response.data);
        }
      }

      console.error('ERROR: validateUser - Error during validation:', response.status, response.data);
      try {
        console.debug('[validateUser] Attempting to create session for user:', userAccount);
        await this.createSessionForUser(userAccount);
        return await this.validateUser(userAccount);
      } catch (createError) {
        console.error('ERROR: validateUser - Failed to create session:', createError, createError?.stack);
        return false;
      }
    } catch (error) {
      console.error('ERROR: validateUser - Exception:', error.message, error?.stack);
      console.debug('[validateUser] Exception details:', {
        userAccount,
        pubKey: wax.pubKeys[0],
        domain_url,
        error: error.toString(),
        stack: error?.stack
      });
      try {
        console.debug('[validateUser] Attempting to create session for user after exception:', userAccount);
        await this.createSessionForUser(userAccount);
        return await this.validateUser(userAccount);
      } catch (createError) {
        console.error('ERROR: validateUser - Failed to create session after error:', createError, createError?.stack);
        return false;
      }
    }
  }

  static async createSessionForUser(userAccount) {
    try {
      // Check if wax object and pubKeys are available
      if (typeof wax === 'undefined' || !wax.pubKeys || !wax.pubKeys[0]) {
        console.error('[createSessionForUser] WAX wallet not properly initialized for user:', userAccount);
        throw new Error('WAX wallet not properly initialized or no public keys available');
      }

      const pubKey = wax.pubKeys[0];
      if (!pubKey) {
        console.error('[createSessionForUser] Public key not available for user:', userAccount);
        throw new Error('Public key not available');
      }
      const postUrl = `${domain_url}/sessions`;
      const postData = {
        user: userAccount,
        token: pubKey
      };
      console.debug('[createSessionForUser] userAccount:', userAccount);
      console.debug('[createSessionForUser] pubKey:', pubKey);
      console.debug('[createSessionForUser] postUrl:', postUrl);
      console.debug('[createSessionForUser] postData:', postData);
      console.debug('[createSessionForUser] REQUEST: { method: POST, url:', postUrl, ', headers: (default axios), data:', postData, '}');

      const response = await axios.post(postUrl, postData);
      console.debug('[createSessionForUser] response status:', response.status);
      console.debug('[createSessionForUser] response data:', response.data);

      if (response.status === 200 || response.status === 201) {
        return response.data;
      } else {
        console.error('ERROR: createSessionForUser - Failed to create session:', response.status, response.data);
        throw new Error('Failed to create session');
      }
    } catch (error) {
      console.error('ERROR: createSessionForUser - Exception:', error.message, error?.stack);
      console.debug('[createSessionForUser] Exception details:', {
        userAccount,
        pubKey: wax.pubKeys[0],
        domain_url,
        error: error.toString(),
        stack: error?.stack
      });
      throw error;
    }
  }
}

async function login() {
  try {
    // Check if required dependencies are available
    if (typeof AuthService === 'undefined') {
      throw new Error('AuthService not loaded. Please refresh the page and try again.');
    }
    if (typeof UIService === 'undefined') {
      throw new Error('UIService not loaded. Please refresh the page and try again.');
    }
    if (typeof DataService === 'undefined') {
      throw new Error('DataService not loaded. Please refresh the page and try again.');
    }

    const userAccount = await AuthService.login();
    UIService.showAuthenticatedUI(userAccount);

    // Initialize player options after successful login
    if (typeof initializePlayerOptionsOnLogin === 'function') {
      initializePlayerOptionsOnLogin(userAccount);
    }

    await DataService.fetchInitialData(userAccount);
    await displayWorlds();
    mainTimer = DataService.setupPeriodicUpdates(userAccount);

    if (sessionToken && !sessionToken.startsWith('Bearer ')) {
      sessionToken = `Bearer ${sessionToken}`;
    }

    return sessionToken;
  } catch (error) {
    console.error('ERROR: Login process error:', error);
    const errorMessage = error?.message || error?.toString() || 'Unknown login error';

    // Don't show alert for user cancellation - user intentionally closed the popup
    if (errorMessage.includes('user closed') || errorMessage.includes('User closed') || errorMessage.includes('cancelled')) {
      console.log('[Login] User cancelled login, not showing error alert');
      throw error; // Re-throw but don't show alert
    }

    if (typeof showAlert === 'function') {
      showAlert(errorMessage);
    } else {
      alert(errorMessage); // Fallback if showAlert is not available
    }
    throw error;
  }
}