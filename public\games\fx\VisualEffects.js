// Visual Effects - Consistent canvas effects for mini games
// Note: Individual effect files are loaded separately via script tags

class VisualEffects {
    constructor() {
        this.particlePool = [];
        this.maxParticles = 100;
        this.initParticlePool();
        
        // Initialize effect instances
        this.explosion = new ExplosionEffect(this);
        this.sparkle = new SparkleEffect(this);
        this.ripple = new RippleEffect(this);
        this.cardFlip = new CardFlipEffect(this);
        this.scorePopup = new ScorePopupEffect();
        this.buttonClick = new ButtonClickEffect(this);
        this.levelUp = new LevelUpEffect();
        this.gameOver = new GameOverEffect();
        this.backgroundParticles = new BackgroundParticlesEffect(this);
        this.pulse = new PulseEffect();
        this.whiteBlink = new WhiteBlinkEffect();
        this.wind = null; // Wind effect instance
    }

    // Initialize particle pool for performance
    initParticlePool() {
        for (let i = 0; i < this.maxParticles; i++) {
            this.particlePool.push(new PIXI.Graphics());
        }
    }

    // Get particle from pool
    getParticle() {
        return this.particlePool.pop() || new PIXI.Graphics();
    }

    // Return particle to pool
    returnParticle(particle) {
        if (this.particlePool.length < this.maxParticles) {
            particle.clear();
            this.particlePool.push(particle);
        }
    }

    // Delegate methods to effect instances
    createExplosion(x, y, color = 0xFFD700, particleCount = 15, stage) {
        this.explosion.create(x, y, color, particleCount, stage);
    }

    createSparkle(x, y, stage, color = 0xFFFFFF) {
        this.sparkle.create(x, y, stage, color);
    }

    createRipple(x, y, stage, color = 0x00FFFF) {
        this.ripple.create(x, y, stage, color);
    }

    createCardFlip(card, onComplete) {
        this.cardFlip.create(card, onComplete);
    }

    createCardFlipBack(card, onComplete) {
        this.cardFlip.createFlipBack(card, onComplete);
    }

    createScorePopup(x, y, score, stage, color = 0x00FF00) {
        this.scorePopup.create(x, y, score, stage, color);
    }

    createButtonClick(x, y, stage) {
        this.buttonClick.create(x, y, stage);
    }

    createLevelUpEffect(stage, level, onComplete) {
        this.levelUp.create(stage, level, onComplete);
    }

    createGameOverEffect(stage, result) {
        this.gameOver.create(stage, result);
    }

    createBackgroundParticles(stage, count = 20) {
        return this.backgroundParticles.create(stage, count);
    }

    createPulseEffect(target, options = 1000) {
        // Handle both old API (duration) and new API (options object)
        if (typeof options === 'number') {
            // Old API: createPulseEffect(target, duration)
            this.pulse.createSinglePulse(target, options);
        } else {
            // New API: createPulseEffect(target, options)
            this.pulse.create(target, options);
        }
    }

    createWhiteBlink(sprite, duration = 120, onComplete) {
        this.whiteBlink.create(sprite, duration, onComplete);
    }
 
    createTracer(sprite, options) {
        if (window.TracerFX && typeof window.TracerFX.addTracer === 'function') {
            window.TracerFX.addTracer(sprite, options);
        }
    } 

    createWindEffect(stage, options) {
        if (this.wind) {
            this.wind.remove();
            this.wind = null;
        }
        if (window.WindEffect) {
            this.wind = new window.WindEffect(options);
            this.wind.addToStage(stage);
            return this.wind;
        }
        return null;
    }
}

// Global instance for easy access
window.VisualEffects = new VisualEffects(); 