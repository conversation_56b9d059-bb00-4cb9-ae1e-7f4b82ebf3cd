async function fetchAndFilterInventoryItems(selectedSchema, filterCondition = null) {
    let items = await fetchAndFilterItems(selectedSchema, filterCondition);
    const filterType = $('#inventory-filter-dropdown').val();
    if (filterType) {
        switch (filterType) {
            case 'mint-asc':
                items.sort((a, b) => {
                    const mintA = Number(a.template_mint) || 0;
                    const mintB = Number(b.template_mint) || 0;
                    return mintA - mintB;
                });
                break;
            case 'mint-desc':
                items.sort((a, b) => {
                    const mintA = Number(a.template_mint) || 0;
                    const mintB = Number(b.template_mint) || 0;
                    return mintB - mintA;
                });
                break;
            case 'name-asc':
                items.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name-desc':
                items.sort((a, b) => b.name.localeCompare(b.name));
                break;
            case 'species':
                if (selectedSchema === 'creature') {
                    items.sort((a, b) => a.data.species.localeCompare(b.data.species));
                }
                break;
            case 'terrain':
                if (selectedSchema === 'vehicles') {
                    items.sort((a, b) => {
                        const terrainA = a.data.terrain || a.template.immutable_data.terrain;
                        const terrainB = b.data.terrain || b.template.immutable_data.terrain;
                        return terrainA.localeCompare(terrainB);
                    });
                }
                break;
            case 'type':
                if (selectedSchema === 'items') {
                    items.sort((a, b) => {
                        const typeA = a.data.type || '';
                        const typeB = b.data.type || '';
                        return typeA.localeCompare(typeB);
                    });
                }
                break;
            case 'in-team':
                break;
            case 'not-in-team':
                break;
        }
    }
    return items;
}

function addInventoryFilterDropdown(selectedSchema, insertionDiv) {
    $('#inventory-filter-container').remove();
    const filterContainer = $('<div>', {
        id: 'inventory-filter-container',
        class: 'inventory-filter-container'
    });
    const filterLabel = $('<label>', {
        for: 'inventory-filter-dropdown',
        text: 'Sort: '
    });
    const filterDropdown = $('<select>', {
        id: 'inventory-filter-dropdown',
        class: 'inventory-filter-dropdown'
    });
    updateFilterDropdownOptions(selectedSchema, insertionDiv, filterDropdown);
    filterDropdown.on('change', function() {
        const selectedFilter = $(this).val();
        applyInventoryFilter(selectedSchema, insertionDiv, selectedFilter);
    });
    filterContainer.append(filterLabel, filterDropdown);
    $('#inventory').find('button:last').after(filterContainer);
}

function updateFilterDropdownOptions(selectedSchema, _, dropdown = null) {
    const filterDropdown = dropdown || $('#inventory-filter-dropdown');
    filterDropdown.empty();
    const activeTab = determineActiveTab();
    let options = [];

    if (activeTab === 'inventory') {
        switch (selectedSchema) {
            case 'houses':
                options = [
                    { value: 'mint-asc', text: 'Mint # (Low to High)' },
                    { value: 'mint-desc', text: 'Mint # (High to Low)' },
                    { value: 'name-asc', text: 'Name (A-Z)' },
                    { value: 'name-desc', text: 'Name (Z-A)' },
                    { value: 'not-in-team', text: 'Available' },
                    { value: 'in-team', text: 'By Team' }
                ];
                break;
            case 'vehicles':
                options = [
                    { value: 'mint-asc', text: 'Mint # (Low to High)' },
                    { value: 'mint-desc', text: 'Mint # (High to Low)' },
                    { value: 'name-asc', text: 'Name (A-Z)' },
                    { value: 'name-desc', text: 'Name (Z-A)' },
                    { value: 'not-in-team', text: 'Available' },
                    { value: 'in-team', text: 'By Team' },
                    { value: 'terrain', text: 'By Terrain' }
                ];
                break;
            case 'creature':
                options = [
                    { value: 'mint-asc', text: 'Mint # (Low to High)' },
                    { value: 'mint-desc', text: 'Mint # (High to Low)' },
                    { value: 'name-asc', text: 'Name (A-Z)' },
                    { value: 'name-desc', text: 'Name (Z-A)' },
                    { value: 'not-in-team', text: 'Available' },
                    { value: 'in-team', text: 'By Team' },
                    { value: 'species', text: 'By Species' }
                ];
                break;
            case 'items':
                options = [
                    { value: 'mint-asc', text: 'Mint # (Low to High)' },
                    { value: 'mint-desc', text: 'Mint # (High to Low)' },
                    { value: 'name-asc', text: 'Name (A-Z)' },
                    { value: 'name-desc', text: 'Name (Z-A)' },
                    { value: 'type', text: 'By Type' }
                ];
                break;
            case 'coins':
                options = [
                    { value: 'mint-asc', text: 'Mint # (Low to High)' },
                    { value: 'mint-desc', text: 'Mint # (High to Low)' },
                    { value: 'name-asc', text: 'Name (A-Z)' },
                    { value: 'name-desc', text: 'Name (Z-A)' }
                ];
                break;
            default:
                options = [
                    { value: 'mint-asc', text: 'Mint # (Low to High)' },
                    { value: 'mint-desc', text: 'Mint # (High to Low)' },
                    { value: 'name-asc', text: 'Name (A-Z)' },
                    { value: 'name-desc', text: 'Name (Z-A)' }
                ];
        }
    } else if (activeTab === 'teams') {
        const teamStatus = getActiveTeamStatus();
        if (teamStatus === 'Ready') {
            options = [
                { value: 'terrain', text: 'By Terrain' },
                { value: 'housed', text: 'By Housed' },
                { value: 'capacity', text: 'By Capacity Filled' },
                { value: 'name-asc', text: 'Team Name (A-Z)' },
                { value: 'name-desc', text: 'Team Name (Z-A)' }
            ];
        } else if (teamStatus === 'Napping') {
            options = [
                { value: 'time-left', text: 'By Time Left' },
                { value: 'housed', text: 'By Housed' },
                { value: 'capacity', text: 'By Capacity Filled' },
                { value: 'name-asc', text: 'Team Name (A-Z)' },
                { value: 'name-desc', text: 'Team Name (Z-A)' }
            ];
        }
    } else if (activeTab === 'adventure') {
        const adventureStatus = getActiveAdventureStatus();
        if (adventureStatus === 'Complete' || adventureStatus === 'In Progress') {
            options = [
                { value: 'world', text: 'By World' },
                { value: 'name-asc', text: 'Team Name (A-Z)' },
                { value: 'name-desc', text: 'Team Name (Z-A)' }
            ];
        }
    }

    options.forEach(option => {
        filterDropdown.append($('<option>', {
            value: option.value,
            text: option.text
        }));
    });
}

function determineActiveTab() {
    if ($('#inventory').is(':visible')) {
        return 'inventory';
    } else if ($('#teams').is(':visible')) {
        return 'teams';
    } else if ($('#adventure').is(':visible')) {
        return 'adventure';
    } else if ($('#games').is(':visible')) {
        return 'games';
    }
    return 'inventory';
}

function getActiveTeamStatus() {
    const readyButton = $('#teams button:contains("Ready")');
    const nappingButton = $('#teams button:contains("Napping")');

    if (readyButton.hasClass('inventory-active-view')) {
        return 'Ready';
    } else if (nappingButton.hasClass('inventory-active-view')) {
        return 'Napping';
    }

    return 'Ready';
}

function getActiveAdventureStatus() {
    const completeButton = $('#adventure button:contains("Complete")');
    const inProgressButton = $('#adventure button:contains("In Progress")');

    if (completeButton.hasClass('inventory-active-view')) {
        return 'Complete';
    } else if (inProgressButton.hasClass('inventory-active-view')) {
        return 'In Progress';
    }

    return 'In Progress';
}

function applyInventoryFilter(selectedSchema, insertionDiv, filterType) {
    let filterCondition = null;
    const activeTab = determineActiveTab();

    if (activeTab === 'inventory') {
        if (filterType === 'in-team') {
            filterCondition = inTeamFilter;
        } else if (filterType === 'not-in-team') {
            filterCondition = notInTeamFilter;
        }
        displayInventory(selectedSchema, insertionDiv, filterCondition);
    } else if (activeTab === 'teams') {
        applyTeamsFilter(filterType);
    } else if (activeTab === 'adventure') {
        applyAdventuresFilter(filterType);
    }
}

function addTeamsFilterDropdown() {
    $('#inventory-filter-container').remove();
    const filterContainer = $('<div>', {
        id: 'inventory-filter-container',
        class: 'inventory-filter-container'
    });
    const filterLabel = $('<label>', {
        for: 'inventory-filter-dropdown',
        text: 'Sort: '
    });
    const filterDropdown = $('<select>', {
        id: 'inventory-filter-dropdown',
        class: 'inventory-filter-dropdown'
    });
    updateFilterDropdownOptions(null, null, filterDropdown);
    filterDropdown.on('change', function() {
        const selectedFilter = $(this).val();
        applyInventoryFilter(null, 'general-inventory', selectedFilter);
    });
    filterContainer.append(filterLabel, filterDropdown);
    $('#teams').find('button:last').after(filterContainer);
}

function addAdventuresFilterDropdown() {
    $('#inventory-filter-container').remove();
    const filterContainer = $('<div>', {
        id: 'inventory-filter-container',
        class: 'inventory-filter-container'
    });
    const filterLabel = $('<label>', {
        for: 'inventory-filter-dropdown',
        text: 'Sort: '
    });
    const filterDropdown = $('<select>', {
        id: 'inventory-filter-dropdown',
        class: 'inventory-filter-dropdown'
    });
    updateFilterDropdownOptions(null, null, filterDropdown);
    filterDropdown.on('change', function() {
        const selectedFilter = $(this).val();
        applyInventoryFilter(null, 'general-inventory', selectedFilter);
    });
    filterContainer.append(filterLabel, filterDropdown);
    $('#adventure').find('button:last').after(filterContainer);
}

function applyTeamsFilter(filterType) {
    const teamStatus = getActiveTeamStatus();
    const teamsDiv = $('#general-inventory');
    showInventoryLoadingSpinner(teamsDiv);
    getPlayerCoreData().then(coreData => {
        let filteredTeams = coreData.teams.filter(team => team.status === teamStatus);
        if (filterType !== 'default') {
            switch (filterType) {
                case 'terrain':
                    filteredTeams.sort((a, b) => {
                        const teamVehicleA = findTeamData(myTeams, a.team_id, 'vehicles', 0);
                        const teamVehicleB = findTeamData(myTeams, b.team_id, 'vehicles', 0);
                        const terrainA = getImmutableData(teamVehicleA, 'terrain', vehiclesData) || 'land';
                        const terrainB = getImmutableData(teamVehicleB, 'terrain', vehiclesData) || 'land';
                        return terrainA.localeCompare(terrainB);
                    });
                    break;
                case 'housed':
                    filteredTeams.sort((a, b) => {
                        const housedA = a.data.house !== 'None' ? 1 : 0;
                        const housedB = b.data.house !== 'None' ? 1 : 0;
                        return housedB - housedA;
                    });
                    break;
                case 'capacity':
                    filteredTeams.sort((a, b) => {
                        const capacityA = a.data.creatures ? a.data.creatures.length : 0;
                        const capacityB = b.data.creatures ? b.data.creatures.length : 0;
                        return capacityB - capacityA;
                    });
                    break;
                case 'name-asc':
                    filteredTeams.sort((a, b) => a.team_name.localeCompare(b.team_name));
                    break;
                case 'name-desc':
                    filteredTeams.sort((a, b) => b.team_name.localeCompare(b.team_name));
                    break;
                case 'time-left':
                    if (teamStatus === 'Napping') {
                        filteredTeams.sort((a, b) => {
                            const timeLeftA = a.nap_total - a.napk_current;
                            const timeLeftB = b.nap_total - b.nap_current;
                            return timeLeftA - timeLeftB;
                        });
                    }
                    break;
            }
        }
        teamsDiv.empty(); // Clear the spinner before adding content
        if (filteredTeams.length === 0) {
            const noTeamsMessage = createInventoryStatusMessage(`No teams found for this status: ${teamStatus}`);
            teamsDiv.append(noTeamsMessage);
        } else {
            filteredTeams.forEach(team => {
                const teamItem = createTeamItem(team);
                teamsDiv.append(teamItem);
            });
        }
    });
}

function applyAdventuresFilter(filterType) {
    const adventureStatus = getActiveAdventureStatus();
    const adventuresDiv = $('#general-inventory');
    showInventoryLoadingSpinner(adventuresDiv);
    let filteredAdventures = adventures.filter(adventure => adventure.status === adventureStatus);
    if (filterType !== 'default') {
        switch (filterType) {
            case 'world':
                filteredAdventures.sort((a, b) => a.mapgrid_4 - b.mapgrid_4);
                break;
            case 'name-asc':
                filteredAdventures.sort((a, b) => {
                    const teamA = myTeams.find(team => team.id === a.team_id);
                    const teamB = myTeams.find(team => team.id === b.team_id);
                    const nameA = teamA ? teamA.team_name : '';
                    const nameB = teamB ? teamB.team_name : '';
                    return nameA.localeCompare(nameB);
                });
                break;
            case 'name-desc':
                filteredAdventures.sort((a, b) => {
                    const teamA = myTeams.find(team => team.id === a.team_id);
                    const teamB = myTeams.find(team => team.id === b.team_id);
                    const nameA = teamA ? teamA.team_name : '';
                    const nameB = teamB ? teamB.team_name : '';
                    return nameB.localeCompare(nameA);
                });
                break;
        }
    }
    adventuresDiv.empty(); // Clear the spinner before adding content
    if (filteredAdventures.length === 0) {
        const noAdventuresMessage = createInventoryStatusMessage(`No adventures found for this status: ${adventureStatus}`);
        adventuresDiv.append(noAdventuresMessage);
    } else {
        filteredAdventures.forEach(adventure => {
            const adventureItem = createAdventureItem(adventure);
            adventuresDiv.append(adventureItem);
        });
    }
}

window.addInventoryFilterDropdown = addInventoryFilterDropdown;
window.updateFilterDropdownOptions = updateFilterDropdownOptions;
window.applyInventoryFilter = applyInventoryFilter;
window.addTeamsFilterDropdown = addTeamsFilterDropdown;
window.addAdventuresFilterDropdown = addAdventuresFilterDropdown;
window.determineActiveTab = determineActiveTab;
window.getActiveTeamStatus = getActiveTeamStatus;
window.getActiveAdventureStatus = getActiveAdventureStatus;

// Function to refresh the current tab's content
window.refreshCurrentTab = function() {
  const activeTab = determineActiveTab();
  
  if (activeTab === 'inventory') {
    displayInventory(currentInventoryView, 'general-inventory');
  } else if (activeTab === 'teams') {
    const teamStatus = getActiveTeamStatus();
    setSelectedTeamType(teamStatus);
  } else if (activeTab === 'adventure') {
    const adventureStatus = getActiveAdventureStatus();
    setSelectedAdventureType(adventureStatus);
  } else if (activeTab === 'games') {
    // For games tab, just ensure the inventory is empty
    $('#general-inventory').empty();
  }
};