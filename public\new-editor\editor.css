/* editor.css - Map Editor 3D Minimal Styling */

/* Base editor styles */
body {
  background: #181a1b;
  color: #fff;
  /* font-family: 'Press Start 2P', 'Common Pixel', monospace; */
}

.editor-flex-container {
  display: flex;
  flex-direction: row;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

#threejs-map-panel {
  flex: 2 1 0;
  background: #222;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 2px solid #333;
  position: relative;
}

#threejs-map-container {
  width: 700px;
  height: 700px;
  background: #111;
  border: 2px solid #333; 
  margin-top: 8em;
  position: relative;
}

#editor-console {
  background: #fff;
  color: #111;
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 10pt;
  padding: 24px 16px;
  min-width: 320px;
  max-width: 420px;
  gap: 16px;
}

.editor-console-section {
  background: #fff;
  border: 1px solid #eee;
  padding: 12px 10px;
  margin-bottom: 8px;
}

.editor-console-section label {
  font-size: 10pt;
  font-weight: 600;
  margin-bottom: 4px;
  color: #222;
}

.editor-console-section input,
.editor-console-section select {
  width: 100%;
  margin-bottom: 8px;
  background: #fff;
  color: #111;
  border: 1px solid #ccc;
  padding: 4px 6px;
  font-size: 10pt;
  font-family: 'Segoe UI', Arial, sans-serif;
}

/* Minimal button styles */
.btn, .mode-button, .editor-nav-button {
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 10pt;
  padding: 4px 10px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background: #fff;
  color: #111;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.15s, color 0.15s, border 0.15s;
  min-width: 64px;
  min-height: 28px;
  box-shadow: none;
  outline: none;
}

.btn:active, .mode-button:active, .editor-nav-button:active {
  background: #f0f0f0;
}

.btn-success, .btn-green {
  background: #eafbe7;
  color: #1a7f37;
  border: 1px solid #b6e2c6;
}

.btn-danger, .btn-red {
  background: #fdeaea;
  color: #c0392b;
  border: 1px solid #f5b7b1;
}

.btn-primary, .btn-blue, .mode-button.active, .editor-nav-button.active {
  background: #eaf1fb;
  color: #1761a0;
  border: 1px solid #b6cbe2;
}

.btn-secondary, .btn-unselected, .mode-button:not(.active), .editor-nav-button:not(.active) {
  background: #fff;
  color: #111;
  border: 1px solid #ccc;
}

/* Remove old button color rules */
.mode-button, .editor-nav-button {
  text-transform: none;
  font-weight: 500;
  border-width: 1px;
}

/* Mode selector buttons */
.mode-selector {
  margin-bottom: 20px;
}

.mode-selector label {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
}

.mode-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.mode-button {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #444;
  background: #292d32;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  font-family: 'Press Start 2P', cursive;
  text-transform: uppercase;
}

.mode-button.active {
  background: #007bff;
  border-color: #0056b3;
  color: #fff;
}

.navigation-label {
  position: absolute;
  margin-top: 1em;
  left: 50%;
  transform: translateX(-50%);
  background: #23272b;
  color: #00fff0;
  padding: 8px 16px;
  border: 1px solid #00fff0;
  font-size: 12px;
  font-family: 'Press Start 2P', cursive;
  text-transform: uppercase;
  z-index: 1000;
  text-align: center;
}

.navigation-label .world-info {
  color: #fff;
  font-size: 10px;
  margin-bottom: 2px;
}

.navigation-label .zone-info {
  color: #ffb347;
  font-size: 10px;
  margin-bottom: 2px;
}

.navigation-label .locale-info {
  color: #4CAF50;
  font-size: 10px;
}

/* Editor nav buttons */
.editor-nav-buttons {
  position: absolute;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
  z-index: 1000;
  pointer-events: auto;
  background: #23272b;
  padding: 8px;
  border: 1px solid #444;
}

.editor-nav-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  border: 2px solid #444;
  background: #292d32;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  white-space: nowrap;
  min-width: 80px;
  justify-content: center;
  font-family: 'Press Start 2P', cursive;
  text-transform: uppercase;
}

.editor-nav-button.active {
  background: #007bff;
  border-color: #0056b3;
  color: #fff;
}

.nav-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

/* Button groups */
#editor-changes-btns {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

#editor-changes-btns .btn {
  margin: 0;
  font-size: 12px;
  padding: 6px 12px;
}

/* Pending changes list */
#pendingChangesList {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #444;
  padding: 8px;
  background: #181a1b;
}

.change-item {
  margin-bottom: 8px;
  padding: 6px;
  background: #2a2a1b;
  border-left: 3px solid #ffb347;
  position: relative;
  font-size: 11px;
}

.change-item button {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ff4444;
  color: white;
  border: none;
  padding: 2px 6px;
  font-size: 10px;
  cursor: pointer;
}

/* Modal styling */
#editor-changes-modal .modal-dialog {
  max-width: 700px;
}

/* Minimal, white-theme for Review Changes modal */
#editor-changes-modal pre {
  background: none;
  color: #222;
  padding: 8px;
  border: none;
  font-size: 12px;
  font-family: 'Segoe UI', Arial, monospace;
  box-shadow: none;
}

#editor-changes-modal .modal-header, 
#editor-changes-modal .modal-footer, 
#editor-changes-modal .modal-content {
  background: #fff;
  color: #222;
  border: none;
}

.changed-row {
  font-weight: bold;
  color: #ffb347;
  background: #2a2a1b;
}

.modal-header, .modal-footer {
  background: #23272b;
  color: #fff;
}

.modal-content {
  background: #23272b;
  color: #fff;
}

/* Tile hover effects */
.tile-hover-info {
  background: #fff;
  color: #111;
  border: 1px solid #b6cbe2;
}

/* Additions for JS refactor */
.visible { display: block !important; }
.hidden { display: none !important; }

.mini-tile-preview {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  width: 24px;
  height: 24px;
  border: 2px solid #00fff0;
  background: #222;
  border-radius: 4px;
  box-shadow: 0 2px 8px #0002;
  display: none;
}

#editor-changes-row-json {
  overflow-y: auto;
  max-height: 220px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .editor-flex-container {
    flex-direction: column;
  }
  
  #threejs-map-panel {
    flex: 1;
    border-right: none;
    border-bottom: 2px solid #333;
  }
  
  #editor-console {
    max-height: 50vh;
  }
  
  #threejs-map-container {
    width: 90vw;
    height: 60vh;
    margin: 12px auto;
  }
}

@media (max-width: 768px) {
  .mode-buttons {
    flex-direction: column;
  }
  
  .editor-nav-buttons {
    flex-direction: column;
    gap: 2px;
  }
  
  .editor-nav-button, .mode-button {
    min-width: 48px;
    font-size: 9pt;
    padding: 4px 6px;
  }
  
  .navigation-label {
    font-size: 10px;
    padding: 6px 12px;
    min-width: 300px;
    top: 10%;
  }
} 