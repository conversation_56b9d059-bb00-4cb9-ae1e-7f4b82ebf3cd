const mini_games = [
    {
        "id": 1,
        "type": "land",
        "name": "Card Match",
        "image": "",
        "instructions": "Match the cards to score points. Time goes down each level you complete!",
        "description": "A simple card matching game."
    },
    {
        "id": 2,
        "type": "land",
        "name": "Treasure Frenzy",
        "image": "",
        "instructions": "Click the treasure chests to score points!",
         "description": "A simple clicking game."
    },
    {
        "id": 3,
        "type": "land",
        "name": "Forest Runner",
        "image": "",
        "description": "na"
    },
    {
        "id": 4,
        "type": "land",
        "name": "Stick Fighter",
        "image": "",
        "description": "na"
    },
    {
        "id": 5,
        "type": "land",
        "name": "Bouncy Click",
        "image": "",
        "instructions": "Click the colored balls to score points! Faster clicks means more points!",
         "description": "A simple click the balls to collect points game."
    },
    {
        "id": 19,
        "type": "land",
        "name": "Forest Scavenger",
        "image": "",
        "instructions": "Scavenge for items and avoid goblins! Use the keys WASD to move.",
        "description": "A top-down grid-based collection and goblin avoidance game!"
    },
    {
        "id": 6,
        "type": "land",
        "name": "Squirrel Jumper",
        "image": "",
        "description": "na"
    },
    {
        "id": 7,
        "type": "water",
        "name": "Fishing Frenzy",
        "image": "",
        "description": "na"
    },
    {
        "id": 8,
        "type": "water",
        "name": "Submarine Explorer",
        "image": "",
        "description": "na"
    },
    {
        "id": 9,
        "type": "water",
        "name": "Jetski Racer",
        "image": "",
        "description": "na"
    },
    {
        "id": 10,
        "type": "space",
        "name": "Meteor Dodger",
        "image": "",
        "description": "na"
    },
    {
        "id": 11,
        "type": "space",
        "name": "Rocket Pilot",
        "image": "",
        "description": "na"
    },
    {
        "id": 12,
        "type": "space",
        "name": "Alien Asteroids",
        "image": "",
        "description": "na"
    },
    {
        "id": 13,
        "type": "variety",
        "name": "Brick Breaker",
        "image": "",
        "description": "na"
    },
    {
        "id": 14,
        "type": "variety",
        "name": "Cloudhopper",
        "image": "",
        "description": "Cloudhopper platformer where we jump and try to go higher on platforms to get to the top cloud."
    },
    {
        "id": 15,
        "type": "variety",
        "name": "Flappy Bird",
        "image": "",
        "description": "na"
    },
    {
        "id": 16,
        "type": "variety",
        "name": "Snake",
        "image": "",
        "description": "na"
    },
    {
        "id": 17,
        "type": "variety",
        "name": "Pong",
        "image": "",
        "description": "na"
    },
    {
        "id": 18,
        "type": "variety",
        "name": "Draw the Word",
        "image": "",
        "instructions": "Can you draw it? Challenge your drawing skills!",
        "description": "Draw the challenge word to earn GXP."
    },
    {
        "id": 20,
        "type": "space",
        "name": "Laser Defender",
        "image": "",
        "instructions": "Move mouse to aim, click to shoot, spacebar for powerups!",
        "description": "Defend your spaceship! Destroy enemies and asteroids."
    }
];
