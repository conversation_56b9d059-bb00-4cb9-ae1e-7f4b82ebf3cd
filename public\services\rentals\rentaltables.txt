-- Creating a function to update the `updated_at` timestamp
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Updated rentals table
CREATE TABLE rentals (
    id SERIAL PRIMARY KEY,
    asset_id BIGINT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('house', 'vehicle', 'other')),
    owner_id VARCHAR(36) NOT NULL,
    renter_id VARCHAR(36),
    currency VARCHAR(12) NOT NULL CHECK (currency ~ '^[A-Z]{3}$'),
    unit_cost DECIMAL(10,2) NOT NULL CHECK (unit_cost >= 0),
    total_cost DECIMAL(10,2) GENERATED ALWAYS AS (unit_cost * duration) STORED,
    bal_paid DECIMAL(10,2) NOT NULL CHECK (bal_paid >= 0), 
	start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    duration INTEGER NOT NULL CHECK (duration IN (7, 14, 30, 60)),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'cancelled', 'completed', 'pending', 'hold_listed', 'hold_unlisted', 'hold_in_use')),  
    CONSTRAINT fk_owner FOREIGN KEY (owner_id) REFERENCES players(wax_id),
    CONSTRAINT valid_dates CHECK (end_date IS NULL OR end_date > start_date),
    CONSTRAINT valid_payment CHECK (bal_paid <= bal_total)
);

-- Trigger to update `updated_at` on rentals table
CREATE TRIGGER update_rentals_timestamp
BEFORE UPDATE ON rentals
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Exclusion constraint to prevent overlapping rentals
ALTER TABLE rentals
ADD CONSTRAINT no_overlapping_rentals
EXCLUDE USING gist (
    asset_id WITH =,
    tsrange(start_date, end_date, '[]') WITH &&
) WHERE (status IN ('active', 'pending', 'hold_in_use'));

-- Indexes for performance
CREATE INDEX idx_rentals_asset_id ON rentals(asset_id);
CREATE INDEX idx_rentals_owner_id ON rentals(owner_id);
CREATE INDEX idx_rentals_renter_id ON rentals(renter_id);

-- Updated escrow table
CREATE TABLE escrow (
    id SERIAL PRIMARY KEY,
    rental_id INTEGER NOT NULL,
    owner_id VARCHAR(36) NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL CHECK (currency ~ '^[A-Z]{3}$'),
    issued_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'held', 'disbursed', 'refunded', 'failed')),
    tx VARCHAR(255),
    CONSTRAINT fk_rental FOREIGN KEY (rental_id) REFERENCES rentals(id),
    CONSTRAINT fk_owner FOREIGN KEY (owner_id) REFERENCES players(wax_id)
);

-- Index for performance
CREATE INDEX idx_escrow_rental_id ON escrow(rental_id);