class TypingEffect {
    constructor(element, options = {}) {
        this.element = element;
        this.options = {
            speed: options.speed || 50, // milliseconds per character
            typeBy: options.typeBy || 'letter', // 'letter' or 'word'
            onComplete: options.onComplete || null,
            onProgress: options.onProgress || null
        };
        this.isTyping = false;
        this.currentIndex = 0;
        this.fullText = '';
    }

    type(text) {
        this.fullText = text;
        this.currentIndex = 0;
        this.isTyping = true;
        this.element.textContent = '';
        
        if (this.options.typeBy === 'word') {
            this.typeWord();
        } else {
            this.typeLetter();
        }
    }

    typeLetter() {
        if (this.currentIndex < this.fullText.length && this.isTyping) {
            this.element.textContent += this.fullText[this.currentIndex];
            this.currentIndex++;
            
            if (this.options.onProgress) {
                this.options.onProgress(this.currentIndex / this.fullText.length);
            }
            
            setTimeout(() => this.typeLetter(), this.options.speed);
        } else if (this.currentIndex >= this.fullText.length) {
            this.complete();
        }
    }

    typeWord() {
        const words = this.fullText.split(' ');
        
        if (this.currentIndex < words.length && this.isTyping) {
            if (this.currentIndex === 0) {
                this.element.textContent = words[0];
            } else {
                this.element.textContent += ' ' + words[this.currentIndex];
            }
            this.currentIndex++;
            
            if (this.options.onProgress) {
                this.options.onProgress(this.currentIndex / words.length);
            }
            
            setTimeout(() => this.typeWord(), this.options.speed);
        } else if (this.currentIndex >= words.length) {
            this.complete();
        }
    }

    complete() {
        this.isTyping = false;
        this.element.textContent = this.fullText;
        
        if (this.options.onComplete) {
            this.options.onComplete();
        }
    }

    skip() {
        this.isTyping = false;
        this.element.textContent = this.fullText;
        
        if (this.options.onComplete) {
            this.options.onComplete();
        }
    }

    stop() {
        this.isTyping = false;
    }
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = TypingEffect;
} else {
    window.TypingEffect = TypingEffect;
} 