async function addAdventure(team_id) {
  if (!team_id) {
    console.error('ERROR: addAdventure - team_id is undefined or null');
    showAlert('Error: Team ID is missing');
    return;
  }

  if (!newDestination) {
    showAlert('Error: Destination is not selected');
    return;
  }

  // Convert properties to numbers if they're strings
  if (typeof newDestination.world === 'string') {
    newDestination.world = Number(newDestination.world);
  }

  if (typeof newDestination.zone === 'string') {
    newDestination.zone = Number(newDestination.zone);
  }

  if (typeof newDestination.locale === 'string') {
    newDestination.locale = Number(newDestination.locale);
  }

  // Fix for the issue: Check if properties exist and are valid numbers (including 0)
  // Use typeof check instead of falsy check to handle 0 values correctly
  const worldValid = newDestination.world !== undefined && newDestination.world !== null && !isNaN(Number(newDestination.world));
  const zoneValid = newDestination.zone !== undefined && newDestination.zone !== null && !isNaN(Number(newDestination.zone));
  const localeValid = newDestination.locale !== undefined && newDestination.locale !== null && !isNaN(Number(newDestination.locale));

  if (!worldValid || !zoneValid || !localeValid) {
    showAlert('Error: Destination information is incomplete');
    return;
  }

  try {
    // Ensure all properties are numbers before calling API
    newDestination.world = Number(newDestination.world);
    newDestination.zone = Number(newDestination.zone);
    newDestination.locale = Number(newDestination.locale);
    const result = await addAdventureApi(Number(team_id));
    if (result.success) {
      AudioManager.playUISound('adventure');
      showAlert('Successfully added adventure!');

      // Check if newDestination is defined
      if (!newDestination || newDestination.locale === undefined) {
        console.error('ERROR: addAdventure - newDestination or locale is undefined after API call:', newDestination);
      } else {
        // Create a mock adventure object if needed
        const mockAdventure = {
          team_id: teamSelectedForAdventure,
          owner_id: wax.userAccount,
          mapgrid_256: newDestination.locale,
          current_steps: 0,
          init_steps: 100
        };

        // Display the vehicle icon
        displayVehicleMovingIcon('player', newDestination.locale, mockAdventure);
      }

      // Update logs and reload data
      displayGameLogs(wax.userAccount);
      await reloadPlayerTeamData('Ready');
      await updatePlayerCounters(playerCounter, myTeams, playerAdventures);
      
      // Refresh ThreeJS map if it's active and we're in locale view
      if (typeof refreshLocaleView === 'function') {
        await refreshLocaleView();
      }
    } else {
      if (result.error) {
        showAlert(`Oops! We had a problem.... ${result.error}`);
      } else {
        showAlert('Oops! An unknown error occurred');
      }
    }
  } catch (error) {
    showAlert(`Oops! An error occurred: ${error.message}`);
  }
}

async function deleteAdventure(adventure_id) {
  if (!adventure_id) {
    console.error('ERROR: deleteAdventure - adventure_id is undefined or null');
    showAlert('Error: Could not process adventure deletion. Missing adventure ID.');
    return;
  }

  const success = await deleteAdventureApi(adventure_id);

  if (success) {
    // Play sound effect
    if (typeof AudioManager !== 'undefined') {
      AudioManager.playUISound('cancel');
    }

    showAlert(`Successfully claimed reward! Your team will now nap.`);

    if (playerCounter.rewardsClaimable > 0) {
      await reloadPlayerAdventureData('Complete');
    } else {
      await reloadPlayerAdventureData('In Progress');
    }

    await updatePlayerCounters(playerCounter, myTeams, playerAdventures);
  } else {
    console.error('ERROR: deleteAdventure - Failed to delete adventure');
    showAlert('Error: Failed to claim reward. Please try again.');
  }
}