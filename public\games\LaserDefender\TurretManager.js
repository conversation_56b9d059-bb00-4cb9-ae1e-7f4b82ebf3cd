class LaserDefenderTurretManager {
    constructor(game) {
        this.game = game;
        this.turret = null;
        this.turretBody = null;
        this.turretX = 0;
        this.turretY = 0;
        this.lastShotTime = 0;
        this.shotCooldown = 300;
        this.reticleTimeout = null;
        this.reticle = null;
        this.movementController = null;
    }

    createTurret() {
        this.turret = new PIXI.Sprite(this.game.textures.turret);
        this.turret.anchor.set(0.5, 0.5);
        this.turret.x = this.turretX;
        this.turret.y = this.turretY;
        this.game.app.stage.addChild(this.turret);
        if (this.reticle) {
            this.game.app.stage.removeChild(this.reticle);
        }
        this.reticle = new PIXI.Sprite(this.game.textures.reticle1);
        this.reticle.anchor.set(0.5, 0.5);
        this.reticle.x = this.turretX;
        this.reticle.y = this.turretY;
        this.reticle.zIndex = 1000;
        this.game.app.stage.addChild(this.reticle);
        if (this.game.app.view) {
        }
        this.turretBody = Matter.Bodies.rectangle(
            this.turretX,
            this.turretY,
            32,
            32,
            { 
                isStatic: true,
                label: 'turret',
                collisionFilter: {
                    category: 0x0001,
                    mask: 0x0002
                }
            }
        );
        Matter.World.add(this.game.physicsManager.world, this.turretBody);
    }

    setupControls() {
        this.game.app.stage.interactive = true;
        this._fireInterval = null;
        this.game.app.stage.on('pointermove', (event) => {
            if (this.game.gameState === 'playing') {
                const mousePos = event.data.global;
                this.aimTurret(mousePos.x, mousePos.y);
                if (this.reticle) {
                    this.reticle.x = mousePos.x;
                    this.reticle.y = mousePos.y;
                }
            }
        });
        this.game.app.stage.on('pointerdown', (event) => {
            if (this.game.gameState === 'playing') {
                this.shoot();
                if (!this._fireInterval) {
                    this._fireInterval = setInterval(() => {
                        if (this.game.gameState === 'playing') this.shoot();
                    }, this.shotCooldown);
                }
            }
        });
        this.game.app.stage.on('pointerup', () => {
            if (this._fireInterval) {
                clearInterval(this._fireInterval);
                this._fireInterval = null;
            }
        });
        this.game.app.stage.on('pointerupoutside', () => {
            if (this._fireInterval) {
                clearInterval(this._fireInterval);
                this._fireInterval = null;
            }
        });
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
            this.game.addGameEventListener('click', (event) => {
                if (this.game.gameState === 'playing') {
                    this.shoot();
                }
            }, gameArea);
        }
        this.game.addGameEventListener('keydown', (event) => {
            if (event.code === 'Space' && this.game.gameState === 'playing') {
                event.preventDefault();
                this.game.powerupManager.activatePowerup();
            }
        }, document);
    }

    aimTurret(mouseX, mouseY) {
        const dx = mouseX - this.turretX;
        const dy = mouseY - this.turretY;
        const angle = Math.atan2(dy, dx);
        this.turret.rotation = angle + Math.PI / 2;
    }

    shoot() {
        const now = Date.now();
        if (now - this.lastShotTime < this.shotCooldown) return;
        this.lastShotTime = now;
        if (this.reticle && this.game.textures.reticle2) {
            this.reticle.texture = this.game.textures.reticle2;
            if (this.reticleTimeout) clearTimeout(this.reticleTimeout);
            this.reticleTimeout = setTimeout(() => {
                if (this.reticle && this.game.textures.reticle1) {
                    this.reticle.texture = this.game.textures.reticle1;
                }
            }, 100);
        }
        const angle = this.turret.rotation - Math.PI / 2;
        const speed = 8;
        if (this.game.powerupManager.activePowerups.spreadShot.active) {
            const angles = [angle - 0.2, angle, angle + 0.2];
            angles.forEach(shotAngle => {
                this.game.projectileManager.createPlayerProjectile(shotAngle, speed);
            });
        } else {
            this.game.projectileManager.createPlayerProjectile(angle, speed);
        }
        if (window.GameSounds) {
            window.GameSounds.playLaserShot();
        }
    }

    updateShotCooldown() {
        if (this.game.powerupManager.activePowerups.rapidFire.active) {
            this.shotCooldown = 150;
        } else {
            this.shotCooldown = this.game.powerupManager.activePowerups.rapidFire.originalCooldown;
        }
    }

    setTurretX(x) {
        const minX = 30;
        const maxX = this.game.app.screen.width - 30;
        const clampedX = Math.max(minX, Math.min(maxX, x));
        this.turretX = clampedX;
        if (this.turret) this.turret.x = clampedX;
        if (this.turretBody) Matter.Body.setPosition(this.turretBody, { x: clampedX, y: this.turretY });
        // Do NOT update reticle.x here
    }

    getTurretX() {
        return this.turretX;
    }

    getTurretY() {
        return this.turretY;
    }

    getTurretWidth() {
        return this.turret ? this.turret.width : 32;
    }

    getTurretSprite() {
        return this.turret;
    }

    getTurretBody() {
        return this.turretBody;
    }

    cleanup() {
        document.removeEventListener('keydown', this.handleKeydown);
        if (this.turret && this.turret.parent) {
            this.turret.parent.removeChild(this.turret);
        }
        if (this.turretBody) {
            Matter.World.remove(this.game.physicsManager.world, this.turretBody);
        }
        if (this.reticle && this.reticle.parent) {
            this.reticle.parent.removeChild(this.reticle);
        }
        if (this.game.app.view) {
            this.game.app.view.style.cursor = '';
        }
    }
}

window.LaserDefenderTurretManager = LaserDefenderTurretManager;