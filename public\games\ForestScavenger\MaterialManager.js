class ForestScavengerMaterialManager {
  constructor(game) {
    this.game = game;
    this.materials = [];
    this.sprites = [];
    this.requirements = { wood: 1, metal: 1, glass: 0, stone: 0, coin: 0 };
    this.collected = { wood: 0, metal: 0, glass: 0, stone: 0, coin: 0 };
    this.activePulseEffects = new Map(); // Track which materials have active pulse effects
  }

  init() {
    this.materials = [];
    this.sprites = [];
    this.activePulseEffects.clear(); // Clear pulse tracking
    
    // Get level configuration
    const levelConfig = this.game.levelConfigs[this.game.level] || this.game.levelConfigs[1];
    this.requirements = { 
      wood: levelConfig.wood, 
      metal: levelConfig.metal, 
      glass: levelConfig.glass, 
      stone: levelConfig.stone, 
      coin: levelConfig.coin 
    };
    this.collected = { wood: 0, metal: 0, glass: 0, stone: 0, coin: 0 };
    
    this.initMaterials();
  }

  initMaterials() {
    let occupied = new Set();
    occupied.add('8,8'); // House position
    this.game.goblinManager.goblins.forEach(g => occupied.add(`${g.x},${g.y}`));
    
    // Add safe zone positions to occupied
    this.game.safeZoneManager.safeZones.forEach(zone => {
      occupied.add(`${zone.x},${zone.y}`);
    });
    
    let types = Object.keys(this.requirements).filter(k => this.requirements[k] > 0);
    types.forEach(type => {
      for (let i = 0; i < this.requirements[type]; i++) {
        let placed = false;
        while (!placed) {
          let x = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
          let y = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
          let key = `${x},${y}`;
          
          if (this.game.gridManager.isWalkable(x, y) && 
              !occupied.has(key) && 
              !this.game.safeZoneManager.isInSafeZone(x, y)) {
            this.materials.push({ x, y, type });
            occupied.add(key);
            placed = true;
          }
        }
      }
    });
  }

  drawMaterials() {
    // Store which materials had active pulse effects before redrawing
    const materialsWithPulses = new Map();
    this.materials.forEach((material, index) => {
      const sprite = this.sprites[index];
      if (sprite && this.activePulseEffects.has(sprite)) {
        materialsWithPulses.set(`${material.x},${material.y},${material.type}`, {
          type: material.type,
          pulseData: this.activePulseEffects.get(sprite)
        });
      }
    });
    
    // Remove old sprites
    this.sprites.forEach(sprite => {
      if (sprite && sprite.parent) {
        sprite.parent.removeChild(sprite);
      }
    });
    this.sprites = [];
    this.activePulseEffects.clear(); // Clear pulse tracking
    
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    
    // Draw new sprites
    this.materials.forEach(material => {
      let matSprite = new PIXI.Sprite(this.game.images.materials[material.type]);
      matSprite.x = material.x * scaledTileSize + scaledTileSize / 2 + gridOffsetX;
      matSprite.y = material.y * scaledTileSize + this.game.hudHeight + scaledTileSize / 2 + gridOffsetY;
      matSprite.width = scaledTileSize;
      matSprite.height = scaledTileSize;
      matSprite.anchor.set(0.5); // Center the sprite for proper pulse effect
      this.game.app.stage.addChild(matSprite);
      this.sprites.push(matSprite);
      
      // Check if this material had an active pulse effect and restore it
      const materialKey = `${material.x},${material.y},${material.type}`;
      if (materialsWithPulses.has(materialKey)) {
        const pulseData = materialsWithPulses.get(materialKey);
        this.addMaterialEffects(matSprite, material.type, pulseData);
      } else {
        // Add new pulse effects to materials
        this.addMaterialEffects(matSprite, material.type);
      }
    });
  }

  addMaterialEffects(sprite, type, existingPulseData = null) {
    if (window.VisualEffects) {
      if (window.VisualEffects.createPulseEffect) {
        // Use stone's pulse rate (0.6) for all materials to ensure consistent speed
        const basePulseRate = 0.6;
        // Use consistent intensity (0.15) for all materials and coins
        const baseIntensity = 0.15;
        
        let startTime;
        
        // If we have existing pulse data, use it to maintain animation continuity
        if (existingPulseData) {
          // Restore the pulse effect with the same timing
          startTime = existingPulseData.startTime;
          window.VisualEffects.createPulseEffect(sprite, {
            pulseRate: basePulseRate,
            intensity: baseIntensity,
            continuous: true,
            startTime: startTime // Use original start time to maintain phase
          });
        } else {
          // Create new pulse effect with random phase offset
          const randomPhaseOffset = Math.random(); // Random value between 0 and 1
          const phaseOffsetMs = randomPhaseOffset * (1000 / basePulseRate); // Convert phase offset to milliseconds
          startTime = Date.now() - phaseOffsetMs;
          
          window.VisualEffects.createPulseEffect(sprite, {
            pulseRate: basePulseRate,
            intensity: baseIntensity,
            continuous: true,
            startTime: startTime
          });
        }
        
        // Track this sprite as having an active pulse effect
        this.activePulseEffects.set(sprite, {
          type: type,
          startTime: startTime
        });
      }
    }
  }

  checkMaterialCollection() {
    for (let i = 0; i < this.materials.length; i++) {
      let material = this.materials[i];
      if (material.x === this.game.playerManager.player.x && material.y === this.game.playerManager.player.y) {
        // Play collect sound
        if (window.GameSounds) {
          window.GameSounds.playCollect();
        }
        
        this.collected[material.type]++;
        this.materials.splice(i, 1);
        
        // Update HUD material count using the new CollectList component
        if (this.game.materialListHUD) {
          this.game.materialListHUD.updateCollectibleCount(material.type, this.collected[material.type], this.requirements[material.type]);
        }
        
        // Enhanced visual effects for material collection
        const scale = this.game.scale || 1;
        const scaledTileSize = this.game.TILE_SIZE * scale;
        const gridOffsetX = this.game.gridOffsetX || 0;
        const gridOffsetY = this.game.gridOffsetY || 0;
        const centerX = this.game.playerManager.player.x * scaledTileSize + scaledTileSize/2 + gridOffsetX;
        const centerY = this.game.playerManager.player.y * scaledTileSize + this.game.hudHeight + scaledTileSize/2 + gridOffsetY;
        
        if (window.VisualEffects) {
          const materialColors = {
            wood: 0x8B4513,   
            metal: 0xC0C0C0,  
            glass: 0x87CEEB,  
            stone: 0x696969,  
            coin: 0xFFD700     
          };
          const color = materialColors[material.type] || 0xFFFFFF;
          
          for (let j = 0; j < 6; j++) {
            setTimeout(() => {
              const angle = (Math.PI * 2 * j) / 6;
              const sparkleX = centerX + Math.cos(angle) * 20;
              const sparkleY = centerY + Math.sin(angle) * 20;
              window.VisualEffects.createSparkle(sparkleX, sparkleY, this.game.app.stage, color);
            }, j * 50);
          }
          
          if (material.type === 'coin') {
            window.VisualEffects.createExplosion(centerX, centerY, 0xFFD700, 12, this.game.app.stage);
          }
          
          window.VisualEffects.createRipple(centerX, centerY, this.game.app.stage, color);
          window.VisualEffects.createButtonClick(centerX, centerY, this.game.app.stage);
        }
        
        // Redraw materials to remove the collected one while preserving pulse effects on others
        this.drawMaterials();
        
        this.game.updateHUD();
        break;
      }
    }
  }

  checkWinCondition() {
    return Object.keys(this.requirements).every(type => this.collected[type] >= this.requirements[type]);
  }
}

window.ForestScavengerMaterialManager = ForestScavengerMaterialManager; 