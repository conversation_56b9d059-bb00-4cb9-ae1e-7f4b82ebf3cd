class ForestScavengerPlayerManager {
  constructor(game) {
    this.game = game;
    this.player = { x: 8, y: 8, dir: 'down' };
    this.sprite = null;
  }

  init() {
    // Place player far from goblins
    const farthestPosition = this.game.gridManager.getFarthestPositionFromGoblins();
    this.player = { x: farthestPosition.x, y: farthestPosition.y, dir: 'down' };
    this.sprite = null;
  }

  drawPlayer() {
    // Remove only the player sprite, not all stage children
    if (this.sprite && this.sprite.parent) {
      this.sprite.parent.removeChild(this.sprite);
    }
    
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const playerSize = 10 * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    
    this.sprite = new PIXI.Sprite(this.game.images.player[this.player.dir]);
    this.sprite.x = this.player.x * scaledTileSize + (scaledTileSize - playerSize) / 2 + gridOffsetX;
    this.sprite.y = this.player.y * scaledTileSize + this.game.hudHeight + (scaledTileSize - playerSize) / 2 + gridOffsetY;
    this.sprite.width = playerSize;
    this.sprite.height = playerSize;
    
    // Set transparency when player is in safe zone (50% transparent)
    if (this.game.isInSafeZone) {
      this.sprite.alpha = 0.5;
    } else {
      this.sprite.alpha = 1.0;
    }
    
    this.game.app.stage.addChild(this.sprite);
  }

  movePlayer(dx, dy, dir) {
    this.player.dir = dir;
    let nx = this.player.x + dx;
    let ny = this.player.y + dy;
    
    // Check if new position is valid
    if (this.game.gridManager.isWalkable(nx, ny) && 
        !this.game.gridManager.isOccupied(nx, ny, true, true, true)) {
      
      // Check safe zone transitions
      const wasInSafeZone = this.game.isInSafeZone;
      const isInSafeZone = this.game.safeZoneManager.isInSafeZone(nx, ny);
      
      if (!wasInSafeZone && isInSafeZone) {
        // Player is entering safe zone
        this.game.safeZoneManager.onPlayerEnterSafeZone();
      } else if (wasInSafeZone && !isInSafeZone) {
        // Player is leaving safe zone
        this.game.safeZoneManager.onPlayerExitSafeZone();
      }
      
      this.player.x = nx;
      this.player.y = ny;
      this.game.isInSafeZone = isInSafeZone;
      
      // Check for material collection
      this.game.materialManager.checkMaterialCollection();
      
      // Check for goblin encounter
      this.game.goblinManager.checkGoblinEncounter();
      
      // Check win condition
      this.game.checkWin();
      
      // Use optimized update to preserve pulse effects
      this.game.updateMovingEntities();
    }
  }

  getPosition() {
    return { x: this.player.x, y: this.player.y };
  }

  isInSafeZone() {
    return this.game.safeZoneManager.isInSafeZone(this.player.x, this.player.y);
  }
}

window.ForestScavengerPlayerManager = ForestScavengerPlayerManager; 