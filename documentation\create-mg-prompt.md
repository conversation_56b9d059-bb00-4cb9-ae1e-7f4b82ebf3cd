# Card Match Game Overview

Any new game created MUST be a completely new game not already created and has a different gameplay loop or mechanic.
NEVER create bubble pop rush or derivative games, come up with completely different games each time asked to make a new mini game that we do not have. we already have card match, bubble pop and others.
 if you need me to create png files for assets used in the game, keep it to a minimum of 2-3 new assets, or no new assets. you are also free to use any asset images in the images folder that are in images/ui folder


** create new folder for the mini game and keep things clean and maintainable  create individual js files for parts of game, learn from other games that have seperate js files
**Gameplay:**
- Flip cards to find matching pairs. Each level presents a shuffled grid of cards with hidden images.
- Players can flip two cards at a time. If they match, the pair is removed; if not, they flip back.
- The challenge increases with each level: more image variety, less time, and more shuffle randomness.

**Progression:**
- 10+ levels, each harder than the last (more image types, less time).
- Players advance by matching all pairs before time runs out.

**Win Condition:**
- Match all pairs before the timer ends.

**Lose Condition:**
- Timer reaches zero before all pairs are matched.

**FX & Core:**
- Uses `BaseGame` for timer, HUD, and countdown.
- Uses `VisualEffects` for sparkles, flips, and feedback.
- Uses Matterjs for collision, physics and more as needed for the game
**Inspirations**
- Atari games: Galaga, Pac man, physics based games 
- NES games
- Warioware mini games
- Mario Party mini games
- 2d and 3d games that are simple arcade style
- Shooting games, driving games, fly through rings, jump platformer, puzzle swim,whack a mole, escape, chase, sliding, throwing, picking up and moving, digging, and other game ideas welcome, wackamole

** Player controls ideas **
- Easy to play
- Mouse drag and drop
- Mouse click to select and move 
- Mouse pointer location
- Space bar, WASD controls

---

# Mini Game System Integration Guide

## System Architecture

The mini game system is built with a modular architecture that separates concerns and provides reusable components:

### Core Components
- **BaseGame** (`public/games/core/BaseGame.js`): Provides common game functionality including timer, HUD, countdown, and game lifecycle management
- **GameManager** (`public/games/core/GameManager.js`): Handles game initialization, modal management, and game switching
- **VisualEffects** (`public/games/fx/VisualEffects.js`): Provides consistent visual effects across all games
- **GameUtils** (`public/games/utils/GameUtils.js`): Contains utility functions for DOM manipulation and common game operations

### File Structure
```
public/games/
├── core/                     # Core system files
│   ├── BaseGame.js          # Base game class with common functionality
│   ├── GameManager.js       # Game initialization and management
│   └── gameSounds.js        # Sound effects for games
├── fx/                       # Visual effects system
│   ├── VisualEffects.js     # Main effects coordinator
│   └── effects/             # Individual effect implementations
│       ├── ExplosionEffect.js
│       ├── SparkleEffect.js
│       ├── RippleEffect.js
│       └── ... (other effects)
├── utils/                    # Utility functions
│   ├── GameUtils.js         # DOM utilities and common functions
│   └── drawing/             # Drawing-specific utilities
├── games/                    # Individual game implementations
│   ├── CardMatch.js         # Card matching game
│   ├── TreasureFrenzy.js    # Treasure clicking game
│   ├── BouncyClick.js       # Ball clicking game
│   └── ... (other games)
└── games.js                 # Main entry point and game registry
```

## Integration Points

### 1. Game Data Registration
**File:** `public/data/games.js`
Add your game to the `mini_games` array:
```javascript
{
    "id": [unique_id],
    "type": "land|water|space|variety",
    "name": "Your Game Name",
    "image": "path/to/image.png", // Optional
    "instructions": "Game instructions for players",
    "description": "Brief description of the game"
}
```

### 2. Active Games List
**File:** `public/games/games.js`
Add your game ID to the `active_mini_games` array:
```javascript
var active_mini_games = [1, 2, 5, 18, 19, [your_game_id]];
```

### 3. Game Class Registration
**File:** `public/games/games.js` (in the `displayGameModal` function)
Add your game class to the switch statement:
```javascript
switch (game.name) {
    case "Your Game Name":
        GameManager.instance.currentGame = new YourGameClass();
        break;
    // ... other games
}
```

### 4. HTML Integration
**File:** `public/index.html`
Add your game script to the HTML:
```html
<!-- Individual games -->
<script src="games/games/YourGame.js"></script>
```

### 5. Modal Structure
**File:** `public/ux/modals/game-modals.js`
The game modal is automatically created with the required structure:
- Start screen with title and instructions
- Game area with HUD and canvas
- Game over screen with results

## Creating a New Mini Game

### Step 1: Create Game Class
Create a new file in `public/games/games/YourGame.js`:
```javascript
class YourGame extends BaseGame {
    constructor() {
        super();
        // Initialize game-specific properties
        this.gameObjects = [];
        this.gameState = 'waiting';
    }

    onCountdownComplete() {
        // Called when 3-second countdown finishes
        this.startGameLogic();
    }

    startGameLogic() {
        // Your main game loop and logic
        this.gameInterval = setInterval(() => {
            this.updateGame();
        }, 16); // ~60 FPS
    }

    updateGame() {
        // Update game state, check win/lose conditions
        this.time++;
        this.updateHUD();
        
        // Check win condition
        if (this.checkWinCondition()) {
            this.handleWin();
        }
        
        // Check lose condition
        if (this.checkLoseCondition()) {
            this.handleLose();
        }
    }

    checkWinCondition() {
        // Return true if player wins
        return false;
    }

    checkLoseCondition() {
        // Return true if player loses
        return this.time >= 60; // Example: 60 second time limit
    }

    handleWin() {
        this.score += 100;
        this.level++;
        this.nextLevelOrEnd();
    }

    handleLose() {
        this.endGame('Game Over!');
    }
}
```

### Step 2: Register Your Game
Follow the integration points listed above to register your game in the system.

### Step 3: Add Visual Effects
Use the VisualEffects system for consistent feedback:
```javascript
// Explosion effect
window.VisualEffects.createExplosion(x, y, color, particleCount, this.app.stage);

// Sparkle effect
window.VisualEffects.createSparkle(x, y, this.app.stage, color);

// Score popup
window.VisualEffects.createScorePopup(x, y, score, this.app.stage, color);

// Level up effect
window.VisualEffects.createLevelUpEffect(this.app.stage, this.level, callback);
```

### Step 4: Add Sound Effects
Use the GameSounds system for audio feedback:
```javascript
if (window.GameSounds) {
    window.GameSounds.playBubblePop(); // For clicks
    window.GameSounds.playGameOver();  // For game over
    window.GameSounds.playCountdown(); // For countdown
}
```

## Required Files to Update

When creating a new mini game, you must update these files:

1. **`public/data/games.js`** - Add game data entry
2. **`public/games/games.js`** - Add to active games list and game class registration
3. **`public/index.html`** - Add script tag for your game file
4. **`public/games/games/YourGame.js`** - Create your game implementation

## Optional Enhancements

### Custom HUD Elements
Extend the HUD with game-specific elements:
```javascript
// In your game constructor
this.customHUD = new PIXI.Text('Custom Info', {
    fontFamily: 'Press Start 2P',
    fontSize: 16,
    fill: 0xFFFFFF
});
this.customHUD.x = 10;
this.customHUD.y = 50;
this.app.stage.addChild(this.customHUD);
```

### Powerup System
Implement powerups that require points to activate:
```javascript
this.powerupProgress = 0;
this.powerupTarget = 10; // Points needed to activate powerup

// In your scoring logic
this.powerupProgress++;
if (this.powerupProgress >= this.powerupTarget) {
    this.activatePowerup();
}
```

### Physics Integration
For games requiring physics, use Matter.js:
```javascript
// Initialize physics
this.physicsManager = new PhysicsManager();

// Create physics bodies
this.physicsManager.createBallBody(x, y, radius, sprite);

// Update physics in game loop
this.physicsManager.update();
```

---

# Mini Game Creation Prompt Template

> Create a new mini game for our system. Follow these rules:
> - There must be a clear challenge that increases from level to level.
> - Include at least 10 levels or infinite difficulty scaling.
> - Define a win condition (how the player wins a level or the game).
> - Define a lose condition (how the player can fail or lose).
> - Define powerups to add spice to a game - these require a certain amt of points gathered before you can use it
> - Use the `BaseGame` class for core logic (timer, HUD, countdown, etc).
> - Use `VisualEffects` (fx) for feedback and effects.
> - Keep the game simple, fun, and easy to understand.
> - Example: Card Match (see above). ONLY use reusable HUD elements:
 Timer, Level, Score
 Optional: Heart Meter (uses heart icons to display lives), Powerup meter (displays how much points gathered to be able to use a powerup) -  we use up this bar when we use the power up and then it refills at some prescribed rate. Health meter (just like powerup meter, a rectangle of a different color that is filled green and reveals a red bar behind it )

**Prompt Example:**
> Design a mini game where players [describe core mechanic]. Each level, [describe how challenge increases]. The player wins by [win condition], and loses if [lose condition]. Use our mini game system's core and fx features. Create the md for it with a unique name following our example names. List any graphics we may need or 

Common problems:
ERROR: "Game not implemented yet" error: 
HOW TO FIX:
1. initializeGame(game)  in GameManager.js must also be updated.
2. all our mini games have an async startGame() method that calls initGame() and then startCountdown()
and at the end of the mini game's js file we need to use a 'window.ClassName = ClassName;' 
 
 