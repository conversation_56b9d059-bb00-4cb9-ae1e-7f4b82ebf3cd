function createStandardButton({
  text,
  icon,
  classes = '',
  dataAttr = '',
  dataValue = '',
  onClick,
  iconProps = {}
}) {
  const button = $('<button>').addClass(`btn-secondary m-1 ${classes}`);

  if (icon) {
    const img = $('<img>').attr('src', icon);
    Object.entries(iconProps).forEach(([key, value]) => img.attr(key, value));
    button.append(img);
  }

  if (text) button.append(document.createTextNode(` ${text}`));
  if (dataAttr && dataValue) button.attr(`data-${dataAttr}`, dataValue);
  if (onClick) button.on('click', onClick);

  return button;
} 
