 console.log('reloadPlayerActiveTeams: Before createTeamItem, is displayTeamViewModal defined?', typeof displayTeamViewModal);

async function storeData(schema, data) {
  // Make sure global variables are initialized
  if (typeof creaturesData === 'undefined') {
    window.creaturesData = {};
  }
  if (typeof vehiclesData === 'undefined') {
    window.vehiclesData = {};
  }

  if (schema === 'creature') {
    window.creaturesData = data;
  }
  if (schema === 'vehicles') {
    window.vehiclesData = data;
  }
}

async function fetchTable(user, collection, schema) {
  var promises = [];
  var result = [];
  var finalResult = [];
  var currentPage = 1;
  var maxPages = 3; // increase as needed during testings
  while (currentPage <= maxPages) {
    const url = wax_endpoint + 'atomicassets/v1/assets?collection_name=' + collection + '&schema_name=' + schema + '&owner=' + user + '&page=' + currentPage + '&limit=100&order=desc&sort=asset_id';
    promises.push(await axios.get(url));
    currentPage++;
  }
  const data = await Promise.all(promises);
  data.forEach(({
    data
  }) => {
    for (n in data) {
      if (data[n].length) {
        result = [...result, data[n]];
      }
    }
    finalResult = [].concat.apply([], result);
  });
  storeData(schema, finalResult); // store the data for vehicles and creatures for img calls
  return finalResult;
}

// Core fetching of assets for inventories
async function fetchAndFilterItems(selectedSchema, filterCondition = null) {
    let items = [];
    
    // Special case: when "items" schema is selected, also fetch "foods" items
    if (selectedSchema === 'items') {
        const itemsData = await fetchTable(wax.userAccount, 'cutecrushies', 'items');
        const foodsData = await fetchTable(wax.userAccount, 'cutecrushies', 'foods');
        items = [...itemsData, ...foodsData];
    } else {
        // Fetch items from the table for other schemas
        items = await fetchTable(wax.userAccount, 'cutecrushies', selectedSchema);
    }
    
    // Filter items based on the provided condition (if any)
    return filterCondition ? items.filter(filterCondition) : items;
}

// Core map data fetching
async function getMapData() {
    console.log('[MAP DEBUG] getMapData called');
    allZones = [];
    try {
        const response = await axios.get(domain_url + '/players/zones');
        var data = response.data;
        for (var m in data) {
            allZones.push(data[m]);
        }
        console.log(`[MAP DEBUG] getMapData loaded ${allZones.length} zones`);
        return allZones;
    } catch (error) {
        console.error('[MAP ERROR] Error loading zone data:', error);
        return [];
    }
}

// Core data fetching functions
async function fetchPlayerData(table) {
  return await fetchTable(wax.userAccount, 'cutecrushies', table);
}

async function getTeams() {
  try {
    const response = await axios.get(`${domain_url}/teams/`);
    var result = response.data;
    return result;
  } catch (error) {
    console.log(error);
  }
}

async function getPlayerTeams() {
  allTeams = await getTeams();
  myTeams = allTeams.filter(team => team.owner_id === wax.userAccount);
  allTeamsCreatures = myTeams.map(team => team.data.creatures);
  allTeamsVehicles = myTeams.map(team => team.data.creatures);
  console.log(myTeams)
  return myTeams;
}

async function getPlayerCoreData() {
  return {
    teams: await getPlayerTeams(),
    adventures: await getAdventuresByPlayer(),
    balances: await displayBalances(url_player, url_dust)
  };
}

async function reloadPlayerTeamData(status) {
  const coreData = await getPlayerCoreData();
  myTeams = coreData.teams;
  adventures = coreData.adventures;
  housesData = await fetchPlayerData('houses');
  const assets = await reloadPlayerActiveTeams(status);
  return assets;
}


async function getAdventures() {
  console.log('[MAP DEBUG] getAdventures called');
  try {
    const {
      data
    } = await axios.get(`${domain_url}/adventures/`);
    allAdventures = data;
    console.log(`[MAP DEBUG] getAdventures loaded ${allAdventures.length} adventures`);
    return data;
  } catch (error) {
    console.error('[MAP ERROR] Error loading adventure data:', error);
    return [];
  }
}

// adventures data
async function getAdventuresByPlayer() {
  try {
    const {
      data
    } = await axios.get(`${domain_url}/adventures/owner/${wax.userAccount}`);
    playerAdventures = data;
    return data;
  } catch (error) {
    console.error(error);
  }
}

function reloadPlayerAdventures(status){
  const inventory = $("#general-inventory");
  showInventoryLoadingSpinner(inventory);
  getAdventuresByPlayer().then(adventures => {
    inventory.empty(); // Clear the spinner before adding content
    adventures.filter(adventure => adventure.status === status)
              .forEach(adventure => {
                const adventureItem = createAdventureItem(adventure);
                inventory.append(adventureItem);
              });
  });
}

async function reloadPlayerAdventureData(status) {
  const coreData = await getPlayerCoreData();
  myTeams = coreData.teams;
  adventures = coreData.adventures;
  const assets = await reloadPlayerAdventures(status);
  return assets;
}

async function getPlayerSettings(user){
  const url = `https://express-crushie.herokuapp.com/players/player_settings/${user}`;
  try {
    const response = await axios.get(url);
    playerData.settings = response.data[0].data;
    return response.data[0].data;
  } catch (error) {
    console.error(error);
  }
}

// Individual data reloaders
const reloadPlayerData = {
  houses: async () => { housesData = await fetchPlayerData('houses'); },
  vehicles: async () => { vehiclesData = await fetchPlayerData('vehicles'); },
  creatures: async () => { creaturesData = await fetchPlayerData('creature'); },
  all: async () => {
    await Promise.all([
      reloadPlayerData.vehicles(),
      reloadPlayerData.creatures()
    ]);
  }
};

async function reloadPlayerActiveTeams(status){
  const teamsDiv = $('#general-inventory');
  showInventoryLoadingSpinner(teamsDiv);
  getPlayerTeams().then(teams => {
    teamsDiv.empty(); // Clear the spinner before adding content
    teams.filter(team => team.status === status)
         .forEach(team => {
           const teamItem = createTeamItem(team);
           teamsDiv.append(teamItem);
         });
  });
}
