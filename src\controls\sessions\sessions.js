const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addSessionToken = async (req, res) => {
  const { token, user } = req.body;

  // Input validation
  if (!token || !user) {
    return res.status(400).json({ error: 'Missing token or user' });
  }

  // Validate WAX account format (should contain a dot)
  if (!user.includes('.')) {
    return res.status(400).json({ error: 'Invalid WAX account format' });
  }

  // Ensure we're hashing the raw token, not a Bearer token
  const tokenToHash = token.startsWith('Bearer ') ? token.split(' ')[1] : token;

  try {
    const hashedToken = await bcrypt.hash(tokenToHash, 10); // Increased salt rounds for better security

    // Check if player exists, create if not (for new WAX accounts)
    console.log(`Checking if player exists for WAX account: ${user}`);
    const playerExists = await pool.query(queries.exist.checkWaxIDExists, [user]);
    console.log(`Player existence check result: ${playerExists.rows.length} rows found`);
    if (playerExists.rows.length === 0) {
      console.log(`Player does not exist, creating new player for: ${user}`);
      try {
        const now = new Date();
        const defaultGXP = 0;
        const defaultNectar = 3;
        const defaultCredits = 3;

        console.log(`Attempting to create player with params: [${user}, ${now}, ${now}, ${defaultGXP}, ${defaultNectar}, ${defaultCredits}]`);
        const playerResult = await pool.query(queries.add.addPlayer, [user, now, now, defaultGXP, defaultNectar, defaultCredits]);
        console.log(`Player creation result:`, playerResult);
        console.log(`New player created for WAX account: ${user}`);

        // Create default player settings
        const defaultSettings = {
          nectar_max: 3,
          credits_max: 3,
          adventure_gxp_bonus: 0,
          nap_rate_bonus: 0
        };
        console.log(`Attempting to create player settings with params: [${user}, ${JSON.stringify(defaultSettings)}]`);
        const settingsResult = await pool.query(queries.add.addPlayerSettings, [user, JSON.stringify(defaultSettings)]);
        console.log(`Player settings creation result:`, settingsResult);
        console.log(`Default settings created for player: ${user}`);

        // Verify player was created successfully
        const verifyPlayer = await pool.query(queries.exist.checkWaxIDExists, [user]);
        console.log(`Player verification after creation: ${verifyPlayer.rows.length} rows found`);
        if (verifyPlayer.rows.length > 0) {
          console.log(`✅ Player successfully created and verified for: ${user}`);
        } else {
          console.log(`❌ Player creation verification failed for: ${user}`);
        }
      } catch (playerErr) {
        console.error('Error creating new player for user:', user);
        console.error('Player creation error details:', playerErr);
        console.error('Error message:', playerErr.message);
        console.error('Error code:', playerErr.code);
        console.error('Error detail:', playerErr.detail);
        // Continue with session creation even if player creation fails
      }
    }

    // Check if user session already exists
    const existingSession = await pool.query(queries.getby.getUserSessionById, [user]);

    if (existingSession.rows.length === 0) {
      // No existing session - create new one
      try {
        await pool.query(queries.add.addUserSession, [user, hashedToken]);
        console.log(`New session created for user: ${user}`);
        return res.status(201).json({ message: 'New session created', user });
      } catch (err) {
        console.error('Error creating new session:', err);
        return res.status(500).json({ error: 'Error adding new user session' });
      }
    }

    // Session exists - check if expired
    const userSession = existingSession.rows[0];
    const now = new Date();

    if (userSession.expires_at < now) {
      // Session expired - update with new token
      try {
        await pool.query(queries.add.addUserSession, [user, hashedToken]);
        console.log(`Expired session updated for user: ${user}`);
        return res.status(201).json({ message: 'Session updated with new token', user });
      } catch (err) {
        console.error('Error updating expired session:', err);
        return res.status(500).json({ error: 'Error updating expired user session' });
      }
    }

    // Session exists and not expired - validate token
    try {
      const isMatch = await bcrypt.compare(tokenToHash, userSession.token);

      if (isMatch) {
        console.log(`Valid token for user: ${user}`);
        return res.status(200).json({ message: 'Token valid', user });
      } else {
        // Token doesn't match - update with new token (user might have re-logged in)
        await pool.query(queries.add.addUserSession, [user, hashedToken]);
        console.log(`Token updated for user: ${user}`);
        return res.status(201).json({ message: 'Session updated with new token', user });
      }
    } catch (bcryptError) {
      console.error('Error comparing tokens:', bcryptError);
      return res.status(500).json({ error: 'Error validating token' });
    }

  } catch (err) {
    console.error('Error in addSessionToken:', err);
    return res.status(500).json({ error: 'Internal server error during session creation' });
  }
};

const getUserSessionById = async (req, res) => {
  try {
    const authorizationHeader = req.headers.authorization;

    // Check if the header is in Bearer format
    let token = null;
    if (authorizationHeader && authorizationHeader.startsWith('Bearer ')) {
      token = authorizationHeader.split(' ')[1];
    } else {
      // If not in Bearer format, use the whole header as token (for backward compatibility)
      token = authorizationHeader;
    }

    const wax_id = req.params.user;

    pool.query(queries.getby.getUserSessionById, [wax_id], async (error, results) => {
      if (error) {
        return res.status(500).send('Internal Server Error');
      }

      // Check if we have results
      if (!results.rows || results.rows.length === 0) {
        return res.status(401).json({ error: 'No active session found' });
      }

      const storedToken = results.rows[0].token;

      // Check if token is expired
      const expiresAt = results.rows[0].expires_at;
      const now = new Date();

      if (expiresAt < now) {
        return res.status(401).json({ error: 'Session expired' });
      }

      try {
        // Try comparing with the Bearer token first
        let isMatch = false;
        if (token) {
          try {
            isMatch = await bcrypt.compare(token, storedToken);
          } catch (bcryptError) {
          }
        }

        if (isMatch) {
          // Return the session data in the format expected by the client
          return res.status(200).json(results.rows);
        } else {
          return res.status(401).send('Token invalid');
        }
      } catch (bcryptError) {
        return res.status(500).send('Error validating token');
      }
    });
  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
};

const getUserSessionByToken = (req, res) => {
  var token = req.params.id;
  pool.query(queries.getby.getUserSessionByToken, [token], (error, results) => {
    if (error) {
      res.status(500).send('Internal Server Error');
    } else {
      res.status(200).json(results.rows);
    }
  });
};

const removeUserSession = (req, res) => {
  var wax_id = req.params.wax_id;
  pool.query(queries.remove.removeUserSession, [wax_id], (error, results) => {
    res.status(200).send("Session removed successfully for user: " + wax_id);
  });
} 

module.exports = {
  addSessionToken,
  getUserSessionById,
  getUserSessionByToken,
  removeUserSession
};