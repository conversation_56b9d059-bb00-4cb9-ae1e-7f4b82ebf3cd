async function displayVehicleMovingIcon(owner, id, adventure) {
  if (!adventure || !adventure.team_id) {
    console.error('ERROR: displayVehicleMovingIcon - adventure or team_id is undefined:', adventure);
    return;
  }

  if(owner==='player'){
    var landVehicleTag = 'land-vehicle';
    var waterVehicleTag = 'water-vehicle';
    var spaceVehicleTag = 'space-vehicle';
  }else if(owner==='npc'){
    var landVehicleTag = 'land-npc';
    var waterVehicleTag = 'water-npc';
    var spaceVehicleTag = 'space-npc';
  }

  const totalSquares = 255 - adventure.mapgrid_256;
  const progress = Math.round((adventure.current_steps / adventure.init_steps) * 100);
  const distance = Math.round((totalSquares * progress) / 100);

  const squareDiv = document.getElementById(id);
  if (!squareDiv) {
    console.error('ERROR: displayVehicleMovingIcon - squareDiv not found for id:', id);
    return;
  }

  squareDiv.querySelector('.vehicle-icon')?.remove();
  const vehicle_icon = document.createElement('div');

  let icon_type;
  try {
    if(owner === 'npc') {
      icon_type = getTerrainTypeByTeamId(adventure.team_id, allTeams);
    } else {
      if (!myTeams || !Array.isArray(myTeams) || myTeams.length === 0) {
        console.error('ERROR: myTeams is invalid in displayVehicleMovingIcon');
        icon_type = 'land';
      } else {
        icon_type = await getVehicleInfoByTeamId('terrain', adventure.team_id, myTeams, vehiclesData);
      }
    }

    if (!icon_type) {
      console.warn('WARN: icon_type is null or undefined, defaulting to "land"');
      icon_type = 'land';
    }
  } catch (error) {
    console.error('ERROR: Failed to get terrain type:', error);
    icon_type = 'land';
  }

  if (icon_type === 'land') {
    vehicle_icon.classList.add('vehicle-icon', landVehicleTag);
  }
  if (icon_type === 'space') {
    vehicle_icon.classList.add('vehicle-icon', spaceVehicleTag);
  }
  if (icon_type === 'water') {
    vehicle_icon.classList.add('vehicle-icon', waterVehicleTag);
  }

  vehicle_icon.title = `${progress}% - ${adventure.owner_id} Team ${adventure.team_id} Moving to #${id + 1}`;
  document.getElementById(id).append(vehicle_icon);
}

async function displayTeamsOnMap(displayTeamIcon, displayVehicleMovingIcon, displayTreasureIcon, displayTeamReadyIcon) {
  let allAdventures = await getAdventures();
  if (!allAdventures || allAdventures.length === 0) {
    allAdventures = await getAdventures();
  }

  let playerAdventures = await getAdventuresByPlayer();
  if (!playerAdventures) {
    playerAdventures = await getAdventuresByPlayer();
  }

  window.teamCounts = countAndCategorizeTeams(allAdventures, playerAdventures);

  const myTeamLocations = new Set();
  const npcTeamLocations = new Set();

  playerAdventures.filter(adventure => {
    return adventure.status === 'In Progress' &&
           adventure.mapgrid_4 === nav.world &&
           adventure.mapgrid_16 === nav.zone;
  }).forEach(adventure => {
    myTeamLocations.add(adventure.mapgrid_256);
    displayTeamIcon('player', adventure.mapgrid_256, adventure);
    displayVehicleMovingIcon('player', adventure.mapgrid_256, adventure);
  });

  allAdventures.filter(adventure => {
    return adventure.status === 'In Progress' &&
           adventure.owner_id !== wax.userAccount &&
           adventure.mapgrid_4 === nav.world &&
           adventure.mapgrid_16 === nav.zone;
  }).forEach(adventure => {
    npcTeamLocations.add(adventure.mapgrid_256);
    displayTeamIcon('npc', adventure.mapgrid_256, adventure);
    displayVehicleMovingIcon('npc', adventure.mapgrid_256, adventure);
  });

  let completeCount = 0;
  playerAdventures.forEach(adventure => {
    if (adventure.status === 'Complete' &&
        adventure.mapgrid_4 === nav.world &&
        adventure.mapgrid_16 === nav.zone) {
        displayTreasureIcon(adventure.mapgrid_256, adventure);
        completeCount++;
    }
  });

  if (completeCount > 0) {
    await displayTeamReadyIcon(nav, 0, completeCount + ' Team(s) Ready!');
  }
}