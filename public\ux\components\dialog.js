class DialogSystem {
    constructor() {
        this.dialogQueue = [];
        this.activeDialogs = [];
        this.container = null;
        this.typingEffect = null;
        this.init();
    } 
    init() { 
        this.container = document.createElement('div');
        this.container.className = 'dialog-container';
        this.container.id = 'dialog-system-container';
        document.body.appendChild(this.container); 
        this.loadCSS();
    } 
    loadCSS() {
        if (!document.querySelector('link[href*="dialog.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/css/dialog.css';
            document.head.appendChild(link);
        }
    }

    /**
     * Show a dialog with the specified options
     * @param {Object} options - Dialog configuration
     * @param {string|Array} options.text - Text to display (string or array of strings)
     * @param {string} options.characterName - Name of the character
     * @param {string} options.characterImage - URL of character image
     * @param {string} options.position - Position on screen ('center', 'top-left', 'top-right', 'bottom-left', 'bottom-right')
     * @param {string} options.entryDirection - Entry animation direction ('top', 'bottom', 'left', 'right')
     * @param {number} options.timeout - Auto-close timeout in seconds (default: 5)
     * @param {boolean} options.allowMultiple - Allow multiple dialogs side by side
     * @param {number} options.typingSpeed - Typing speed in milliseconds
     * @param {string} options.typingMode - 'letter' or 'word'
     * @param {Function} options.onComplete - Callback when dialog completes
     * @param {Function} options.onClose - Callback when dialog closes
     */
    show(options) {
        const dialog = {
            id: this.generateId(),
            text: Array.isArray(options.text) ? options.text : [options.text],
            characterName: options.characterName || 'Unknown',
            characterImage: options.characterImage || '',
            position: options.position || 'center',
            entryDirection: options.entryDirection || 'bottom',
            timeout: options.timeout || 5,
            allowMultiple: options.allowMultiple || false,
            typingSpeed: options.typingSpeed || 50,
            typingMode: options.typingMode || 'letter',
            onComplete: options.onComplete || null,
            onClose: options.onClose || null,
            currentTextIndex: 0,
            timeoutId: null,
            element: null,
            typingEffect: null
        };

        this.dialogQueue.push(dialog);
        this.processQueue();
    }
 
    processQueue() {
        if (this.dialogQueue.length === 0 || this.activeDialogs.length > 0) {
            return;
        }

        const dialog = this.dialogQueue.shift();
        this.createDialogElement(dialog);
        this.activeDialogs.push(dialog);
    }
 
    createDialogElement(dialog) {
        const dialogElement = document.createElement('div');
        dialogElement.className = `dialog-window dialog-${dialog.position}`;
        dialogElement.id = `dialog-${dialog.id}`;

        // Add entry animation class
        dialogElement.classList.add(`dialog-slide-from-${dialog.entryDirection}`);

        dialogElement.innerHTML = `
            <div class="dialog-header">
                <div class="dialog-character-section">
                    ${dialog.characterImage ? `<img src="${dialog.characterImage}" alt="${dialog.characterName}" class="dialog-character-image">` : ''}
                    <div class="dialog-character-name">${dialog.characterName}</div>
                </div>
                <div class="dialog-content">
                    <div class="dialog-text"></div>
                </div>
            </div>
            <div class="dialog-controls">
                <button class="btn-secondary">Next ></button>
                <button class="btn-secondary">Close</button>
            </div>
        `;

        // Position the dialog
        this.positionDialog(dialogElement, dialog);

        // Add to container
        this.container.appendChild(dialogElement);
        this.container.style.display = 'block';

        // Store element reference
        dialog.element = dialogElement;

        // Start animation
        requestAnimationFrame(() => {
            dialogElement.classList.remove(`dialog-slide-from-${dialog.entryDirection}`);
            dialogElement.classList.add('active');
        });

        // Start typing the first text segment
        this.startTyping(dialog);

        // Add event listeners
        this.addEventListeners(dialog);
    }
 
    positionDialog(element, dialog) {
        const positions = {
            'center': { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' },
            'top-left': { top: '20px', left: '20px' },
            'top-right': { top: '20px', right: '20px' },
            'bottom-left': { bottom: '20px', left: '20px' },
            'bottom-right': { bottom: '20px', right: '20px' }
        };

        const pos = positions[dialog.position] || positions.center;
        Object.assign(element.style, pos);
    }
 
    startTyping(dialog) {
        const textElement = dialog.element.querySelector('.dialog-text');
        const currentText = dialog.text[dialog.currentTextIndex];

        if (!currentText) {
            this.completeDialog(dialog);
            return;
        }

        // Create typing effect
        dialog.typingEffect = new TypingEffect(textElement, {
            speed: dialog.typingSpeed,
            typeBy: dialog.typingMode,
            onComplete: () => {
                this.onTypingComplete(dialog);
            }
        });

        textElement.classList.add('typing');
        dialog.typingEffect.type(currentText);

        // Start typing audio
        if (window.dialogAudioManager) {
            window.dialogAudioManager.startTypingAudio();
        }
    }
 
    onTypingComplete(dialog) {
        const textElement = dialog.element.querySelector('.dialog-text');
        textElement.classList.remove('typing');

        // Stop typing audio
        if (window.dialogAudioManager) {
            window.dialogAudioManager.stopTypingAudio();
        }

        // Start timeout for auto-close
        if (dialog.timeout > 0) {
            dialog.timeoutId = setTimeout(() => {
                this.nextTextSegment(dialog);
            }, dialog.timeout * 1000);
        }
    }
 
    nextTextSegment(dialog) {
        // Clear timeout
        if (dialog.timeoutId) {
            clearTimeout(dialog.timeoutId);
            dialog.timeoutId = null;
        }

        dialog.currentTextIndex++;

        if (dialog.currentTextIndex < dialog.text.length) {
            // More text segments to show
            this.startTyping(dialog);
        } else {
            // All text segments shown, complete dialog
            this.completeDialog(dialog);
        }
    }
 
    completeDialog(dialog) {
        if (dialog.onComplete) {
            dialog.onComplete();
        }

        // Auto-close after a short delay
        setTimeout(() => {
            this.closeDialog(dialog);
        }, 1000);
    }
 
    closeDialog(dialog) {
        if (dialog.timeoutId) {
            clearTimeout(dialog.timeoutId);
        }

        if (dialog.typingEffect) {
            dialog.typingEffect.stop();
        }

        // Stop typing audio when closing dialog
        if (window.dialogAudioManager) {
            window.dialogAudioManager.stopTypingAudio();
        }

        // Remove from active dialogs
        const index = this.activeDialogs.findIndex(d => d.id === dialog.id);
        if (index > -1) {
            this.activeDialogs.splice(index, 1);
        }

        // Animate out
        if (dialog.element) {
            dialog.element.classList.remove('active');
            dialog.element.classList.add(`dialog-slide-from-${dialog.entryDirection}`);

            setTimeout(() => {
                if (dialog.element && dialog.element.parentNode) {
                    dialog.element.parentNode.removeChild(dialog.element);
                }
                
                // Hide container if no more active dialogs
                if (this.activeDialogs.length === 0 && this.dialogQueue.length === 0) {
                    this.container.style.display = 'none';
                }
            }, 300);
        }

        // Call onClose callback
        if (dialog.onClose) {
            dialog.onClose();
        }

        // Process next dialog in queue
        this.processQueue();
    }
 
    addEventListeners(dialog) {
        // Select buttons within the dialog-controls container
        const buttons = dialog.element.querySelectorAll('.dialog-controls .btn-secondary');
        const nextBtn = buttons[0]; // First button is "Next"
        const closeBtn = buttons[1]; // Second button is "Close"

        nextBtn.addEventListener('click', () => {
            if (dialog.typingEffect && dialog.typingEffect.isTyping) {
                // Skip typing and show full text
                dialog.typingEffect.skip();
                // Stop typing audio when skipping
                if (window.dialogAudioManager) {
                    window.dialogAudioManager.stopTypingAudio();
                }
            } else {
                // Move to next segment
                this.nextTextSegment(dialog);
            }
        });

        closeBtn.addEventListener('click', () => {
            this.closeDialog(dialog);
        });
    } 
    closeAll() {
        const dialogsToClose = [...this.activeDialogs];
        dialogsToClose.forEach(dialog => this.closeDialog(dialog));
        this.dialogQueue = [];
    } 
    generateId() {
        return 'dialog_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
 
    // test() {
    //     this.show({
    //         text: [
    //             "Well, it's about time we go on an adventure, don't you think?",
    //             "What do you think of Starchips? He's kinda fun, right?",
    //             "This window can close automatically after a countdown or manually. Up to you.",
    //             "I look forward to going to the Crushie Forest soon!"
    //         ],
    //         characterName: "Test Crushie",
    //         characterImage: "/images/test-crushie.png", // Replace with actual image path
    //         position: "center",
    //         entryDirection: "bottom",
    //         timeout: 15,
    //         typingSpeed: 50,
    //         typingMode: "letter"
    //     });
    // }
} 
window.dialogSystem = new DialogSystem(); 
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DialogSystem;
}