/* Shop Modal Styles */
.shop-container {
    padding: 20px; 
}

/* Coin Balance Bar */
.coin-balance-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(90deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
    border: 2px solid #b8860b;
    border-radius: 25px;
    padding: 10px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.coin-balance-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.coin-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.coin-balance {
    font-weight: bold;
    font-size: 16px;
    color: #8b4513;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
}

/* Shop Item */
.shop-item {
    display: flex;
    align-items: center;
    background: #fff0d8;
    border: 2px solid #9e683c;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
} 

.shop-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-color: #b87a4a;
}

.item-image {
    flex-shrink: 0;
    margin-right: 15px;
    position: relative; 
}

.material-image {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.shop-item:hover .material-image {
    transform: scale(1.05); 
}

.item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.item-name {
    font-size: 18px;
    font-weight: bold;
    color: #551843;
    margin: 0 0 8px 0;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
}

.item-cost {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #6c757d;
}

.cost-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
}

/* Blend Info */
.blend-info {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
}

.blend-description {
    margin: 0;
    color: #6c757d;
    font-style: italic;
    text-align: center; 
}

/* Blend Button */
.blend-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: 2px solid #1e7e34;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: bold;
    font-size: 16px;
    color: white;
    text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.blend-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.blend-button:hover:not(.disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}

.blend-button:hover:not(.disabled)::before {
    left: 100%;
}

.blend-button:active:not(.disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.blend-button.disabled {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border-color: #495057;
    color: #adb5bd;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.blend-button.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shop-container {
        padding: 15px;
    }
    
    .shop-item {
        flex-direction: column;
        text-align: center;
        padding: 20px 15px;
    }
    
    .item-image {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .material-image {
        width: 80px;
        height: 80px;
    }
    
    .item-name {
        font-size: 20px;
        margin-bottom: 10px;
    }
    
    .blend-button {
        width: 100%;
        padding: 15px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .coin-balance-bar {
        padding: 8px 15px;
    }
    
    .coin-balance {
        font-size: 14px;
    }
    
    .coin-icon {
        width: 20px;
        height: 20px;
    }
    
    .material-image {
        width: 70px;
        height: 70px;
    }
    
    .item-name {
        font-size: 18px;
    }
}

/* Animation for shop items */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.shop-item {
    animation: fadeInUp 0.5s ease-out;
}

/* Hover effects for town squares */
.mapsquare.town {
    cursor: pointer;
    transition: all 0.3s ease;
}

.mapsquare.town:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

/* Modal specific overrides */
.modal-content .shop-container {
    border: none;
    background: transparent;
    padding: 0;
}

.modal-body .shop-container {
    margin: 0;
} 