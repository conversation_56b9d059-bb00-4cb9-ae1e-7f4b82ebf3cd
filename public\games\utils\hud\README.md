# HUD Configuration System

This system provides a centralized way to manage HUD elements across all mini-games with consistent positioning and backgrounds.

## Overview

The HUD configuration system consists of:
- `hud-config.js` - Main configuration file with positions and element definitions
- `resource-bar.js` - ResourceBar component for progress bars
- `heart-hp.js` - HeartMeter component for heart/powerup displays
- `README.md` - This documentation

## Standardized HUD Positions

### Medium Positions (148x40 pixels)
- **hud_med_position_1** (x:154, y:50) - Used for resource bars, heart meters, etc.
- **hud_med_position_2** (x:302, y:50) - Used for resource bars, heart meters, etc.

### Small Position (46x40 pixels)
- **hud_sm_position_1** (x:454, y:50) - Used for small elements like wind direction icon

### Large Position (183x40 pixels)
- **hud_lg_position_1** (x:119, y:50) - Used for custom HUD elements that need more space

## Predefined HUD Elements

### Resource Bar
```javascript
const hpBar = HUDConfig.createHudElement('resource_bar', 'hud_med_position_1', {
    max: 100,
    value: 100,
    color: 0x39ff14,
    underColor: 0xff2222
});
```

### Heart Meter
```javascript
const heartMeter = HUDConfig.createHudElement('heart_meter', 'hud_med_position_2', {
    count: 5,
    value: 5,
    fullIcon: 'images/games/hud/heart.png',
    emptyIcon: 'images/games/hud/heart-empty.png',
    iconWidth: 24,
    iconHeight: 24,
    spacing: 4
});
```

### Powerup Bar
```javascript
const powerupBar = HUDConfig.createHudElement('powerup_bar', 'hud_med_position_1', {
    max: 10,
    value: 0,
    color: 0x00FFFF,
    underColor: 0x333333
});
```

### Wind Indicator
```javascript
const windIndicator = HUDConfig.createHudElement('wind_indicator', 'hud_sm_position_1', {
    width: 32,
    height: 32,
    icon: 'images/games/hud/wind_icon.png'
});
```

### Material List
```javascript
const materialList = HUDConfig.createHudElement('material_list', 'hud_lg_position_1', {
    title: 'COLLECT',
    fontSize: 12,
    lineHeight: 16,
    textColor: 0xFFFFFF,
    strokeColor: 0x000000,
    strokeThickness: 2
});

// Initialize with material requirements
materialList.updateMaterialList({
    wood: 3,
    metal: 2,
    glass: 1
});

// Update individual material count
materialList.updateMaterialCount('wood', 2, 3);
```

## Usage in Games

### Basic Usage
```javascript
// Create a HUD element
const hudElement = HUDConfig.createHudElement('resource_bar', 'hud_med_position_1', options);

// Add to stage with proper layering
HUDConfig.addToStage(app.stage, hudElement);
```

### Using GameUtils
```javascript
// Create with GameUtils (includes error checking)
const hudElement = GameUtils.createHudElementWithConfig('resource_bar', 'hud_med_position_1', options);

// Add to stage
GameUtils.addHudElementToStage(app.stage, hudElement);
```

## Adding New Positions

```javascript
HUDConfig.addPosition('hud_custom_position', {
    x: 200,
    y: 50,
    background: 'images/games/hud/mg_hud_custom.png',
    width: 100,
    height: 40,
    description: 'Custom HUD position'
});
```

## Adding New Elements

```javascript
HUDConfig.addElement('custom_element', {
    type: 'custom',
    defaultPosition: 'hud_med_position_1',
    defaultOptions: {
        width: 100,
        height: 20,
        // ... other options
    }
});
```

## Background Images

Each position uses a specific background image:
- `mg_hud_med.png` - Medium HUD background (148x40)
- `mg_hud_sm.png` - Small HUD background (46x40)
- `mg_hud_lg.png` - Large HUD background (183x40)

These backgrounds are automatically added behind HUD elements when using `HUDConfig.addToStage()`.

## Benefits

1. **Consistency** - All games use the same HUD positioning
2. **Maintainability** - Centralized configuration makes updates easy
3. **Reusability** - HUD elements can be easily reused across games
4. **Extensibility** - Easy to add new positions and elements
5. **Proper Layering** - Backgrounds are automatically positioned correctly