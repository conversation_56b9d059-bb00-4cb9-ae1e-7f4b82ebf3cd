const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addEscrow = (req, res) => {
	const {
		asset_id,
		owner_id,
		renter_id,
		elapsed_days,
		max_days,
		currency_type,
		balance,
		deposit,
		start_date,
		end_date,
		status
	} = req.body;
	//add to db
	pool.query(queries.add.addEscrow, [asset_id, owner_id, renter_id, elapsed_days, max_days, currency_type, balance, deposit, start_date, end_date, status], (error, results) => {
		res.status(201).send("NEW ESCROW added successfully!");
		console.log("The NEW ESCROW has been added.");
		return;
	});
};

const addBorrowedItem = (req, res) => {
	const {
		owner_id,
		asset_id,
		borrower_id,
		max_days
	} = req.body;
	pool.query(queries.add.addBorrowedItem, [owner_id, asset_id, borrower_id, max_days], (error, results) => {
		res.status(201).send("NEW BORROW ITEM added successfully!");
		console.log("The NEW BORROW ITEM has been added.");
		return;
	})
};

const removeBorrowedItem = (req, res) => {
	const asset_id = req.params.asset_id;
	pool.query(queries.remove.removeBorrowedItem, [asset_id], (error, results) => {
		res.status(200).send("Borrowed Item removed successfully.");
	});
};

const getEscrow = (req, res) => {
	pool.query(queries.get.getEscrow, (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getEscrowByAssetId = (req, res) => {
	var asset_id = req.params.asset_id;
	// const status = req.params.status;
	pool.query(queries.getby.getEscrowByAssetId, [asset_id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const removeEscrow = (req, res) => {
	const asset_id = req.params.id;
	pool.query(queries.remove.removeEscrow, [asset_id], (error, results) => {
		res.status(200).send("ITEM removed successfully.");
	});
};

const updateEscrow = (req, res) => {
	const asset_id = req.params.id;
	const {
		elapsed_days,
		balance,
		status
	} = req.body;
	pool.query(queries.up.updateReward, [elapsed_days, balance, status, asset_id], (error, results) => {
		res.status(200).send("ITEM updated successfully.");
	});
};

module.exports = {
  addEscrow,
  addBorrowedItem,
	removeBorrowedItem,
	getEscrow,
	getEscrowByAssetId,
	removeEscrow,
	updateEscrow
};
