const axios = require("axios");
const { app_url } = require("../../config");
const {
  standardPool,
  forestPool,
  healthyFruitsPool,
  waterPool,
  grassPool,
  castlePool
} = require("../data/reward-pools");
const {
  getRandomRewardFromPool,
  getStandardDustReward,
  getStandardGXPReward
} = require("../utils/rand-reward");
const {
  getRandomNum
} = require("../utils/misc");
const {
  addReward,
  updateRewardDisbStatus,
  updatePlayerBalances
} = require("../api/rewards-api");
const { checkGlobalRewardLimit, updateGlobalRewardCount } = require("../api/global-limit");
const { checkPlayerRewardLimit, updatePlayerRewardCount } = require("../api/player-limit");
const systemLogging = require("../api/system-logging");

async function getRewardByTerrainType(terrain) {
  var newReward;
  var rewardType;
  switch (terrain) {
    case 'water':
      newReward = getRandomRewardFromPool(waterPool);
      rewardType = getRandomNum();
      break;
    case 'forest':
      newReward = getRandomRewardFromPool(forestPool);
      rewardType = getRandomNum();
      break;
    case 'grassplains':
      newReward = getRandomRewardFromPool(grassPool);
      rewardType = getRandomNum();
      break;
    case 'castle':
      newReward = getRandomRewardFromPool(castlePool);
      rewardType = getRandomNum();
      break;
    case 'ruins':
      newReward = getRandomRewardFromPool(castlePool);
      rewardType = getRandomNum();
      break;
    case 'town':
      newReward = getRandomRewardFromPool(standardPool);
      rewardType = getRandomNum();
      break;
    default:
      newReward = getRandomRewardFromPool(standardPool);
      rewardType = getRandomNum();
      break;
  }

  if (rewardType === 0) {
    rewardType = "DUST";
  } else {
    rewardType = "NFT";
  }

  return [newReward, rewardType];
}

async function rewardSelectionByPool(owner_id, adventure_id, terrain) {
  console.log(`DEBUG: rewardSelectionByPool - Starting reward selection for adventure_id: ${adventure_id}, owner_id: ${owner_id}, terrain: ${terrain}`);

  try {
    console.log(`DEBUG: rewardSelectionByPool - Checking if reward already exists for adventure_id: ${adventure_id}`);
    const checkResponse = await axios.get(`${app_url}/rewards/eventid/${adventure_id}`);
    console.log(`DEBUG: rewardSelectionByPool - Check response status: ${checkResponse.status}, data length: ${checkResponse.data ? checkResponse.data.length : 0}`);

    if (checkResponse.data && checkResponse.data.length > 0) {
      console.log(`DEBUG: rewardSelectionByPool - Reward already exists for adventure_id: ${adventure_id}, type: ${checkResponse.data[0].type}`);
      return {
        success: true,
        message: "Reward already exists",
        rewardExists: true,
        rewardType: checkResponse.data[0].type
      };
    }

    console.log(`DEBUG: rewardSelectionByPool - Getting reward by terrain type for adventure_id: ${adventure_id}`);
    const [rewardData, rewardType] = await getRewardByTerrainType(terrain);
    console.log(`DEBUG: rewardSelectionByPool - Selected reward type: ${rewardType} for adventure_id: ${adventure_id}`);

    console.log(`DEBUG: rewardSelectionByPool - Checking global reward limit for type: ${rewardType}`);
    const canIssueGlobalReward = await checkGlobalRewardLimit(rewardType);
    console.log(`DEBUG: rewardSelectionByPool - Can issue global reward: ${canIssueGlobalReward} for adventure_id: ${adventure_id}`);

    // Check global limit
    let globalLimitReached = false;
    if (!canIssueGlobalReward) {
      try {
        console.log(`DEBUG: rewardSelectionByPool - Global limit reached, fetching limit data for type: ${rewardType}`);
        const response = await axios.get(`${app_url}/global-limits/${rewardType}`);
        const limitData = response.data[0];
        if (limitData) {
          console.log(`DEBUG: rewardSelectionByPool - Global limit data for type ${rewardType}: current_count: ${limitData.current_count}, max_limit: ${limitData.max_limit}`);
          const uniqueWaxIds = 0;
          const pendingWaxIds = 1;
          await systemLogging.logLimitReached(
            rewardType,
            limitData.current_count,
            uniqueWaxIds,
            limitData.max_limit,
            pendingWaxIds
          );
        }
      } catch (error) {
        console.error(`ERROR: rewardSelectionByPool - Failed to fetch global limit data:`, error.message);
        if (error.response) {
          console.error(`ERROR: rewardSelectionByPool - Status: ${error.response.status}, Data:`, JSON.stringify(error.response.data));
        }
      }

      globalLimitReached = true;
      console.log(`DEBUG: rewardSelectionByPool - Global limit reached for type: ${rewardType}`);
    }

    // Check player limit
    console.log(`DEBUG: rewardSelectionByPool - Checking player reward limit for owner_id: ${owner_id}, type: ${rewardType}`);
    const canIssuePlayerReward = await checkPlayerRewardLimit(owner_id, rewardType);
    console.log(`DEBUG: rewardSelectionByPool - Can issue player reward: ${canIssuePlayerReward} for adventure_id: ${adventure_id}`);

    let playerLimitReached = false;
    if (!canIssuePlayerReward) {
      playerLimitReached = true;
      console.log(`DEBUG: rewardSelectionByPool - Player limit reached for owner_id: ${owner_id}, type: ${rewardType}`);
    }

    // Determine initial status based on limits
    let initialStatus = "Unclaimed";
    if (globalLimitReached) {
      initialStatus = "Global Limit";
    } else if (playerLimitReached) {
      initialStatus = "Player Limit";
    }
    console.log(`DEBUG: rewardSelectionByPool - Initial status for adventure_id: ${adventure_id}: ${initialStatus}`);

    let rewardCreated = false;
    let rewardAmount = 0;

    if (rewardType === "DUST") {
      const dustAmount = getStandardDustReward();
      rewardAmount = dustAmount;
      console.log(`DEBUG: rewardSelectionByPool - Adding DUST reward for adventure_id: ${adventure_id}, amount: ${dustAmount}`);

      const result = await addReward(
        owner_id,
        adventure_id,
        "DUST",
        "DUST Reward",
        `You found ${dustAmount} DUST on your adventure!`,
        "None",
        0,
        dustAmount,
        new Date(),
        initialStatus
      );

      console.log(`DEBUG: rewardSelectionByPool - DUST reward add result for adventure_id: ${adventure_id}: ${result}`);

      if (result) {
        rewardCreated = true;
        console.log(`DEBUG: rewardSelectionByPool - Updating global and player reward counts for DUST, adventure_id: ${adventure_id}`);
        await updateGlobalRewardCount("DUST", dustAmount);
        await updatePlayerRewardCount(owner_id, "DUST", dustAmount);
        try {
          console.log(`DEBUG: rewardSelectionByPool - Fetching DUST global limit data for adventure_id: ${adventure_id}`);
          const response = await axios.get(`${app_url}/global-limits/DUST`);
          const limitData = response.data[0];
          if (limitData) {
            console.log(`DEBUG: rewardSelectionByPool - DUST global limit data: current_count: ${limitData.current_count}, max_limit: ${limitData.max_limit}`);
            const uniqueWaxIds = await getUniqueWaxIdsCount("DUST");
            const percentReached = Math.round((limitData.current_count / limitData.max_limit) * 100);
            await systemLogging.logDUSTDistribution(
              limitData.current_count,
              uniqueWaxIds,
              limitData.max_limit,
              percentReached
            );
          }
        } catch (error) {
          console.error(`ERROR: rewardSelectionByPool - Failed to fetch DUST global limit data:`, error.message);
          if (error.response) {
            console.error(`ERROR: rewardSelectionByPool - Status: ${error.response.status}, Data:`, JSON.stringify(error.response.data));
          }
        }
      } else {
        console.error(`ERROR: rewardSelectionByPool - Failed to add DUST reward for adventure_id: ${adventure_id}`);
      }

    } else if (rewardType === "NFT") {
      rewardAmount = 1;
      console.log(`DEBUG: rewardSelectionByPool - Adding NFT reward for adventure_id: ${adventure_id}, schema: ${rewardData[0]}, template: ${rewardData[1]}`);

      const result = await addReward(
        owner_id,
        adventure_id,
        "NFT",
        rewardData[2],
        `You found a ${rewardData[2]} on your adventure!`,
        rewardData[0],
        rewardData[1],
        1,
        new Date(),
        initialStatus
      );

      console.log(`DEBUG: rewardSelectionByPool - NFT reward add result for adventure_id: ${adventure_id}: ${result}`);

      if (result) {
        rewardCreated = true;
        console.log(`DEBUG: rewardSelectionByPool - Updating global and player reward counts for NFT, adventure_id: ${adventure_id}`);
        await updateGlobalRewardCount("NFT", 1);
        await updatePlayerRewardCount(owner_id, "NFT", 1);
        try {
          console.log(`DEBUG: rewardSelectionByPool - Fetching NFT global limit data for adventure_id: ${adventure_id}`);
          const response = await axios.get(`${app_url}/global-limits/NFT`);
          const limitData = response.data[0];
          if (limitData) {
            console.log(`DEBUG: rewardSelectionByPool - NFT global limit data: current_count: ${limitData.current_count}, max_limit: ${limitData.max_limit}`);
            const uniqueWaxIds = await getUniqueWaxIdsCount("NFT");
            const percentReached = Math.round((limitData.current_count / limitData.max_limit) * 100);
            await systemLogging.logNFTDistribution(
              limitData.current_count,
              uniqueWaxIds,
              limitData.max_limit,
              percentReached
            );
          }
        } catch (error) {
          console.error(`ERROR: rewardSelectionByPool - Failed to fetch NFT global limit data:`, error.message);
          if (error.response) {
            console.error(`ERROR: rewardSelectionByPool - Status: ${error.response.status}, Data:`, JSON.stringify(error.response.data));
          }
        }
      } else {
        console.error(`ERROR: rewardSelectionByPool - Failed to add NFT reward for adventure_id: ${adventure_id}`);
      }

    } else if (rewardType === "GXP") {
      const gxpAmount = getStandardGXPReward();
      rewardAmount = gxpAmount;
      const result = await addReward(
        owner_id,
        adventure_id,
        "GXP",
        "GXP Reward",
        `You earned ${gxpAmount} GXP on your adventure!`,
        "None",
        0,
        gxpAmount,
        new Date(),
        initialStatus
      );

      if (result) {
        rewardCreated = true;
        await updateGlobalRewardCount("GXP", gxpAmount);
        await updatePlayerRewardCount(owner_id, "GXP", gxpAmount);
        try {
          const response = await axios.get(`${app_url}/global-limits/GXP`);
          const limitData = response.data[0];
          if (limitData) {
            const uniqueWaxIds = await getUniqueWaxIdsCount("GXP");
            const percentReached = Math.round((limitData.current_count / limitData.max_limit) * 100);
            await systemLogging.logGXPDistribution(
              limitData.current_count,
              uniqueWaxIds,
              limitData.max_limit,
              percentReached
            );
          }
        } catch (error) {
          console.error(`ERROR: rewardSelectionByPool - Failed to fetch GXP global limit data:`, error.message);
          if (error.response) {
            console.error(`ERROR: rewardSelectionByPool - Status: ${error.response.status}, Data:`, JSON.stringify(error.response.data));
          }
        }
      }
    }

    console.log(`DEBUG: rewardSelectionByPool - Verifying reward creation for adventure_id: ${adventure_id}`);
    const verifyResponse = await axios.get(`${app_url}/rewards/eventid/${adventure_id}`);
    console.log(`DEBUG: rewardSelectionByPool - Verify response status: ${verifyResponse.status}, data length: ${verifyResponse.data ? verifyResponse.data.length : 0}`);

    if (verifyResponse.data && verifyResponse.data.length > 0) {
      rewardCreated = true;
      console.log(`DEBUG: rewardSelectionByPool - Reward verified for adventure_id: ${adventure_id}, status: ${verifyResponse.data[0].status}`);

      // NOTE: Do NOT mark as "Disbursed" here - this should only happen after successful blockchain operations
      // The reward processing system will handle marking rewards as "Disbursed" after successful blockchain operations
      console.log(`DEBUG: rewardSelectionByPool - Reward created successfully for adventure_id: ${adventure_id}, status will be updated by reward processing system`);

      return {
        success: true,
        message: `${rewardType} reward created successfully`,
        rewardType: rewardType,
        amount: rewardAmount
      };
    } else {
      console.error(`ERROR: rewardSelectionByPool - Verification failed, no reward found for adventure_id: ${adventure_id}`);
      return {
        success: false,
        message: `Failed to create ${rewardType} reward`,
        rewardType: rewardType
      };
    }

  } catch (error) {
    await systemLogging.logError(
      "REWARD",
      1,
      "ERR-1000",
      `Error processing reward: ${error.message}`
    );
    return {
      success: false,
      message: `Error processing reward: ${error.message}`,
      error: error.message
    };
  }
}

async function getUniqueWaxIdsCount(rewardType) {
  try {
    const response = await axios.get(`${app_url}/rewards`);
    const allRewards = response.data;
    const uniqueWaxIds = new Set();

    allRewards.forEach(reward => {
      if ((reward.status === 'Claimed' || reward.status === 'Disbursed' ||
           reward.status === 'Player Limit' || reward.status === 'Global Limit') &&
          reward.type === rewardType) {
        uniqueWaxIds.add(reward.wax_id);
      }
    });

    return uniqueWaxIds.size;
  } catch (error) {
    return 0;
  }
}

module.exports = {
  getRewardByTerrainType,
  rewardSelectionByPool,
  getUniqueWaxIdsCount
};