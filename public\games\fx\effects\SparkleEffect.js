// Sparkle Effect
class SparkleEffect {
    constructor(visualEffects) {
        this.visualEffects = visualEffects;
        this.sparkTextures = [];
        this.loadSparkTextures();
    }

    // Load spark textures
    loadSparkTextures() {
        for (let i = 1; i <= 3; i++) {
            const texture = PIXI.Texture.from(`/images/games/hud/sparks-${i}.png`);
            this.sparkTextures.push(texture);
        }
    }

    // Create sparkle effect
    create(x, y, stage, color = 0xFFFFFF) {
        // Create 2 sparkles instead of 1
        for (let i = 0; i < 2; i++) {
            this.createSingleSparkle(x, y, stage, color, i);
        }
    }

    // Create a single sparkle
    createSingleSparkle(x, y, stage, color = 0xFFFFFF, index = 0) {
        const sparkle = new PIXI.Sprite(this.sparkTextures[0]);
        
        // Add slight randomization to position for natural look
        const offsetX = (Math.random() - 0.5) * 8; // ±4px random offset
        const offsetY = (Math.random() - 0.5) * 8; // ±4px random offset
        
        sparkle.x = x + offsetX;
        sparkle.y = y + offsetY;
        sparkle.alpha = 1;
        sparkle.scale.set(1.0); // Increased from 0.5 to make sparkles 4x4px
        sparkle.anchor.set(0.5); // Center the sprite
        
        // Apply color tint if specified
        if (color !== 0xFFFFFF) {
            sparkle.tint = color;
        }
        
        stage.addChild(sparkle);
        
        // Animate sparkle with texture sequence
        let frame = 0;
        let scale = 1.0; // Start scale increased to match initial scale
        let alpha = 1;
        
        // Add slight timing variation between sparkles
        const startDelay = index * 50; // 50ms delay between sparkles
        
        setTimeout(() => {
            const animate = () => {
                // Update texture frame
                if (frame < this.sparkTextures.length) {
                    sparkle.texture = this.sparkTextures[frame];
                }
                
                scale += 0.1;
                alpha -= 0.05;
                
                sparkle.scale.set(scale);
                sparkle.alpha = alpha;
                
                frame++;
                
                if (alpha <= 0) {
                    stage.removeChild(sparkle);
                } else {
                    requestAnimationFrame(animate);
                }
            };
            
            animate();
        }, startDelay);
    }
} 