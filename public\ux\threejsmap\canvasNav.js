// Canvas Navigation Buttons and Map Transition Effect for Three.js Map

(function() {
  // Assumes container, overlayDivs, nav, renderWorlds, renderZones, renderLocales, updateThreeJsNavButtons are globally available
  // If not, you may need to pass them in or adjust accordingly

  function addCanvasNavButtons() {
    // Remove any previous canvas nav overlays
    overlayDivs = overlayDivs.filter(function(div) {
      if (div.classList && div.classList.contains('canvas-nav-btn')) {
        div.remove();
        return false;
      }
      return true;
    });

    // Button definitions (use legacy icons)
    var navDefs = [
      {
        key: 'world',
        icon: '../images/ui/world_icon_small.png',
        label: 'World',
        onClick: function() {
          if (typeof AudioManager !== 'undefined' && AudioManager.playUISound) {
            AudioManager.playUISound('map');
          }
          var camera = getCamera();
          var scene = getScene();
          var clickableTiles = getClickableTiles();
          var mesh = null, fromMesh = null, toMesh = null;
          var targetView = 'worlds';
          var transitionType = window.getMapTransitionType(nav.view, targetView);
          if (transitionType === 'zoom-in') {
            mesh = clickableTiles.find(function(m) { return m.userData.type === 'world'; });
          } else if (transitionType === 'zoom-out-fade') {
            if (nav.view === 'locales') {
              fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'locale' && m.userData.id === nav.locale; });
            } else if (nav.view === 'zones') {
              fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'zone' && m.userData.id === nav.zone; });
            }
            toMesh = clickableTiles.find(function(m) { return m.userData.type === 'world' && m.userData.id === nav.world; });
          }
          window.playThreeJsMapTransitionEffect({
            mesh: mesh,
            camera: camera,
            scene: scene,
            type: transitionType,
            fromMesh: fromMesh,
            toMesh: toMesh,
            fadeOnly: transitionType === 'fade-only',
            onComplete: function() {
              nav.view = 'worlds';
              nav.world = 0;
              nav.zone = 0;
              renderWorlds();
              updateThreeJsNavButtons('world');
              addCanvasNavButtons();
            }
          });
        }
      },
      {
        key: 'zone',
        icon: '../images/ui/zone_icon_small.png',
        label: 'Zone',
        onClick: function() {
          if (typeof AudioManager !== 'undefined' && AudioManager.playUISound) {
            AudioManager.playUISound('map');
          }
          var camera = getCamera();
          var scene = getScene();
          var clickableTiles = getClickableTiles();
          var mesh = null, fromMesh = null, toMesh = null;
          var targetView = 'zones';
          var transitionType = window.getMapTransitionType(nav.view, targetView);
          if (transitionType === 'zoom-in') {
            if (nav.view === 'worlds') {
              mesh = clickableTiles.find(function(m) { return m.userData.type === 'world' && m.userData.id === nav.world; });
            } else {
              mesh = clickableTiles.find(function(m) { return m.userData.type === 'zone'; });
            }
          } else if (transitionType === 'zoom-out-fade') {
            if (nav.view === 'locales') {
              fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'locale' && m.userData.id === nav.locale; });
              toMesh = clickableTiles.find(function(m) { return m.userData.type === 'zone' && m.userData.id === nav.zone; });
            } else if (nav.view === 'worlds') {
              fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'world' && m.userData.id === nav.world; });
              toMesh = clickableTiles.find(function(m) { return m.userData.type === 'zone'; });
            }
          }
          window.playThreeJsMapTransitionEffect({
            mesh: mesh,
            camera: camera,
            scene: scene,
            type: transitionType,
            fromMesh: fromMesh,
            toMesh: toMesh,
            fadeOnly: transitionType === 'fade-only',
            onComplete: function() {
              nav.view = 'zones';
              renderZones();
              updateThreeJsNavButtons('zone');
              addCanvasNavButtons();
            }
          });
        }
      },
      {
        key: 'locale',
        icon: '../images/ui/locale_icon_small.png',
        label: 'Locale',
        onClick: function() {
          if (typeof AudioManager !== 'undefined' && AudioManager.playUISound) {
            AudioManager.playUISound('map');
          }
          var camera = getCamera();
          var scene = getScene();
          var clickableTiles = getClickableTiles();
          var mesh = null, fromMesh = null, toMesh = null;
          var targetView = 'locales';
          var transitionType = window.getMapTransitionType(nav.view, targetView);
          if (transitionType === 'zoom-in') {
            if (nav.view === 'zones') {
              mesh = clickableTiles.find(function(m) { return m.userData.type === 'zone' && m.userData.id === nav.zone; });
            } else {
              mesh = clickableTiles.find(function(m) { return m.userData.type === 'locale'; });
            }
          } else if (transitionType === 'zoom-out-fade') {
            if (nav.view === 'worlds') {
              fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'world' && m.userData.id === nav.world; });
              toMesh = clickableTiles.find(function(m) { return m.userData.type === 'locale'; });
            } else if (nav.view === 'zones') {
              fromMesh = clickableTiles.find(function(m) { return m.userData.type === 'zone' && m.userData.id === nav.zone; });
              toMesh = clickableTiles.find(function(m) { return m.userData.type === 'locale'; });
            }
          }
          window.playThreeJsMapTransitionEffect({
            mesh: mesh,
            camera: camera,
            scene: scene,
            type: transitionType,
            fromMesh: fromMesh,
            toMesh: toMesh,
            fadeOnly: transitionType === 'fade-only',
            onComplete: function() {
              nav.view = 'locales';
              renderLocales();
              updateThreeJsNavButtons('locale');
              addCanvasNavButtons();
            }
          });
        }
      }
    ];

    // Layout
    const btnSize = 28;
    const labelFontSize = 8;
    const labelColor = '#fff';
    const labelFont = "'Press Start 2P', cursive";
    const gap = 16;
    const iconLabelGap = 7;
    // Calculate total width for all buttons (icon+label+gap for each)
    let totalWidth = 0;
    // Use a more generous width calculation and set a minimum width
    const minLabelWidth = 60;
    const labelWidths = navDefs.map(def => Math.max(minLabelWidth, def.label.length * labelFontSize * 1.4) + iconLabelGap + btnSize);
    totalWidth = labelWidths.reduce((a, b) => a + b, 0) + gap * (navDefs.length - 1);
    let xCursor = 0 - totalWidth / 2;
    // Move nav buttons up by 4px
    const y = -RENDERER_CONFIG.height / 2 + 18 + btnSize / 2 - 4;

    navDefs.forEach((def, i) => {
      // Icon + Label in a single clickable container
      const labelWidth = Math.max(minLabelWidth, def.label.length * labelFontSize * 1.4);
      const btnWidth = btnSize + iconLabelGap + labelWidth;
      const btnX = xCursor;
      const btnY = y;

      const containerDiv = document.createElement('div');
      containerDiv.className = 'canvas-nav-btn';
      containerDiv.style.position = 'absolute';
      containerDiv.style.left = `${container.offsetLeft + RENDERER_CONFIG.width / 2 + btnX}px`;
      containerDiv.style.top = `${container.offsetTop + RENDERER_CONFIG.height / 2 + btnY - btnSize / 2}px`;
      containerDiv.style.width = `${btnWidth}px`;
      containerDiv.style.height = `${btnSize}px`;
      containerDiv.style.display = 'flex';
      containerDiv.style.alignItems = 'center';
      containerDiv.style.gap = `${iconLabelGap}px`;
      containerDiv.style.zIndex = 100;
      containerDiv.style.cursor = 'pointer';
      containerDiv.style.background = 'rgba(0,0,0,0.0)';
      containerDiv.style.borderRadius = '6px';
      containerDiv.style.transition = 'background 0.2s, box-shadow 0.2s';

      // Highlight active view
      const navViewKey = def.key === 'world' ? 'worlds' : (def.key === 'zone' ? 'zones' : 'locales');
      if (nav.view === navViewKey) {
        containerDiv.style.background = '#fff';
        containerDiv.style.boxShadow = '0 2px 8px rgba(0,0,0,0.12)';
        containerDiv.style.color = '#222';
      }

      // Click handler
      containerDiv.addEventListener('click', (e) => {
        e.stopPropagation();
        def.onClick();
      });

      // Icon
      const iconImg = document.createElement('img');
      iconImg.src = def.icon;
      iconImg.style.width = `${btnSize}px`;
      iconImg.style.height = `${btnSize}px`;
      iconImg.style.display = 'block';
      iconImg.style.flexShrink = '0';
      iconImg.alt = def.label;

      // Label
      const labelSpan = document.createElement('span');
      labelSpan.textContent = def.label;
      labelSpan.style.color = nav.view === navViewKey ? '#222' : labelColor;
      labelSpan.style.fontSize = labelFontSize + 'px';
      labelSpan.style.fontWeight = 'bold';
      labelSpan.style.fontFamily = labelFont;
      labelSpan.style.whiteSpace = 'nowrap';
      labelSpan.style.userSelect = 'none';
      labelSpan.style.textAlign = 'left';

      containerDiv.appendChild(iconImg);
      containerDiv.appendChild(labelSpan);
      container.appendChild(containerDiv);
      overlayDivs.push(containerDiv);

      // Advance xCursor for next button
      xCursor += btnWidth + gap;
    });
  }

  // Attach to window for global access
  window.addCanvasNavButtons = addCanvasNavButtons;
})(); 