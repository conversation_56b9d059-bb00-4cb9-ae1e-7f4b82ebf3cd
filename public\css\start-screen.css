 

 .cover {
   position: fixed;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   z-index: 999;
   background: linear-gradient(45deg, #ff7f50, #edb21c);
   background: -webkit-linear-gradient(45deg, #ff7f50, #edb21c);
}


.copyright {
  font-family: 'Press Start 2P', cursive;
  font-size: 10px;
  color: white;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: center;
  height: 20px;
  padding: 5px;
  background: rgba(0, 0, 0, 0.5); /* Optional: Adds a semi-transparent background */
}

 .bg-blur {
   height: 100vh;
   overflow: hidden;  
 }

 .bg-blur.focused {
   filter: blur(0);
 }

 .login-box {
   position: absolute;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
   text-align: center;
   display: flex;
   flex-direction: column;
   align-items: center;
 }

 .nes-logo {
   width: 369px;
   margin-bottom: 40px; /* Increased space below logo for better focus */
 }


 .start-screen-description {
    font-family: 'Press Start 2P', cursive;
    color: white;
    text-align: center;
    line-height: 1.6;
    margin: 0 auto;
 }

 /* Message container */
 #message-container {
     font-family: 'Press Start 2P', cursive;
     text-align: center;
     margin-bottom: 3em;
 }

 .message-controller {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    text-align: center;
    background-color: #ffffff1f;
    padding: 20px;
    border: 4px solid white;
    border-radius: 10px;
 }

 a.audio-on[href="#"] {
    position: absolute;
    top: 10px;
    right: 10px;
    border: 4px solid white;
    border-radius: 10px;
    padding: 10px;
 }

 a.audio-on[href="#"] img {
     width: 16px;
     height: auto;
 }

 /* Remove default link styles */
 a.audio-on[href="#"] {
     text-decoration: none;
     color: inherit;
 }

#logoImage{ 
  transition: all 0.08s cubic-bezier(0.4, 0, 0.2, 1.5);   
}
#logoImage:hover{ 
  transform: scale(0.92) translateY(3px);  
}


  /* END OF START SCREEN */
