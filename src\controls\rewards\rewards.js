const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addReward = (req, res) => {
  console.log(`DEBUG: controller.addReward - Starting reward creation from controller`);

  const {
    wax_id,
    event_id,
    type,
    title,
    description,
    schema,
    template_id,
    amount,
    created_date,
    status
  } = req.body;

  console.log(`DEBUG: controller.addReward - Request body for event_id: ${event_id}, wax_id: ${wax_id}, type: ${type}, status: ${status}`);

  // Log the SQL query and parameters for debugging
  console.log(`DEBUG: controller.addReward - SQL query: ${queries.add.addReward}`);
  console.log(`DEBUG: controller.addReward - SQL parameters:`, [wax_id, event_id, type, title, description, schema, template_id, amount, created_date, status]);

  pool.query(queries.add.addReward, [wax_id, event_id, type, title, description, schema, template_id, amount, created_date, status], (error, results) => {
    if (error) {
      console.error(`ERROR: controller.addReward - Database error for event_id: ${event_id}:`, error.message);
      res.status(500).send(error.message);
      return;
    }

    console.log(`DEBUG: controller.addReward - Reward added successfully for event_id: ${event_id}, results:`, results);
    res.status(201).send("NEW REWARD added successfully!");

    let logMessage = description;
    if (type === 'NFT') {
      logMessage = "You received an NFT type reward. Claim it and it will be disbursed later automatically to your wax wallet.";
    }

    const logData = {
      desc: logMessage,
      event_id: event_id,
      type: type,
      schema: schema,
      template_id: template_id,
      amount: amount
    };

    console.log(`DEBUG: controller.addReward - Adding game log for event_id: ${event_id}`);

    pool.query(queries.add.addGameLog, [wax_id, "new", "reward", JSON.stringify(logData)], (error, results) => {
      if (error) {
        console.error(`ERROR: controller.addReward - Error adding game log for event_id: ${event_id}:`, error.message);
        res.status(500).send(error.message);
        return;
      }
      console.log(`DEBUG: controller.addReward - Game log added successfully for event_id: ${event_id}`);
    });
    return;
  });
};

const getRewards = (req, res) => {
  pool.query(queries.get.getRewards, (error, results) => {
    res.status(200).json(results.rows);
    return;
  });
};

const getRewardsByOwnerId = (req, res) => {
  var owner_id = req.params.owner_id;
  pool.query(queries.getby.getRewardsByOwnerId, [owner_id], (error, results) => {
    res.status(200).json(results.rows);
    return;
  });
};

const getRewardsByEventId = (req, res) => {
  var event_id = req.params.event_id;
  pool.query(queries.getby.getRewardsByEventId, [event_id], (error, results) => {
    res.status(200).json(results.rows);
    return;
  });
};

const removeReward = (req, res) => {
  var event_id = req.params.event_id;
  pool.query(queries.remove.removeReward, [event_id], (error, results) => {
    res.status(200).send("ITEM removed successfully.");
  });
};

const addNewLevelReward = async (wax_id, event_id, type, title, description, schema, template_id, amount, created_date, status) => {
  try {
    const result = await pool.query(
      queries.add.addReward,
      [wax_id, event_id, type, title, description, schema, template_id, amount, created_date, status]
    );

    let logMessage = description;
    if (type === 'NFT') {
      logMessage = "You received an NFT type reward. Claim it and it will be disbursed later automatically to your wax wallet.";
    }

    const logData = {
      desc: logMessage,
      event_id: event_id,
      type: type,
      schema: schema,
      template_id: template_id,
      amount: amount
    };

    await pool.query(
      queries.add.addGameLog,
      [wax_id, "new", "reward", JSON.stringify(logData)]
    );

    return true;
  } catch (error) {
    return false;
  }
};

const assignReward = async (id, reward, event_id) => {
  switch (reward.type) {
    case 'DUST':
      await addNewLevelReward(id, 6666, 'DUST', 'DUST Reward', 'New Level! Congratulations, you won a DUST reward (Level Up Reward)!', 'None', 0, 1, new Date(), 'Claimed');
      break;
    case 'NFT':
      await addNewLevelReward(id, 8888, 'NFT', 'NFT Reward', 'New Level! Congratulations, you won an NFT (Level Up Reward)!', 'foods', '230488', 1, new Date(), 'Claimed');
      break;
    case 'GXP':
      await addNewLevelReward(id, 7777, 'GXP', 'GXP Reward', 'New Level! Congratulations, you won a GXP reward (Level Up Reward)!', 'None', 0, 1, new Date(), 'Claimed');
      break;
  }
};

const updateReward = (req, res) => {
  var event_id = req.params.event_id;
  var {
    status,
    event_id: bodyEventId
  } = req.body;

  if (!event_id) {
    return res.status(400).send("Missing event_id parameter");
  }

  if (!status) {
    return res.status(400).send("Missing status in request body");
  }

  const finalEventId = bodyEventId || event_id;

  pool.query(queries.up.updateReward, [status, finalEventId], (error, results) => {
    if (error) {
      return res.status(500).send("Database error: " + error.message);
    }

    if (results.rowCount === 0) {
      return res.status(404).send("No reward found with the specified event_id");
    }

    res.status(200).send("REWARD updated successfully.");
  });
};

const updateRewardBySystem = (req, res) => {
  console.log(`DEBUG: controller.updateRewardBySystem - Starting reward update from controller`);

  var event_id = req.params.event_id;
  var disbursed_date = req.params.disbursed_date;
  var status = req.params.status;

  console.log(`DEBUG: controller.updateRewardBySystem - Request params: event_id: ${event_id}, disbursed_date: ${disbursed_date}, status: ${status}`);

  var {
    status: bodyStatus,
    disbursed_date: bodyDisbursedDate,
    event_id: bodyEventId
  } = req.body;

  // Use body values if available, otherwise use params
  const finalStatus = bodyStatus || status;
  const finalDisbursedDate = bodyDisbursedDate || disbursed_date;
  const finalEventId = bodyEventId || event_id;

  console.log(`DEBUG: controller.updateRewardBySystem - Final values: event_id: ${finalEventId}, disbursed_date: ${finalDisbursedDate}, status: ${finalStatus}`);

  // Log the SQL query and parameters for debugging
  console.log(`DEBUG: controller.updateRewardBySystem - SQL query: ${queries.up.updateRewardBySystem}`);
  console.log(`DEBUG: controller.updateRewardBySystem - SQL parameters:`, [finalStatus, finalDisbursedDate, finalEventId]);

  pool.query(queries.up.updateRewardBySystem, [finalStatus, finalDisbursedDate, finalEventId], (error, results) => {
    if (error) {
      console.error(`ERROR: controller.updateRewardBySystem - Database error for event_id: ${finalEventId}:`, error.message);
      res.status(500).send(`Database error: ${error.message}`);
      return;
    }

    console.log(`DEBUG: controller.updateRewardBySystem - Reward updated successfully for event_id: ${finalEventId}, rowCount: ${results.rowCount}`);

    if (results.rowCount === 0) {
      console.error(`ERROR: controller.updateRewardBySystem - No reward found for event_id: ${finalEventId}`);
      res.status(404).send(`No reward found with the specified event_id: ${finalEventId}`);
      return;
    }

    res.status(200).send("REWARD updated by sys successfully.");
  });
};

module.exports = {
  addReward,
  getRewards,
  getRewardsByOwnerId,
  getRewardsByEventId,
  removeReward,
  addNewLevelReward,
  assignReward,
  updateReward,
  updateRewardBySystem
};