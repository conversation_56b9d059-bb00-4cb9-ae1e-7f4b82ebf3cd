const pool = require("../db");
const queries = require("../queries");
const getPlayerCaps = (req, res) => {
  pool.query(queries.get.getPlayerCaps, (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

const getPlayerCapsByWaxId = (req, res) => {
  const waxId = req.params.wax_id;
  pool.query(queries.get.getPlayerCapsByWaxId, [waxId], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

/**
 * Get player cap by wax_id and reward type
 */
const getPlayerCapByWaxIdAndType = (req, res) => {
  const waxId = req.params.wax_id;
  const rewardType = req.params.reward_type;
  pool.query(queries.getby.getPlayerCapByWaxIdAndType, [waxId, rewardType], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }
    res.status(200).json(results.rows);
  });
};

/**
 * Update player cap count
 */
const updatePlayerCapCount = (req, res) => {
  const waxId = req.params.wax_id;
  const rewardType = req.params.reward_type;
  const { amount } = req.body;

  if (!amount || isNaN(parseFloat(amount))) {
    res.status(400).send("Invalid amount value");
    return;
  }

  // First get the current count
  pool.query(queries.getby.getPlayerCapByWaxIdAndType, [waxId, rewardType], (error, results) => {
    if (error) {
      res.status(500).send(error.message);
      console.error(error);
      return;
    }

    if (results.rows.length === 0) {
      res.status(404).send(`No player cap found for wax_id: ${waxId} and reward type: ${rewardType}`);
      return;
    }

    const currentCount = parseFloat(results.rows[0].current_count);
    const newCount = currentCount + parseFloat(amount);

    // Update the count
    pool.query(
      queries.up.updatePlayerCapCount,
      [newCount, waxId, rewardType],
      (updateError, updateResults) => {
        if (updateError) {
          res.status(500).send(updateError.message);
          console.error(updateError);
          return;
        }
        res.status(200).send(`Player cap count updated for ${waxId} and ${rewardType}`);
      }
    );
  });
};

/**
 * Reset player cap count
 */
const resetPlayerCapCount = (req, res) => {
  const waxId = req.params.wax_id;
  const rewardType = req.params.reward_type; 
  // Calculate new expiry date (24 hours from now)
  const expiryDate = new Date();
  expiryDate.setHours(expiryDate.getHours() + 24); 
  // Reset the count and update expiry date
  pool.query(
    queries.up.resetPlayerCapCount,
    [0, expiryDate, waxId, rewardType],
    (resetError, resetResults) => {
      if (resetError) {
        res.status(500).send(resetError.message);
        console.error(resetError);
        return;
      }
      res.status(200).send(`Player cap reset for ${waxId} and ${rewardType}`);
    }
  );
};

/**
 * Create a new player cap
 */
const createPlayerCap = (req, res) => {
  const { wax_id, reward_type, max_limit } = req.body;

  // Calculate expiry date (24 hours from now)
  const expiryDate = new Date();
  expiryDate.setHours(expiryDate.getHours() + 24);

  pool.query(
    queries.add.addPlayerCap,
    [wax_id, reward_type, 0, max_limit, expiryDate],
    (error, results) => {
      if (error) {
        res.status(500).send(error.message);
        console.error(error);
        return;
      }
      res.status(201).send(`Player cap created for ${wax_id} and ${reward_type}`);
    }
  );
};

module.exports = {
  getPlayerCaps,
  getPlayerCapsByWaxId,
  getPlayerCapByWaxIdAndType,
  updatePlayerCapCount,
  resetPlayerCapCount,
  createPlayerCap
};