const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addGameLog = (req, res) => {
	const {
	wax_id, status, type, data
	} = req.body;
  pool.query(queries.add.addGameLog, [wax_id, status, type, data], (err, result) => {
  	 if (err) {
  			 console.error(err);
  			 res.status(500).send('Error creating a log of this game event');
  			 return;
  	 }
  	 console.log(result);
  	 res.send('Successfully created a log of this game event');
  });
};

const removeGameLog = (req, res) => {
  pool.query(queries.remove.removeGameLog, [], (err, result) => {
    if (err) {
      console.error(err);
      return res.status(500).json({ success: false, error: 'Error removing logs.' });
    }
    console.log(`Deleted ${result.rowCount} logs.`); // Log the count
    res.json({
      success: true,
      deletedCount: result.rowCount, // Send back the count
      message: `Deleted ${result.rowCount} logs older than 30 days.`
    });
  });
};
const getGameStats = (req, res) => {
	pool.query(queries.get.getGameStats, (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getGameLog = (req, res) => {
 pool.query(queries.get.getGameLog, (error, results) => {
	 res.status(200).json(results.rows);
	 return;
 });
};

module.exports = {
  addGameLog,
	getGameStats,
	getGameLog,
	removeGameLog
};
