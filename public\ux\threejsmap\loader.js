// Loader for Three.js map navigation
(function() {
  function loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  async function loadModules() {
    // Define modules in dependency order
    const modules = [
      'ux/threejsmap/config.js',
      'ux/threejsmap/dataService.js',
      'ux/threejsmap/renderer.js',
      'ux/threejsmap/tooltip/mini-tooltip.js',
      'ux/threejsmap/views/worldRenderer.js',
      'ux/threejsmap/views/zoneRenderer.js',
      'ux/threejsmap/views/localeRenderer.js',
      'ux/threejsmap/interaction.js',
      'ux/threejsmap/map.js'
    ];

    try {
      // Load modules sequentially
      for (const module of modules) {
        await loadScript(module);
      }

      // Initialize the map after all modules are loaded
      if (typeof initThreeJsMap === 'function') {
        initThreeJsMap();
      } else {
        console.error('initThreeJsMap function not found');
      }
    } catch (error) {
      console.error('Failed to load module:', error);
    }
  }

  if (!window.THREE) {
    loadScript('https://cdn.jsdelivr.net/npm/three@0.152.2/build/three.min.js')
      .then(loadModules)
      .catch(error => console.error('Failed to load Three.js:', error));
  } else {
    loadModules();
  }
})(); 