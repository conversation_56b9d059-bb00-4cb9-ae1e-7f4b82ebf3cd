let miniTooltipElement = null;
let miniTooltipTimeout = null;

function initializeMiniTooltip() {
  miniTooltipElement = document.createElement('div');
  miniTooltipElement.id = 'threejs-mini-tooltip';
  miniTooltipElement.style.cssText = `
    position: fixed;
    background: white;
    color: black;
    padding: 6px 8px;
    border: 1px solid #747474;
    border-radius: 3px;
    font-family: 'CrushieFont', 'Press Start 2P', monospace;
    font-size: 8px;
    line-height: 1.2;
    z-index: 99999;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.15s ease;
    max-width: 150px;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: left;
  `;
  document.body.appendChild(miniTooltipElement);
}

function showMiniTooltip(content, x, y) {
  if (!miniTooltipElement) {
    initializeMiniTooltip();
  }
  if (miniTooltipTimeout) {
    clearTimeout(miniTooltipTimeout);
  }
  miniTooltipElement.innerHTML = content;
  miniTooltipElement.style.left = `${x + 10}px`;
  miniTooltipElement.style.top = `${y - 10}px`;
  miniTooltipElement.style.opacity = '1';
  const rect = miniTooltipElement.getBoundingClientRect();
  if (rect.right > window.innerWidth) {
    miniTooltipElement.style.left = `${window.innerWidth - rect.width - 10}px`;
  }
  if (rect.top < 0) {
    miniTooltipElement.style.top = `${y + 20}px`;
  }
}

function hideMiniTooltip() {
  if (miniTooltipElement) {
    miniTooltipElement.style.opacity = '0';
  }
  if (miniTooltipTimeout) {
    clearTimeout(miniTooltipTimeout);
  }
}

function showMiniTooltipWithDelay(content, x, y, delay = 100) {
  if (miniTooltipTimeout) {
    clearTimeout(miniTooltipTimeout);
  }
  miniTooltipTimeout = setTimeout(() => {
    showMiniTooltip(content, x, y);
  }, delay);
}

function getLocaleMiniTooltipContent(locale, localeId, worldId, zoneId) {
  const worldNames = ['Forest World', 'Tropical World', 'Desert World', 'Space World'];
  const worldName = worldNames[worldId] || `World ${worldId + 1}`;
  const zoneName = `Zone ${zoneId + 1}`;
  const localeName = locale?.Locale_Name || `Tile ${localeId + 1}`;
  const tileType = locale?.Tile || 'grassplains';
  const terrain = locale?.Terrain || 'Land';
  const hasAdventure = locale?.hasAdventure || false;
  const hasClaimable = locale?.hasClaimable || false;
  
  let content = `${worldName}<br>`;
  content += `${zoneName}<br>`;
  content += `${localeName} #${localeId + 1}<br>`;
  content += `Type: ${terrain}`;
  if (hasAdventure) {
    content += '<br>Adventure Active';
  }
  if (hasClaimable) {
    content += '<br><span style="color: #FFD700; font-weight: bold;">Click to claim 10 GXP!</span>';
  }
  return content;
}

function handleMiniTooltipMouseEnter(event, userData) {
  if (userData.type !== 'locale') {
    return;
  }
  const x = event.clientX;
  const y = event.clientY;
  const currentWorld = nav?.world || 0;
  const currentZone = nav?.zone || 0;
  const content = getLocaleMiniTooltipContent(
    userData.locale, 
    userData.id, 
    currentWorld, 
    currentZone
  );
  showMiniTooltipWithDelay(content, x, y);
}

function handleMiniTooltipMouseLeave() {
  hideMiniTooltip();
}

function cleanupMiniTooltip() {
  if (miniTooltipElement) {
    miniTooltipElement.remove();
    miniTooltipElement = null;
  }
  if (miniTooltipTimeout) {
    clearTimeout(miniTooltipTimeout);
    miniTooltipTimeout = null;
  }
}