async function renderLocales() {
  resetCameraForView('locales');
  clearScene();
  
  const { size, gap, gridSize } = GRID_CONFIG.locales;
  
  const locales = getLocaleData(nav.world, nav.zone);
  const activeAdventures = getActiveAdventures(nav.world, nav.zone);
  const activeLocales = activeAdventures.map(adv => adv.mapgrid_256);
  
  // Get claimable state for this world/zone
  const claimableState = window.claimable ? window.claimable.getClaimableState(nav.world, nav.zone) : { found: true, tile: -1 };
  
  for (let row = 0; row < gridSize; row++) {
    for (let col = 0; col < gridSize; col++) {
      const x = (col - 7.5) * (size + gap);
      const y = (7.5 - row) * (size + gap);
      const reversedRow = gridSize - 1 - row;
      const idx = row * gridSize + col;
      
      const locale = locales[idx];
      const tileType = locale?.Tile || 'grassplains';
      const tileImage = getTileImage(tileType);
      const hasAdventure = activeLocales.includes(idx);
      
      // Check if this locale has a claimable
      const hasClaimable = !claimableState.found && Number(idx) === Number(claimableState.tile);
      
      try {
        await createClickableMesh(tileImage, x, y, size, 4, { 
          type: 'locale', 
          id: idx, 
          locale, 
          hasAdventure,
          hasClaimable,
          claimableState
        });
      } catch (error) {
        const fallbackColor = getTileFallbackColor(tileType);
        createFallbackMesh(x, y, size, 4, { 
          type: 'locale', 
          id: idx, 
          locale, 
          hasAdventure,
          hasClaimable,
          claimableState
        }, fallbackColor);
      }
      
      if (hasAdventure) {
        addOverlayImage(IMAGES.adventure, x, y, size * 0.8, 12);
      }
      
      // Add claimable overlay if this locale has a claimable
      if (hasClaimable) {
        addOverlayImage('images/ui/fx/claimable.gif', x, y, size * 0.6, 14, async () => {
          // Handle claimable click
          try {
            // Prevent double claim
            if (claimableState.found) {
              console.log('Claimable already found, ignoring click');
              return;
            }
            
            // Add GXP reward
            if (typeof transactResource === 'function') {
              await transactResource('gxp', 10, 'add', showAlert, updatePlayerBalances);
            } else {
              console.warn('transactResource function not available');
              // Fallback: just show alert
              if (typeof showAlert === 'function') {
                showAlert("You found 10 GXP!");
              }
            }
            
            // Mark claimable as found
            if (window.claimable && typeof window.claimable.setClaimableFound === 'function') {
              window.claimable.setClaimableFound(nav.world, nav.zone);
            }
            
            // Show success message
            if (typeof showAlert === 'function') {
              showAlert("You found 10 GXP! There are no more claimables for you today.");
            }
            
            // Refresh the locale view to remove the claimable icon
            if (typeof renderLocales === 'function') {
              await renderLocales();
            }
            
          } catch (error) {
            console.error('Error handling claimable click:', error);
            if (typeof showAlert === 'function') {
              showAlert('Error claiming reward. Please try again.');
            }
          }
        });
      }
    }
  }
  
  await renderTeamElements();
  
  renderAdventureSettingStatus();
  
  if (typeof updateThreeJsNavButtons === 'function') {
    updateThreeJsNavButtons('locale');
  }
  if (typeof addCanvasNavButtons === 'function') {
    addCanvasNavButtons();
  }
}

async function refreshLocaleView() {
  if (currentView === 'locales') {
    await renderLocales();
  }
}

async function renderTeamElements() {
  try {
    let allAdventures = [];
    let playerAdventures = [];
    
    if (typeof getAdventures === 'function') {
      allAdventures = await getAdventures();
    }
    
    if (typeof getAdventuresByPlayer === 'function') {
      playerAdventures = await getAdventuresByPlayer();
    }
    
    if (!allAdventures || !Array.isArray(allAdventures)) {
      return;
    }
    
    const currentAdventures = allAdventures.filter(adventure => 
      adventure.status === 'In Progress' &&
      adventure.mapgrid_4 === nav.world &&
      adventure.mapgrid_16 === nav.zone
    );
    
    const playerCurrentAdventures = playerAdventures.filter(adventure => 
      adventure.status === 'In Progress' &&
      adventure.mapgrid_4 === nav.world &&
      adventure.mapgrid_16 === nav.zone
    );
    
    const completedAdventures = playerAdventures.filter(adventure => 
      adventure.status === 'Complete' &&
      adventure.mapgrid_4 === nav.world &&
      adventure.mapgrid_16 === nav.zone
    );
    
    for (const adventure of currentAdventures) {
      const isPlayerAdventure = adventure.owner_id === (typeof wax !== 'undefined' ? wax.userAccount : null);
      const owner = isPlayerAdventure ? 'player' : 'npc';
      
      const localeIndex = adventure.mapgrid_256;
      const row = Math.floor(localeIndex / 16);
      const col = localeIndex % 16;
      const { size, gap } = GRID_CONFIG.locales;
      const x = (col - 7.5) * (size + gap);
      const y = (7.5 - row) * (size + gap);
      
      await renderVehicleIcon(owner, adventure, x, y, size);
      
      renderTeamIcon(owner, adventure, x, y, size);
    }
    
    for (const adventure of completedAdventures) {
      const localeIndex = adventure.mapgrid_256;
      const row = Math.floor(localeIndex / 16);
      const col = localeIndex % 16;
      const { size, gap } = GRID_CONFIG.locales;
      const x = (col - 7.5) * (size + gap);
      const y = (7.5 - row) * (size + gap);
      
      renderTreasureIcon(adventure, x, y, size);
    }
    
    if (completedAdventures.length > 0) {
      renderReadyTeamIcon(completedAdventures.length);
    }
    
  } catch (error) {
  }
}

async function renderVehicleIcon(owner, adventure, x, y, size) {
  try {
    let vehicleType = 'land';
    
    if (owner === 'player' && typeof myTeams !== 'undefined') {
      const team = myTeams.find(t => t.team_id === adventure.team_id);
      if (team && typeof getVehicleInfoByTeamId === 'function') {
        vehicleType = await getVehicleInfoByTeamId('terrain', adventure.team_id, myTeams, vehiclesData);
      }
    } else if (owner === 'npc' && typeof allTeams !== 'undefined') {
      vehicleType = getTerrainTypeByTeamId(adventure.team_id, allTeams);
    }
    
    let iconPath;
    if (owner === 'player') {
      switch (vehicleType) {
        case 'land': iconPath = 'images/ui/player/land_vehicle_icon.gif'; break;
        case 'water': iconPath = 'images/ui/player/water_vehicle_icon.gif'; break;
        case 'space': iconPath = 'images/ui/player/space_vehicle_icon.gif'; break;
        default: iconPath = 'images/ui/player/land_vehicle_icon.gif';
      }
    } else {
      switch (vehicleType) {
        case 'land': iconPath = 'images/ui/npc/land_vehicle_icon_npc.gif'; break;
        case 'water': iconPath = 'images/ui/npc/water_vehicle_icon_npc.gif'; break;
        case 'space': iconPath = 'images/ui/npc/space_vehicle_icon_npc.gif'; break;
        default: iconPath = 'images/ui/npc/land_vehicle_icon_npc.gif';
      }
    }
    
    addOverlayImage(iconPath, x - size/2 + 2, y - size/2 + 2, 16, 16);
    
  } catch (error) {
  }
}

function renderTeamIcon(owner, adventure, x, y, size) {
  try {
    const teamText = `Team #${adventure.team_id}`;
    const textColor = owner === 'player' ? '#0B5ED7' : '#666666';
    const bgColor = owner === 'player' ? 'rgba(11, 94, 215, 0.8)' : 'rgba(102, 102, 102, 0.8)';
    
    addLabel(teamText, x, y + size/2 - 8, size, textColor, 10, {
      background: bgColor,
      borderRadius: '2px',
      padding: '1px 4px'
    });
    
  } catch (error) {
  }
}

function renderTreasureIcon(adventure, x, y, size) {
  try {
    addOverlayImage('images/ui/reward_icon.png', x + size/2 - 8, y - size/2 + 2, 16, 16);
    
    const teamText = `Team #${adventure.team_id}`;
    addLabel(teamText, x, y + size/2 - 8, size, '#000000', 10, {
      background: 'rgba(255, 215, 0, 0.9)',
      borderRadius: '2px',
      padding: '1px 4px'
    });
    
  } catch (error) {
  }
}

function renderReadyTeamIcon(completedCount) {
  try {
    const { size, gap, gridSize } = GRID_CONFIG.locales;
    const mapWidth = gridSize * (size + gap) - gap;
    const mapHeight = gridSize * (size + gap) - gap;
    
    const iconX = -mapWidth/2 + 20;
    const iconY = mapHeight/2 - 20;
    
    addOverlayImage('images/ui/ready_icon.gif', iconX, iconY, 12, 12);
    
    addLabel(`${completedCount} Team(s) Ready!`, iconX + 16, iconY, 200, '#0B5ED7', 12, {
      background: 'rgba(11, 94, 215, 0.8)',
      borderRadius: '4px',
      padding: '2px 6px'
    });
    
  } catch (error) {
  }
}

function renderAdventureSettingStatus() {
  try {
    if (typeof enableSetAdventure !== 'undefined' && enableSetAdventure && 
        typeof teamSelectedForAdventure !== 'undefined' && teamSelectedForAdventure) {
      
      const { size, gap, gridSize } = GRID_CONFIG.locales;
      const mapWidth = gridSize * (size + gap) - gap;
      const mapHeight = gridSize * (size + gap) - gap;
      
      const statusX = mapWidth/2 - 200;
      const statusY = mapHeight/2 - 20;
      
      addLabel('Click any locale to set destination', statusX, statusY + 20, 200, '#FF6B35', 10, {
        background: 'rgba(255, 107, 53, 0.8)',
        borderRadius: '4px',
        padding: '1px 4px'
      });
    }
  } catch (error) {
  }
}

function getTileFallbackColor(tileType) {
  const colorMap = {
    'grassplains': 0x4CAF50,
    'water': 0x2196F3,
    'forest': 0x2E7D32,
    'crushieforest': 0x388E3C,
    'lava': 0xF44336,
    'mountain': 0x795548,
    'cyber': 0x9C27B0,
    'fortress': 0x607D8B,
    'castle': 0x9E9E9E,
    'ruins': 0x8D6E63,
    'town': 0xFF9800,
    'tr_water': 0x00BCD4,
    'tr_castle': 0x009688,
    'tr_island': 0x8BC34A,
    'tr_waterland': 0x4DB6AC,
    'ds_dirt': 0xD7CCC8,
    'ds_castle': 0xBCAAA4,
    'ds_dunes': 0xFFCC02,
    'ds_ruins': 0xA1887F,
    'ds_town': 0xFF8A65,
    'sp_normal': 0x3F51B5,
    'sp_gas1': 0x673AB7,
    'sp_debris': 0x424242,
    'sp_station1': 0x607D8B,
    'sp_gplanet1': 0x4CAF50,
    'sp_dplanet1': 0x795548,
    'sp_iplanet1': 0x2196F3,
    'sp_rplanet1': 0xF44336
  };
  
  return colorMap[tileType] || 0x9E9E9E;
}