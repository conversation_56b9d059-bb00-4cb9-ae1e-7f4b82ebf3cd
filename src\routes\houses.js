// where the express routes are stored
const { Router } = require('express');
const allQuery = require("../controls/controller");

const router = Router();

router.get("/", allQuery.getHouses);
router.get("/:owner_id", allQuery.getHousesByOwnerId);
router.get("/renter/:renter_id", allQuery.getHousesByRenterId);
router.get("/assetid/:asset_id", allQuery.getHousesByAssetId);
router.get("/status/:status", allQuery.getHousesByStatus);

router.post("/", allQuery.addHouse);
router.delete("/:owner_id/:asset_id", allQuery.removeHouse);
router.put("/:asset_id", allQuery.updateHouse);
router.put("/update/:asset_id", allQuery.updateHouseStatus);
module.exports = router;
