const pool = require("../db");
const queries = require("../queries"); 

const addPlayerCaps = (wax_id, callback) => {
  const expiryDate = new Date();
  expiryDate.setHours(expiryDate.getHours() + 24); // 24-hour expiry

  // Array of cap configurations
  const capConfigs = [
    { type: 'GXP', current: 0, max: 100 },
    { type: 'DUST', current: 0, max: 100 },
    { type: 'NFT', current: 0, max: 1 },
    { type: 'NECTAR', current: 0, max: 100 }
  ];

  // Use Promise.all to handle all cap insertions concurrently
  Promise.all(
    capConfigs.map(config =>
      pool.query(queries.add.addPlayerCap, [
        wax_id,
        config.type,
        config.current,
        config.max,
        expiryDate
      ])
    )
  )
    .then(() => {
      console.log('All player caps added successfully');
      callback(null);
    })
    .catch(error => {
      console.error('Error adding player caps:', error);
      callback(error);
    });
};

const addPlayer = (req, res) => { 
  pool.query(queries.exist.checkWaxIDExists, [wax_id], (err, result) => {
    if (err) {
      console.error(err);
      res.status(500).send('Error checking if wax_id exists');
      return;
    }

    if (result.rowCount > 0) {
      res.status(409).send('Player with this wax_id already exists');
      return;
    }

    pool.query(queries.add.addPlayer, [wax_id, gxp, date_joined, last_online, nectar, credits], (error, results) => {
      if (error) {
        console.error(error);
        res.status(500).send('Error adding player');
        return;
      }

      const playerLevelSettings = {
        nectar_max: 3,
        credits_max: 3,
        adventure_gxp_bonus: 0,
        nap_rate_bonus: 0
      };

      pool.query(queries.add.addPlayerSettings, [wax_id, playerLevelSettings], (settingsError, settingsResults) => {
        if (settingsError) {
          console.error('Error adding player settings:', settingsError);
          res.status(500).send('Error adding player settings');
          return;
        }

        addPlayerCaps(wax_id, (capError) => {
          if (capError) {
            console.error('Error adding player caps:', capError);
            res.status(500).send('Error adding player caps');
            return;
          }

          console.log('Player, player settings, and player caps added successfully.');
          res.status(201).send('Player, player settings, and player caps added successfully.');
        });
      });
    });
  });
};

const getPlayers = (req, res) => {
	pool.query(queries.get.getPlayers, (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getPlayerById = (req, res) => {
	var id = req.params.id;
	pool.query(queries.getby.getPlayerById, [id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getPlayerStatsById = (req, res) => {
	var id = req.params.id;
	pool.query(queries.getby.getPlayerStatsById, [id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const removePlayer = (req, res) => {
	const id = req.params.id;
	pool.query(queries.remove.removePlayer, [id], (error, results) => {
		res.status(200).send("Player removed successfully.");
	});
};

const getPlayerLevelById = (req, res) => {
	var wax_id = req.params.id;
	pool.query(queries.getby.getPlayerLevelById, [wax_id], (error, results) => {
    console.log("YOUR PLAYER LEVEL IS : " + wax_id);
    console.log(results.rows);
		res.status(200).json(results.rows);
		return;
	});
};

const getPlayerSettingsById = (req, res) => {
    var wax_id = req.params.id;

    // Retrieve player settings
    pool.query(queries.getby.getPlayerSettingsById, [wax_id], (error, results) => {
        console.log("YOUR PLAYER SETTINGS ARE: ", results.rows);
        if (results.rows.length > 0) {
            res.status(200).json(results.rows);
            return;
        }
        // If player settings don't exist, create new row with default settings
        var playerLevelSettings = {
            nectar_max: 3,
            credits_max: 3,
            adventure_gxp_bonus: 0,
            nap_rate_bonus: 0
        };
        const addPlayerSettings = 'INSERT INTO player_settings (wax_id, data) VALUES ($1, $2::jsonb)';
        pool.query(addPlayerSettings, [wax_id, playerLevelSettings], (settingsError, settingsResults) => {
            if (settingsError) {
                console.error('Error adding player settings:', settingsError);
                res.status(500).send('Internal Server Error');
            } else {
                console.log('Player and player settings added successfully.');
                res.status(201).send('Player and player settings added successfully.');
            }
        });
    });
};  

const updatePlayer = async (req, res) => {
  try {
    const id = req.params.id;
    console.log('\n[updatePlayer] Starting update for player:', id);

    // Validate request body
    const { gxp, last_online, nectar, credits } = req.body;
    console.log('[updatePlayer] Request body:', { gxp, last_online, nectar, credits });

    // Validate input values
    if (gxp === undefined || !Number.isInteger(gxp) || gxp < 0) {
      console.error('[updatePlayer] Invalid GXP value:', gxp);
      return res.status(400).send("Invalid GXP value.");
    }
    if (nectar === undefined || !Number.isInteger(nectar) || nectar < 0) {
      console.error('[updatePlayer] Invalid nectar value:', nectar);
      return res.status(400).send("Invalid nectar value.");
    }
    if (credits === undefined || !Number.isInteger(credits) || credits < 0) {
      console.error('[updatePlayer] Invalid credits value:', credits);
      return res.status(400).send("Invalid credits value.");
    }

    // Authentication check
    console.log('\n[updatePlayer] Authentication check for user:', id);

    const authHeader = req.headers.authorization;
    console.log('[updatePlayer] Auth header:', authHeader ? `${authHeader.substring(0, 15)}...` : 'MISSING');
    const playerResults = await pool.query(queries.getby.getPlayerById, [id]);

    if (playerResults.rows.length === 0) {
      console.error('[updatePlayer] Player not found:', id);
      return res.status(404).send("Player does not exist in the database, could not update.");
    }
    console.log('[updatePlayer] Updating player data for:', id);
    console.log('[updatePlayer] New values:', { gxp, last_online, nectar, credits });

    await pool.query(queries.up.updatePlayer, [gxp, last_online, nectar, credits, id]);
    console.log('[updatePlayer] Update successful for player:', id);

    return res.status(200).send("Player updated successfully.");
  } catch (error) {
    console.error('[updatePlayer] Error during player update:', error.message);
    console.error('[updatePlayer] Error stack:', error.stack);
    return res.status(500).send("Internal Server Error: " + error.message);
  }
};  

module.exports = {
  addPlayer,
  getPlayers,
  getPlayerById,
  getPlayerStatsById,
  getPlayerLevelById,
  getPlayerSettingsById,
  removePlayer,
  updatePlayer
};
