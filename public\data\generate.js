const flowers = ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>chi<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Lu<PERSON>ous", "<PERSON>ia", "<PERSON>"];
const colors = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Azure"];
const verbs = ["Jumping", "Twirly", "Flying", "Bouncing", "Sparkling", "Dazzling", "Sprinting", "Galloping", "Whirling", "Zooming"];
const romanNumerals = ["I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X"];

var forestWorld = [
	[0, 0, 'Forest Zone 0', 'Forest', 0, 100000, 'locked'],
	[0, 1, 'Forest Zone 1', 'Forest', 0, 100000, 'locked'],
	[0, 2, 'Forest Zone 2', 'Forest', 0, 100000, 'locked'],
	[0, 3, 'Forest Zone 3', 'Forest', 0, 100000, 'locked'],
	[0, 4, 'Forest Zone 4', 'Forest', 0, 100000, 'locked'],
	[0, 5, 'Forest Zone 5', 'Forest', 0, 100000, 'locked'],
	[0, 6, 'Forest Zone 6', 'Forest', 0, 100000, 'locked'],
	[0, 7, 'Forest Zone 7', 'Forest', 0, 100000, 'locked'],
	[0, 8, 'Forest Zone 8', 'Forest', 0, 100000, 'locked'],
	[0, 9, 'Forest Zone 9', 'Forest', 0, 100000, 'locked'],
	[0, 10, 'Forest Zone 10', 'Forest', 0, 100000, 'locked'],
	[0, 11, 'Forest Zone 11', 'Forest', 0, 100000, 'locked'],
	[0, 12, 'Forest Zone 12', 'Forest', 0, 100000, 'locked'],
	[0, 13, 'Forest Zone 13', 'Forest', 0, 100000, 'locked'],
	[0, 14, 'Forest Zone 14', 'Forest', 0, 100000, 'locked'],
	[0, 15, 'Forest Zone 15', 'Forest', 0, 100000, 'locked']
];
var tropicWorld = [
	[1, 0, 'Tropic Zone 0', 'Tropic', 0, 400000, 'locked'],
	[1, 1, 'Tropic Zone 1', 'Tropic', 0, 400000, 'locked'],
	[1, 2, 'Tropic Zone 2', 'Tropic', 0, 400000, 'locked'],
	[1, 3, 'Tropic Zone 3', 'Tropic', 0, 400000, 'locked'],
	[1, 4, 'Tropic Zone 4', 'Tropic', 0, 400000, 'locked'],
	[1, 5, 'Tropic Zone 5', 'Tropic', 0, 400000, 'locked'],
	[1, 6, 'Tropic Zone 6', 'Tropic', 0, 400000, 'locked'],
	[1, 7, 'Tropic Zone 7', 'Tropic', 0, 400000, 'locked'],
	[1, 8, 'Tropic Zone 8', 'Tropic', 0, 400000, 'locked'],
	[1, 9, 'Tropic Zone 9', 'Tropic', 0, 400000, 'locked'],
	[1, 10, 'Tropic Zone 10', 'Tropic', 0, 400000, 'locked'],
	[1, 11, 'Tropic Zone 11', 'Tropic', 0, 400000, 'locked'],
	[1, 12, 'Tropic Zone 12', 'Tropic', 0, 400000, 'locked'],
	[1, 13, 'Tropic Zone 13', 'Tropic', 0, 400000, 'locked'],
	[1, 14, 'Tropic Zone 14', 'Tropic', 0, 400000, 'locked'],
	[1, 15, 'Tropic Zone 15', 'Tropic', 0, 400000, 'locked']
];
var desertWorld = [
	[2, 0, 'Desert Zone 0', 'Desert', 0, 1250000, 'locked'],
	[2, 1, 'Desert Zone 1', 'Desert', 0, 1250000, 'locked'],
	[2, 2, 'Desert Zone 2', 'Desert', 0, 1250000, 'locked'],
	[2, 3, 'Desert Zone 3', 'Desert', 0, 1250000, 'locked'],
	[2, 4, 'Desert Zone 4', 'Desert', 0, 1250000, 'locked'],
	[2, 5, 'Desert Zone 5', 'Desert', 0, 1250000, 'locked'],
	[2, 6, 'Desert Zone 6', 'Desert', 0, 1250000, 'locked'],
	[2, 7, 'Desert Zone 7', 'Desert', 0, 1250000, 'locked'],
	[2, 8, 'Desert Zone 8', 'Desert', 0, 1250000, 'locked'],
	[2, 9, 'Desert Zone 9', 'Desert', 0, 1250000, 'locked'],
	[2, 10, 'Desert Zone 10', 'Desert', 0, 1250000, 'locked'],
	[2, 11, 'Desert Zone 11', 'Desert', 0, 1250000, 'locked'],
	[2, 12, 'Desert Zone 12', 'Desert', 0, 1250000, 'locked'],
	[2, 13, 'Desert Zone 13', 'Desert', 0, 1250000, 'locked'],
	[2, 14, 'Desert Zone 14', 'Desert', 0, 1250000, 'locked'],
	[2, 15, 'Desert Zone 15', 'Desert', 0, 1250000, 'locked']
];
var spaceWorld = [
	[3, 0, 'Space Zone 0', 'Space', 0, 7200000, 'locked'],
	[3, 1, 'Space Zone 1', 'Space', 0, 7200000, 'locked'],
	[3, 2, 'Space Zone 2', 'Space', 0, 7200000, 'locked'],
	[3, 3, 'Space Zone 3', 'Space', 0, 7200000, 'locked'],
	[3, 4, 'Space Zone 4', 'Space', 0, 7200000, 'locked'],
	[3, 5, 'Space Zone 5', 'Space', 0, 7200000, 'locked'],
	[3, 6, 'Space Zone 6', 'Space', 0, 7200000, 'locked'],
	[3, 7, 'Space Zone 7', 'Space', 0, 7200000, 'locked'],
	[3, 8, 'Space Zone 8', 'Space', 0, 7200000, 'locked'],
	[3, 9, 'Space Zone 9', 'Space', 0, 7200000, 'locked'],
	[3, 10, 'Space Zone 10', 'Space', 0, 7200000, 'locked'],
	[3, 11, 'Space Zone 11', 'Space', 0, 7200000, 'locked'],
	[3, 12, 'Space Zone 12', 'Space', 0, 7200000, 'locked'],
	[3, 13, 'Space Zone 13', 'Space', 0, 7200000, 'locked'],
	[3, 14, 'Space Zone 14', 'Space', 0, 7200000, 'locked'],
	[3, 15, 'Space Zone 15', 'Space', 0, 7200000, 'locked']
];

var terrainTypes = ['land', 'water', 'space'];
var naturalTiles = ['grassplains', 'forest', 'water', 'ruins', 'town', 'castle'];
var tropicTiles = ['tr_water', 'tr_water', 'tr_water', 'tr_island', 'tr_waterland', 'tr_castle'];
var desertTiles = ['ds_dirt', 'ds_dirt', 'ds_dunes', 'ds_ruins', 'ds_town', 'ds_castle'];
var spaceTiles = ['sp_normal', 'sp_normal', 'sp_normal', 'sp_gas1', 'sp_debris', 'sp_station1'];
var forests = ["Spooky", "Whispers", "Mushbrooms", "Creaky", "Dewdrop", "Spiderweb", "Old Branch", "Shadowy", "Scratchingpost", "Monkeydream", "Birdbirch", "Perching Pine", "Howling Oaks", "Caterpillar", "Barking Birch", "Fairy Elm", "Creaking Elm", "Creaking Timber", "Mister E.", "Mumbling Mosquito", "Mossy", "Berrytree", "Sneakytree", "Run-n-Hide", "Tangleknot", "Wovensilk", "Spiderking", "Green Wizard's", "Screeching Timber"];
var waters = ["Calm Waters", "Gentlebubbles Pond", "Mooshy Marshes", "Pleasant Pond", "Easy Sailing Lake", "Beachball Beach", "Soggy Britches Lake", "Mildew Marsh", "Cat Tails Marsh", "Cat Tails Cove", "Sunbeam Shore"];
var lands = ["Grassy Hills", "Purring Plains", "Butterfly Fields", "Happy Plains", "Picnic Valley", "Summerwind Valley", "Bird Song Hills", "Morningcheer Hills"];
var castles = ["Shakyknees", "Tremblewhisker", "Saberfang", "Fleefoot", "Moonhowl", "Stayaway", "Dangerzone", "Afraidmore", "Oh-no", "Hiccup"];
var ruins = ["Ancient"];
var towns = ["Relaxing"];

// random names for mixing
var grasses = ["Grassy Hills", "Purring Plains", "Butterfly Fields", "Happy Plains", "Picnic Valley", "Summerwind Valley", "Bird Song Hills", "Morningcheer Hills"];
var forests = ["Spooky", "Whispers", "Mushbrooms", "Creaky", "Dewdrop", "Spiderweb", "Old Branch", "Shadowy", "Scratchingpost", "Monkeydream", "Birdbirch", "Perching Pine", "Howling Oaks", "Caterpillar", "Barking Birch", "Fairy Elm", "Creaking Elm", "Creaking Timber", "Mister E.", "Mumbling Mosquito", "Mossy", "Berrytree", "Sneakytree", "Run-n-Hide", "Tangleknot", "Wovensilk", "Spiderking", "Green Wizard's", "Screeching Timber"];
var waters = ["Calm Waters", "Gentlebubbles Pond", "Mooshy Marshes", "Pleasant Pond", "Easy Sailing Lake", "Beachball Beach", "Soggy Britches Lake", "Mildew Marsh", "Cat Tails Marsh", "Cat Tails Cove", "Sunbeam Shore"];
var castles = ["Shakyknees", "Tremblewhisker", "Saberfang", "Fleefoot", "Moonhowl", "Stayaway", "Dangerzone", "Afraidmore", "Oh-no", "Hiccup"];
var ruins = ["Mossy", "Dusty", "Rubbish", "Foggy", "Dewdust", "Old Castle", "Small Castle", "Large Castle"];
var towns = ["Leaky Roof", "Gentle", "Happy", "Sweet Sunny", "Summerwake", "Truewishes", "Fisher\'s", "Barron\'s", "Redlond", "Kistal", "Seamont", "King", "Tinroof", "Wetbarrel"];
// TROPIC LOCATION NAMES
var tr_castles = ["Sammba", "Kal-la-la", "Jiggy", "Sunnybeach", "Summershine"];
var tr_islands = ["Coconutty", "Softee Sand", "Silkspell", "Cheerful", "Gleebloom", "Beachball", "Cocktail", "Tangerhyme", "Blueski", "Dreamy", "White Whale"];
var tr_waterlands = ["Sharcana", "Turtlesh", "Lagoonka", "Shellkelesh", "Eelkwesh"];
var tr_water = ["Starry", "Shiny", "Easy", "Calm"];
// DESERT LOCATION NAMES
var ds_castles = ["Messterious", "Empty", "Abandoned", "Whatch-Yer-Step", "No Chill", "Dusty Bucket", "Cracker Dupe", "Dust Haven"];
var ds_dirt = ["Dusty Plains", "Snake Hole Valley", "Cactus Plains", "Nothing-to-See Valley", "Eh Plains"];
var ds_dunes = ["Rolling", "Tumble", "Frenly", "Sunny"];
var ds_ruins = ["Dusty", "Orange Dust", "Red Dust", "Old", "Small", "Large"];
var ds_towns = ["Friendly", "Mining", "Quiet", "Small"];
// SPACE LOCATION NAMES
var sp_space = ["Normal", "Casual", "Starry", "Starry", "Empty"];
var sp_gasfields = ["Strange Gas", "Space-Genius", "Funky", "Glowing Gas", "Gas"];
var sp_stations = ["Star", "Orbital", "Crushie Space", "Trade"];
var sp_debris = ["Ship Debris", "Station Debris", "Unknown Objects"];
var sp_planets = ["Vibrant", "Exciting", "Calm", "Moodyweather", "Relaxy"]; 