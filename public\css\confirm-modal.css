.confirm-modal .modal-content {
  border: 2px solid #ff6b6b;
  border-radius: 5px;
  background: #fff9e6;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
}

.confirm-modal .modal-header {
  background: #ff6b6b;
  background-image: url('../images/ui/bg/modal_header.png');
  border-bottom: 2px solid #d63030;
}

.confirm-modal .modal-title {
  color: white;
  font-weight: bold;
  font-size: 20px;
  text-shadow: -1px 1px 0 #d63030;
}

.confirm-modal .modal-body {
  padding: 20px;
  font-size: 16px;
  text-align: center;
}

.confirm-modal .modal-footer {
  border-top: none;
  justify-content: space-between;
  padding: 10px 20px 20px;
}

.confirm-modal .btn-danger {
  background-color: #ff6b6b;
  border-color: #d63030;
  color: white;
  font-weight: bold;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.confirm-modal .btn-danger:hover {
  background-color: #d63030;
  transform: scale(1.05);
}

.confirm-modal .btn-secondary {
  background-color: #6c757d;
  border-color: #5a6268;
  color: white;
  font-weight: bold;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.confirm-modal .btn-secondary:hover {
  background-color: #5a6268;
  transform: scale(1.05);
}

.confirm-modal .modal-body::before {
  content: "⚠️";
  display: block;
  font-size: 32px;
  margin-bottom: 15px;
}
