class UIService {
  static showAuthenticatedUI(userAccount) {
    // Show player containers
    $('#playerCountersContainer, #player-log-list').show();

    // Add loading spinner
    $('#player-log-list').append('<div class="spinner-border text-primary" role="status"><span class="sr-only"></span></div>');
 
    document.querySelectorAll('.cover').forEach(cover => {
      cover.parentNode.removeChild(cover);
    });

    // Update login button
    const loginButton = document.getElementById('loginButton');
    const imgElement = loginButton.querySelector('img');
    loginButton.innerHTML = `${imgElement.outerHTML} ${userAccount}`;
    $('#loginButton').css('font-weight', 'bold'); 
    try {
      if (typeof AudioManager !== 'undefined') {
        AudioManager.playUISound('dooropen'); 
      } else {
        console.error('[UI DEBUG] AudioManager is not defined when trying to play dooropen sound');
      }
    } catch (error) {
      console.error('[UI DEBUG] Error playing dooropen sound:', error);
    }
  }
}
