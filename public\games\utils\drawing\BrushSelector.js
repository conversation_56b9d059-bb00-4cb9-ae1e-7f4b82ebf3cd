// BrushSelector.js - Brush size selection tool for DrawingCanvas
class BrushSelector {
    constructor(container, options = {}) {
        this.container = container;
        this.onBrushChange = options.onBrushChange || (() => {});
        this.minBrush = 1;
        this.maxBrush = 20;
        this.brushSize = options.defaultBrushSize || 3;
        this.trackWidth = options.trackWidth || 80;
        this.createBrushSlider();
    }

    createBrushSlider() {
        this.brushContainer = new PIXI.Container();
        this.container.addChild(this.brushContainer);

        // Track
        this.track = new PIXI.Graphics();
        this.track.beginFill(0xCCCCCC);
        this.track.drawRoundedRect(0, 0, this.trackWidth, 12, 6);
        this.track.endFill();
        this.track.lineStyle(1, 0x666666, 1);
        this.track.drawRoundedRect(0, 0, this.trackWidth, 12, 6);
        this.brushContainer.addChild(this.track);

        // Handle
        this.handle = new PIXI.Graphics();
        this.handle.beginFill(0x4A90E2);
        this.handle.drawCircle(0, 6, 7);
        this.handle.endFill();
        this.handle.lineStyle(1, 0x2E5C8A, 1);
        this.handle.drawCircle(0, 6, 7);
        this.handle.x = this.getHandleX();
        this.handle.y = 0;
        this.handle.interactive = true;
        this.handle.cursor = 'pointer';
        this.handle.buttonMode = true;
        this.handle.on('pointerdown', this.onHandleDown.bind(this));
        this.handle.on('pointerup', this.onHandleUp.bind(this));
        this.handle.on('pointerupoutside', this.onHandleUp.bind(this));
        this.handle.on('pointermove', this.onHandleMove.bind(this));
        this.brushContainer.addChild(this.handle);

        // Value label
        this.valueLabel = new PIXI.Text(`${this.brushSize}`, {
            fontFamily: 'Arial',
            fontSize: 8,
            fill: 0x666666
        });
        this.valueLabel.x = this.trackWidth / 2;
        this.valueLabel.y = -10;
        this.valueLabel.anchor = new PIXI.Point(0.5, 0);
        this.brushContainer.addChild(this.valueLabel);

        // Brush label (centered)
        this.brushLabel = new PIXI.Text('Brush', {
            fontFamily: 'Arial',
            fontSize: 10,
            fill: 0x333333
        });
        this.brushLabel.anchor = new PIXI.Point(0.5, 0);
        this.brushLabel.x = this.trackWidth / 2;
        this.brushLabel.y = 22;
        this.brushContainer.addChild(this.brushLabel);
    }

    getHandleX() {
        const normalizedValue = (this.brushSize - this.minBrush) / (this.maxBrush - this.minBrush);
        return normalizedValue * this.trackWidth;
    }

    getBrushFromX(x) {
        const clampedX = Math.max(0, Math.min(this.trackWidth, x));
        const normalizedValue = clampedX / this.trackWidth;
        return Math.round(this.minBrush + (normalizedValue * (this.maxBrush - this.minBrush)));
    }

    onHandleDown(event) {
        this.isDragging = true;
        this.dragStartX = event.data.global.x;
        this.dragStartHandleX = this.handle.x;
    }

    onHandleUp(event) {
        this.isDragging = false;
    }

    onHandleMove(event) {
        if (!this.isDragging) return;
        const deltaX = event.data.global.x - this.dragStartX;
        const newX = this.dragStartHandleX + deltaX;
        this.setHandlePosition(newX);
    }

    setHandlePosition(x) {
        const newBrush = this.getBrushFromX(x);
        this.setBrushSize(newBrush);
    }

    setBrushSize(value) {
        this.brushSize = Math.max(this.minBrush, Math.min(this.maxBrush, value));
        this.handle.x = this.getHandleX();
        this.valueLabel.text = `${this.brushSize}`;
        this.onBrushChange('custom', this.brushSize);
    }

    getSelectedBrush() {
        return {
            type: 'custom',
            size: this.brushSize
        };
    }

    getBrushSize() {
        return this.brushSize;
    }
}

window.BrushSelector = BrushSelector; 