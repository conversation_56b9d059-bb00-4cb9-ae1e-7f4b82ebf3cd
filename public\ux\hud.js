
var currentOrder = 0;
function rotateHUD() {
var columnOrder = [
  ['#column-1', '#column-2', '#column-3'],
  ['#column-2', '#column-3', '#column-1'],
  ['#column-3', '#column-1', '#column-2']
];
  var cols = columnOrder[currentOrder];
  $('#column-1').insertAfter(cols[2]);
  $('#column-2').insertAfter(cols[0]);
  $('#column-3').insertAfter(cols[1]);
  currentOrder++;
  if(currentOrder >= columnOrder.length) {
    currentOrder = 0;
  }
}

function changeLogoSize() {
  var logo = $('#logoImage');
  var currentWidth = logo.width();
  var newWidth = (currentWidth === 64) ? 128 : 64;
  logo.css({ width: newWidth });
}

function updateTabCounters(teams, adventures) {
  $('#teams-counter').text(teams);
  $('#adventures-counter').text(adventures);
}


async function updatePlayerCounters(player, teamData, adventuresData) {
  player.teamsNapping = 0;
  player.teamsReady = 0;
  player.rewardsClaimable = 0;
  player.adventuresInProgress = 0;
  teamData.forEach(team => {
    if (team.status === 'Napping') {
      player.teamsNapping++;
    }
    if (team.status === 'Ready') {
      player.teamsReady++;
    }
  });
  adventuresData.forEach(adventure => {
    if (adventure.status === 'Complete') {
      player.rewardsClaimable++;
    }
    if (adventure.status === 'In Progress') {
      player.adventuresInProgress++;
    }
  });
  updateTabCounters(teamData.length, player.adventuresInProgress);
  $('#playerCountersContainer ul li:nth-child(1) span').text(`${player.rewardsClaimable} Rewards`);
  $('#playerCountersContainer ul li:nth-child(2) span').text(`${player.teamsReady} Teams Ready!`);
  $('#playerCountersContainer ul li:nth-child(3) span').text(`${player.teamsNapping} Napping`);
}

async function displayBalanceIcons(type, value, imagePath) {
  if (value === 0) {
    return '~';
  }
var title_text = "Try reloading. An error occurred.";

if(type==='GXP'){
title_text="GXP is used to unlock zones.";
} else if(type==='Credits'){
title_text="Credits are used to play mini games.";
}else if(type==='Nectar'){
title_text="Nectar is used to refuel vehicles for adventures.";
}

  if (value > 10) {
    return `<img src="${imagePath}" width="12px" alt="${type}" title="${title_text}"> ${value}`;
  }
  let imageTags = '';
  for (let i = 0; i < value; i++) {
    imageTags += `<img src="${imagePath}" width="12px" alt="${type}" title="${title_text}">`;
  }
  return imageTags;
}

function changeTileOptions() {
  var selectedWorld = nav.world;
  var worldDropdowns = document.querySelectorAll('[id^="tileOptionsWorld"]');

  for (var i = 0; i < worldDropdowns.length; i++) {
    var dropdown = worldDropdowns[i];
    if (dropdown.id === 'tileOptionsWorld' + selectedWorld) {
      dropdown.style.display = 'block';
    } else {
      dropdown.style.display = 'none';
    }
  }
}


  async function showStats() {
      const statsList = document.getElementById('player-stats-list');
      const logList = document.getElementById('player-log-list');
      const journalButton = document.getElementById('journal-button');
      const statsButton = document.getElementById('stats-button');

      // Fetch player settings data if not already loaded
      if (statsList.innerHTML === '') {
          const settings = await getPlayerSettings(wax.userAccount);

          // Only show the four main stats with icons and bonus/max images
          const statConfig = [
            {
              key: 'nectar_max',
              label: 'Nectar Max',
              icon: 'images/ui/nectar_icon.png',
              type: 'max',
            },
            {
              key: 'credits_max',
              label: 'Credits Max',
              icon: 'images/ui/credits_icon.png',
              type: 'max',
            },
            {
              key: 'nap_rate_bonus',
              label: 'Nap Rate Bonus',
              icon: 'images/ui/bed_icon.png',
              type: 'bonus',
            },
            {
              key: 'adventure_gxp_bonus',
              label: 'Adventure GXP Bonus',
              icon: 'images/ui/gxp_icon.png',
              type: 'bonus',
            },
          ];

          let settingsHTML = '<ul style="list-style:none;padding-left:0;">';
          for (const stat of statConfig) {
            const value = settings[stat.key] !== undefined ? settings[stat.key] : '-';
            const iconImg = `<img src='${stat.icon}' width='12' style='vertical-align:middle;margin-right:2px;'>`;
            const rightImg = stat.type === 'bonus'
              ? `<img src='images/ui/bonus.png' style='height:12px;vertical-align:middle;margin-left:0;margin-right:6px;'>`
              : `<img src='images/ui/max.png' style='height:12px;vertical-align:middle;margin-left:0;margin-right:6px;'>`;
            const label = `<span style='font-weight:bold;'>${stat.label}:</span>`;
            const valueSpan = `<span style='margin-left:6px;'>${value}</span>`;
            settingsHTML += `<li class='stat-row'>${iconImg}${rightImg}${label}${valueSpan}</li>`;
          }
          settingsHTML += '</ul>';
          statsList.innerHTML = settingsHTML;
      }

      // Show stats, hide journal
      statsList.style.display = 'block';
      logList.style.display = 'none';

      // Update button states
      statsButton.classList.add('active-tab');
      journalButton.classList.remove('active-tab');
  }

  function showJournal() {
      const statsList = document.getElementById('player-stats-list');
      const logList = document.getElementById('player-log-list');
      const journalButton = document.getElementById('journal-button');
      const statsButton = document.getElementById('stats-button');

      // Show journal, hide stats
      logList.style.display = 'block';
      statsList.style.display = 'none';

      // Update button states
      journalButton.classList.add('active-tab');
      statsButton.classList.remove('active-tab');
  }
