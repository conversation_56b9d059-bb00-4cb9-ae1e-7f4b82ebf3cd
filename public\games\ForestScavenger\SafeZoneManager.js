class ForestScavengerSafeZoneManager {
  constructor(game) {
    this.game = game;
    this.safeZones = [];
    this.sprites = [];
    this.sparkleTimeouts = []; // Track sparkle timeouts for cleanup
    this.activePulseEffects = new Map(); // Track which safe zones have active pulse effects
  }

  init() {
    this.safeZones = [];
    this.sprites = [];
    this.activePulseEffects.clear(); // Clear pulse tracking
    this.cleanupSparkles(); // Clean up any existing sparkle cycles
    
    const levelConfig = this.game.levelConfigs[this.game.level] || this.game.levelConfigs[1];
    const numSafeZones = levelConfig.safeZones;
    
    this.createSafeZones(numSafeZones);
  }

  cleanupSparkles() {
    // Clear all existing sparkle timeouts
    this.sparkleTimeouts.forEach(timeoutId => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    });
    this.sparkleTimeouts = [];
  }

  createSafeZones(numSafeZones) {
    let attempts = 0;
    const maxAttempts = 100;
    
    while (this.safeZones.length < numSafeZones && attempts < maxAttempts) {
      let x = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
      let y = Math.floor(Math.random() * (this.game.GRID_SIZE - 2)) + 1;
      
      // Check if position is valid (not house, not occupied, not too close to other safe zones)
      if (this.game.gridManager.isWalkable(x, y) && 
          !this.game.gridManager.isOccupied(x, y, true, true, true) &&
          !this.isTooCloseToOtherSafeZones(x, y)) {
        
        this.safeZones.push({ x, y });
        this.game.gridManager.setTile(x, y, 'safeZone');
      }
      attempts++;
    }
  }

  isTooCloseToOtherSafeZones(x, y) {
    const minDistance = 3; // Minimum distance between safe zones
    
    for (let zone of this.safeZones) {
      const distance = this.game.gridManager.getDistance({ x, y }, zone);
      if (distance < minDistance) {
        return true;
      }
    }
    
    return false;
  }

  drawSafeZones() {
    // Store which safe zones had active pulse effects before redrawing
    const zonesWithPulses = new Map();
    this.safeZones.forEach((zone, index) => {
      const sprite = this.sprites[index];
      if (sprite && this.activePulseEffects.has(sprite)) {
        zonesWithPulses.set(`${zone.x},${zone.y}`, {
          pulseData: this.activePulseEffects.get(sprite)
        });
      }
    });
    
    // Remove old sprites
    this.sprites.forEach(sprite => {
      if (sprite && sprite.parent) {
        sprite.parent.removeChild(sprite);
      }
    });
    this.sprites = [];
    this.activePulseEffects.clear(); // Clear pulse tracking
    
    // Clean up existing sparkle cycles before creating new ones
    this.cleanupSparkles();
    
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    
    // Draw safe zones as highlighted grass with glow effect
    this.safeZones.forEach(zone => {
      // Create a glowing background for the safe zone
      const glowSprite = new PIXI.Graphics();
      glowSprite.beginFill(0x00FF00, 0.3); // Semi-transparent green
      glowSprite.lineStyle(2, 0x00FF00, 0.8); // Green border
      glowSprite.drawRoundedRect(-scaledTileSize/2, -scaledTileSize/2, scaledTileSize, scaledTileSize, 4);
      glowSprite.endFill();
      
      glowSprite.x = zone.x * scaledTileSize + scaledTileSize/2 + gridOffsetX;
      glowSprite.y = zone.y * scaledTileSize + this.game.hudHeight + scaledTileSize/2 + gridOffsetY;
      
      this.game.app.stage.addChild(glowSprite);
      this.sprites.push(glowSprite);
      
      // Check if this safe zone had an active pulse effect and restore it
      const zoneKey = `${zone.x},${zone.y}`;
      if (zonesWithPulses.has(zoneKey)) {
        const pulseData = zonesWithPulses.get(zoneKey);
        this.addSafeZonePulseEffect(glowSprite, pulseData);
      } else {
        // Add pulsing effect to safe zones
        this.addSafeZonePulseEffect(glowSprite);
      }
      
      // Add sparkle effects periodically (only if game is still active)
      if (!this.game.gameOver && !this.game.win) {
        this.addSafeZoneSparkles(zone);
      }
    });
  }

  addSafeZonePulseEffect(sprite, existingPulseData = null) {
    if (window.VisualEffects && window.VisualEffects.createPulseEffect) {
      // If we have existing pulse data, use it to maintain animation continuity
      if (existingPulseData) {
        // Restore the pulse effect with the same timing
        window.VisualEffects.createPulseEffect(sprite, {
          pulseRate: 1.5,
          intensity: 0.2,
          continuous: true,
          startTime: existingPulseData.startTime // Use original start time to maintain phase
        });
      } else {
        // Create new pulse effect
        window.VisualEffects.createPulseEffect(sprite, {
          pulseRate: 1.5,
          intensity: 0.2,
          continuous: true
        });
      }
      
      // Track this sprite as having an active pulse effect
      this.activePulseEffects.set(sprite, {
        startTime: existingPulseData ? existingPulseData.startTime : Date.now()
      });
    }
  }

  addSafeZoneSparkles(zone) {
    const addSparkles = () => {
      // Check if game is still active and safe zones are still being drawn
      if (this.game.gameOver || this.game.win || 
          !this.game.app.stage.children.includes(this.sprites[0]) ||
          !this.safeZones.some(sz => sz.x === zone.x && sz.y === zone.y)) {
        return; // Stop sparkle cycle if game ended or safe zone removed
      }
      
      const scale = this.game.scale || 1;
      const scaledTileSize = this.game.TILE_SIZE * scale;
      const gridOffsetX = this.game.gridOffsetX || 0;
      const gridOffsetY = this.game.gridOffsetY || 0;
      const centerX = zone.x * scaledTileSize + scaledTileSize / 2 + gridOffsetX;
      const centerY = zone.y * scaledTileSize + this.game.hudHeight + scaledTileSize / 2 + gridOffsetY;
      
      if (window.VisualEffects && window.VisualEffects.createSparkle) {
        // Add 2-3 sparkles around the safe zone
        for (let i = 0; i < 3; i++) {
          setTimeout(() => {
            // Double-check game is still active before creating sparkle
            if (!this.game.gameOver && !this.game.win) {
              const angle = (Math.PI * 2 * i) / 3;
              const sparkleX = centerX + Math.cos(angle) * 20;
              const sparkleY = centerY + Math.sin(angle) * 20;
              window.VisualEffects.createSparkle(sparkleX, sparkleY, this.game.app.stage, 0x00FF00);
            }
          }, i * 300);
        }
      }
      
      // Schedule next sparkle burst and track the timeout
      const nextTimeout = setTimeout(addSparkles, 4000 + Math.random() * 2000);
      this.sparkleTimeouts.push(nextTimeout);
    };
    
    // Start the sparkle cycle and track the initial timeout
    const initialTimeout = setTimeout(addSparkles, 2000 + Math.random() * 1000);
    this.sparkleTimeouts.push(initialTimeout);
  }

  isInSafeZone(x, y) {
    return this.safeZones.some(zone => zone.x === x && zone.y === y);
  }

  onPlayerEnterSafeZone() {
    // Stop goblin pursuit
    this.game.goblinManager.stopPursuit();
    
    // Add visual effect to indicate safety
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    const centerX = this.game.playerManager.player.x * scaledTileSize + scaledTileSize / 2 + gridOffsetX;
    const centerY = this.game.playerManager.player.y * scaledTileSize + this.game.hudHeight + scaledTileSize / 2 + gridOffsetY;
    
    if (window.VisualEffects) {
      // Create a green ripple effect
      window.VisualEffects.createRipple(centerX, centerY, this.game.app.stage, 0x00FF00);
      
      // Add multiple green sparkles
      for (let i = 0; i < 6; i++) {
        setTimeout(() => {
          const angle = (Math.PI * 2 * i) / 6;
          const sparkleX = centerX + Math.cos(angle) * 25;
          const sparkleY = centerY + Math.sin(angle) * 25;
          window.VisualEffects.createSparkle(sparkleX, sparkleY, this.game.app.stage, 0x00FF00);
        }, i * 100);
      }
    }
  }

  onPlayerExitSafeZone() {
    // Resume goblin pursuit
    this.game.goblinManager.resumePursuit();
    
    // Add visual effect to indicate danger
    const scale = this.game.scale || 1;
    const scaledTileSize = this.game.TILE_SIZE * scale;
    const gridOffsetX = this.game.gridOffsetX || 0;
    const gridOffsetY = this.game.gridOffsetY || 0;
    const centerX = this.game.playerManager.player.x * scaledTileSize + scaledTileSize / 2 + gridOffsetX;
    const centerY = this.game.playerManager.player.y * scaledTileSize + this.game.hudHeight + scaledTileSize / 2 + gridOffsetY;
    
    if (window.VisualEffects) {
      // Create an orange warning ripple
      window.VisualEffects.createRipple(centerX, centerY, this.game.app.stage, 0xFFA500);
      
      // Add warning sparkles
      for (let i = 0; i < 4; i++) {
        setTimeout(() => {
          const angle = (Math.PI * 2 * i) / 4;
          const sparkleX = centerX + Math.cos(angle) * 20;
          const sparkleY = centerY + Math.sin(angle) * 20;
          window.VisualEffects.createSparkle(sparkleX, sparkleY, this.game.app.stage, 0xFFA500);
        }, i * 150);
      }
    }
  }

  getSafeZoneCount() {
    return this.safeZones.length;
  }

  getSafeZonePositions() {
    return this.safeZones.map(zone => ({ x: zone.x, y: zone.y }));
  }
}

window.ForestScavengerSafeZoneManager = ForestScavengerSafeZoneManager; 