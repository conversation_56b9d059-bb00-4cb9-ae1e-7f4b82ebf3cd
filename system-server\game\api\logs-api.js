const axios = require("axios");
const { app_url } = require("../../config");

async function removeGameLogs() {
  console.log("Attempting to remove logs!");
  const url = `${app_url}/players/logs/`;

  try {
    const response = await axios.delete(url, {
      headers: { "Content-Type": "application/json" },
    });

    // Axios wraps the response in a 'data' property
    console.log('Success:', response.data);
    return response.data;
  } catch (error) { 
    if (error.response) { 
      console.error(`HTTP error! Status: ${error.response.status}`, error.response.data);
      throw new Error(error.response.data.error || 'Failed to delete logs');
    } else if (error.request) { 
      console.error('No response received:', error.request);
      throw new Error('Network error - no response from server');
    } else { 
      console.error('Request setup error:', error.message);
      throw error;
    }
  }
}
async function logAdventureCompletion(id, team_id, owner_id, teamName, locationData) {
  try {
    const logData = {
      desc: `Team ${teamName} (ID: ${team_id}) completed adventure ${id}!`,
      adventure_id: id,
      team_id: team_id,
      location: locationData
    };

    await axios.post(
      `${app_url}/players/logs/`,
      {
        wax_id: owner_id,
        status: "new",
        type: "adventure_complete",
        data: JSON.stringify(logData)
      },
      {
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  } catch (error) {
    // Silently fail if logging fails
  }
}
module.exports = {
  removeGameLogs, logAdventureCompletion
};
