class LaserDefenderPhysicsManager {
    constructor(game) {
        this.game = game;
        this.engine = null;
        this.world = null;
        this.physicsRunning = false;
    }

    setupPhysics() {
        this.engine = Matter.Engine.create({
            gravity: { x: 0, y: 0.1 }  
        });
        this.world = this.engine.world;
        this.physicsRunning = true;
        this.runPhysics();
        this.checkCollisions();
    }

    runPhysics() {
        if (!this.physicsRunning) return;
        Matter.Engine.update(this.engine, 1000 / 60);
        this.game.enemyManager.enemies.forEach((enemy, index) => {
            if (enemy && this.game.enemyManager.enemyBodies[index]) {
                const body = this.game.enemyManager.enemyBodies[index];
                enemy.x = body.position.x;
                enemy.y = body.position.y;
                enemy.rotation = body.angle;
            }
        });
        this.game.projectileManager.playerProjectiles.forEach((projectile, index) => {
            if (projectile && this.game.projectileManager.projectileBodies[index]) {
                const body = this.game.projectileManager.projectileBodies[index];
                projectile.x = body.position.x;
                projectile.y = body.position.y;
            }
        });
        this.game.projectileManager.enemyProjectiles.forEach((projectile, index) => {
            if (projectile && this.game.projectileManager.enemyProjectileBodies[index]) {
                const body = this.game.projectileManager.enemyProjectileBodies[index];
                projectile.x = body.position.x;
                projectile.y = body.position.y;
            }
        });
        if (this.physicsRunning) {
            requestAnimationFrame(() => this.runPhysics());
        }
    }

    checkCollisions() {
        Matter.Events.on(this.engine, 'collisionStart', (event) => {
            event.pairs.forEach((pair) => {
                const bodyA = pair.bodyA;
                const bodyB = pair.bodyB;
                if ((bodyA.label === 'playerProjectile' && bodyB.label.includes('asteroid')) ||
                    (bodyB.label === 'playerProjectile' && bodyA.label.includes('asteroid'))) {
                    const projectile = bodyA.label === 'playerProjectile' ? bodyA : bodyB;
                    const enemy = bodyA.label === 'playerProjectile' ? bodyB : bodyA;
                    this.handleProjectileEnemyCollision(projectile, enemy);
                }
                if ((bodyA.label === 'playerProjectile' && ['shooter', 'shielded', 'boss'].includes(bodyB.label)) ||
                    (bodyB.label === 'playerProjectile' && ['shooter', 'shielded', 'boss'].includes(bodyA.label))) {
                    const projectile = bodyA.label === 'playerProjectile' ? bodyA : bodyB;
                    const enemy = bodyA.label === 'playerProjectile' ? bodyB : bodyA;
                    this.handleProjectileEnemyCollision(projectile, enemy);
                }
                if ((bodyA.label === 'enemyProjectile' && bodyB.label === 'turret') ||
                    (bodyB.label === 'enemyProjectile' && bodyA.label === 'turret')) {
                    if (!this.game.powerupManager.activePowerups.shield.active) {
                        this.handleEnemyProjectileTurretCollision();
                    }
                }
                if ((bodyA.label === 'asteroid' && bodyB.label === 'turret') ||
                    (bodyB.label === 'asteroid' && bodyA.label === 'turret')) {
                    const asteroidBody = bodyA.label === 'asteroid' ? bodyA : bodyB;
                    this.handleAsteroidTurretCollision(asteroidBody);
                }
                if ((['shooter', 'shielded', 'boss'].includes(bodyA.label) && bodyB.label === 'turret') ||
                    (['shooter', 'shielded', 'boss'].includes(bodyB.label) && bodyA.label === 'turret')) {
                    const npcBody = ['shooter', 'shielded', 'boss'].includes(bodyA.label) ? bodyA : bodyB;
                    this.handleAsteroidTurretCollision(npcBody);
                }
                if (['asteroid', 'shooter', 'shielded', 'boss'].includes(bodyA.label) && 
                    ['asteroid', 'shooter', 'shielded', 'boss'].includes(bodyB.label)) {
                    this.handleEnemyEnemyCollision(bodyA, bodyB);
                }
            });
        });
    }
    
    handleProjectileEnemyCollision(projectile, enemy) {
        const projectileIndex = this.game.projectileManager.projectileBodies.indexOf(projectile);
        const enemyIndex = this.game.enemyManager.enemyBodies.indexOf(enemy);
        if (projectileIndex !== -1 && enemyIndex !== -1) {
            const projectileSprite = this.game.projectileManager.playerProjectiles[projectileIndex];
            const enemySprite = this.game.enemyManager.enemies[enemyIndex];
            this.game.app.stage.removeChild(projectileSprite);
            this.game.projectileManager.playerProjectiles.splice(projectileIndex, 1);
            Matter.World.remove(this.world, projectile);
            this.game.projectileManager.projectileBodies.splice(projectileIndex, 1);
            enemySprite.health--;
            if (window.VisualEffects) {
                window.VisualEffects.createWhiteBlink(enemySprite, 120);
            }
            if (enemySprite.health <= 0) {
                this.game.score += enemySprite.score;
                this.game.powerupProgress += 5;
                this.game.app.stage.removeChild(enemySprite);
                this.game.enemyManager.enemies.splice(enemyIndex, 1);
                Matter.World.remove(this.world, enemy);
                this.game.enemyManager.enemyBodies.splice(enemyIndex, 1);
                if (window.VisualEffects) {
                    window.VisualEffects.createExplosion(enemySprite.x, enemySprite.y, 0xFF0000, 10, this.game.app.stage);
                    window.VisualEffects.createScorePopup(enemySprite.x, enemySprite.y, enemySprite.score, this.game.app.stage, 0x00FF00);
                }
                if (window.GameSounds) {
                    window.GameSounds.playEnemyHit();
                }
            }
        }
    }
    
    handleEnemyProjectileTurretCollision() {
        const projectileIndex = this.game.projectileManager.enemyProjectileBodies.findIndex(body => 
            body.label === 'enemyProjectile'
        );
        if (projectileIndex !== -1) {
            const projectileSprite = this.game.projectileManager.enemyProjectiles[projectileIndex];
            const projectileBody = this.game.projectileManager.enemyProjectileBodies[projectileIndex];
            this.game.playerHealth -= 10;
            this.game.app.stage.removeChild(projectileSprite);
            this.game.projectileManager.enemyProjectiles.splice(projectileIndex, 1);
            Matter.World.remove(this.world, projectileBody);
            this.game.projectileManager.enemyProjectileBodies.splice(projectileIndex, 1);
            if (window.VisualEffects) {
                window.VisualEffects.createExplosion(this.game.turretManager.turret.x, this.game.turretManager.turret.y, 0xFF0000, 5, this.game.app.stage);
            }
        }
    }
    
    handleAsteroidTurretCollision(body) {
        this.game.playerHealth -= 15;
        if (body) {
            const enemyIndex = this.game.enemyManager.enemyBodies.indexOf(body);
            if (enemyIndex !== -1) {
                const enemySprite = this.game.enemyManager.enemies[enemyIndex];
                this.game.app.stage.removeChild(enemySprite);
                this.game.enemyManager.enemies.splice(enemyIndex, 1);
                Matter.World.remove(this.world, body);
                this.game.enemyManager.enemyBodies.splice(enemyIndex, 1);
            }
        }
        if (window.VisualEffects) {
            window.VisualEffects.createExplosion(this.game.turretManager.turret.x, this.game.turretManager.turret.y, 0xFF6600, 8, this.game.app.stage);
        }
    }
    
    handleEnemyEnemyCollision(bodyA, bodyB) {
        // Add a small repulsive force to separate overlapping enemies
        const dx = bodyB.position.x - bodyA.position.x;
        const dy = bodyB.position.y - bodyA.position.y;
        const dist = Math.sqrt(dx * dx + dy * dy) || 1;
        const force = 0.002; // Small force
        const fx = (dx / dist) * force;
        const fy = (dy / dist) * force;
        Matter.Body.applyForce(bodyA, bodyA.position, { x: -fx, y: -fy });
        Matter.Body.applyForce(bodyB, bodyB.position, { x: fx, y: fy });
    }

    cleanup() {
        this.physicsRunning = false;
        this.game.enemyManager.enemyBodies.forEach(body => {
            if (body) Matter.World.remove(this.world, body);
        });
        this.game.projectileManager.projectileBodies.forEach(body => {
            if (body) Matter.World.remove(this.world, body);
        });
        this.game.projectileManager.enemyProjectileBodies.forEach(body => {
            if (body) Matter.World.remove(this.world, body);
        });
        if (this.game.turretManager.turretBody) {
            Matter.World.remove(this.world, this.game.turretManager.turretBody);
        }
        if (this.engine) {
            Matter.Engine.clear(this.engine);
        }
    }
}

window.LaserDefenderPhysicsManager = LaserDefenderPhysicsManager;