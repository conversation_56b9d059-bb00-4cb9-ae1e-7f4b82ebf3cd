// ORG to do: we need to remove any queries hard coded in controllers
// SECURITY to do: RLS row level security
// The user should only be able to update their wallet's rows
// create, update, delete teams and adventures
// sanitize inputs: prevent all injection attacks on client and backend
// only update rewards and update their player row
// they should NEVER be able to update their logs, delete rewards, delete player or create player

// const { body } = require("express-validator"); sql attacks prevention code needed
