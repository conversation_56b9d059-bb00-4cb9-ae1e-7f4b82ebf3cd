// HUD Configuration System - Centralized positioning and styling for mini-game HUD elements
// Usage: HUDConfig.getPosition('hud_med_position_1') or HUDConfig.createHudElement('resource_bar', options)

(function() {
    window.HUDConfig = {
        // Standardized HUD positions
        positions: {
            'hud_med_position_1': {
                x: 154,
                y: 50,
                background: 'images/games/hud/mg_hud_med.png',
                width: 148, // Approximate width of mg_hud_med.png
                height: 40, // Approximate height of mg_hud_med.png
                description: 'Medium HUD position 1 - used for resource bars, heart meters, etc.'
            },
            'hud_med_position_2': {
                x: 302,
                y: 50,
                background: 'images/games/hud/mg_hud_med.png',
                width: 148,
                height: 40,
                description: 'Medium HUD position 2 - used for resource bars, heart meters, etc.'
            },
            'hud_sm_position_1': {
                x: 458, // 454 + 4
                y: 53, // 50 + 3
                background: 'images/games/hud/mg_hud_sm.png',
                width: 46, // Approximate width of mg_hud_sm.png
                height: 40, // Approximate height of mg_hud_sm.png
                description: 'Small HUD position 1 - used for small elements like wind direction icon'
            },
            'hud_lg_position_1': {
                x: 119,
                y: 50,
                background: 'images/games/hud/mg_hud_lg.png',
                width: 183, // Approximate width of mg_hud_lg.png
                height: 40, // Approximate height of mg_hud_lg.png
                description: 'Large HUD position 1 - used for custom HUD elements that need more space'
            }
        },

        // Predefined HUD element configurations
        elements: {
            'resource_bar': {
                type: 'bar',
                defaultPosition: 'hud_med_position_1',
                defaultOptions: {
                    width: 140,
                    height: 18,
                    max: 100,
                    value: 100,
                    color: 0x39ff14,
                    underColor: 0xff2222
                }
            },
            'heart_meter': {
                type: 'hearts',
                defaultPosition: 'hud_med_position_2',
                defaultOptions: {
                    count: 5,
                    value: 5,
                    iconWidth: 7,
                    iconHeight: 7,
                    spacing: 4
                }
            },
            'powerup_bar': {
                type: 'bar',
                defaultPosition: 'hud_med_position_1',
                defaultOptions: {
                    width: 140,
                    height: 18,
                    max: 10,
                    value: 0,
                    color: 0x00FFFF,
                    underColor: 0x333333
                }
            },
            'wind_indicator': {
                type: 'custom',
                defaultPosition: 'hud_sm_position_1',
                defaultOptions: {
                    width: 32,
                    height: 32,
                    icon: 'images/games/hud/wind_icon.png'
                }
            },
            'material_list': {
                type: 'custom',
                defaultPosition: 'hud_lg_position_1',
                defaultOptions: {
                    title: 'COLLECT',
                    fontSize: 12,
                    lineHeight: 16,
                    textColor: 0xFFFFFF,
                    strokeColor: 0x000000,
                    strokeThickness: 2
                }
            },
            'collectibles': {
                type: 'collectibles',
                defaultPosition: 'hud_med_position_1',
                defaultOptions: {
                    layout: 'horizontal',
                    title: 'COLLECT',
                    showTitle: false,
                    fontSize: 10,
                    textColor: 0xFFFFFF,
                    strokeColor: 0x000000,
                    strokeThickness: 2,
                    iconSize: 12,
                    spacing: 6,
                    itemSpacing: 3,
                    titleSpacing: 8,
                    padding: { x: 8, y: 8 },
                    animateUpdates: true,
                    updateDuration: 300
                }
            }
        },

        // Get position configuration by name
        getPosition: function(positionName) {
            return this.positions[positionName] || null;
        },

        // Get element configuration by name
        getElement: function(elementName) {
            return this.elements[elementName] || null;
        },

        // Create a HUD element with background at a specific position
        createHudElement: function(elementName, positionName, customOptions = {}) {
            const elementConfig = this.getElement(elementName);
            const positionConfig = this.getPosition(positionName || elementConfig.defaultPosition);
            
            if (!elementConfig || !positionConfig) {
                console.error('HUDConfig: Invalid element or position name:', elementName, positionName);
                return null;
            }

            // Merge default options with custom options
            const options = Object.assign({}, elementConfig.defaultOptions, customOptions);
            
            // Create the HUD element
            let hudElement = null;
            if (elementConfig.type === 'bar') {
                hudElement = new ResourceBar(options);
            } else if (elementConfig.type === 'hearts') {
                hudElement = new HeartMeter(options);
            } else if (elementConfig.type === 'collectibles') {
                hudElement = new CollectList(options);
            } else if (elementConfig.type === 'custom') {
                hudElement = this.createCustomHudElement(options);
            }

            if (hudElement) {
                // Position the element
                hudElement.x = positionConfig.x;
                hudElement.y = positionConfig.y;
                
                // Add background if specified
                if (positionConfig.background) {
                    const background = PIXI.Sprite.from(positionConfig.background);
                    background.x = positionConfig.x;
                    background.y = positionConfig.y;
                    background.zIndex = -1; // Ensure background is behind the element
                    hudElement.background = background;
                }
            }

            return hudElement;
        },

        // Create a custom HUD element (like wind indicator or material list)
        createCustomHudElement: function(options) {
            const container = new PIXI.Container();
            
            if (options.icon) {
                const icon = PIXI.Sprite.from(options.icon);
                icon.width = options.width || 32;
                icon.height = options.height || 32;
                container.addChild(icon);
            }

            // Handle material list type
            if (options.title === 'COLLECT') {
                // Create a container for material list
                const materialContainer = new PIXI.Container();
                
                // Add title
                const titleStyle = new PIXI.TextStyle({
                    fontFamily: 'Arial',
                    fontSize: options.fontSize || 12,
                    fill: options.textColor || 0xFFFFFF,
                    stroke: options.strokeColor || 0x000000,
                    strokeThickness: options.strokeThickness || 2,
                    dropShadow: true,
                    dropShadowColor: 0x000000,
                    dropShadowBlur: 2,
                    dropShadowDistance: 1
                });
                
                const titleText = new PIXI.Text(options.title, titleStyle);
                titleText.x = 10;
                titleText.y = 5;
                materialContainer.addChild(titleText);
                
                // Store the container for later use
                container.materialContainer = materialContainer;
                container.addChild(materialContainer);
                
                // Add method to update material list
                container.updateMaterialList = function(materials) {
                    // Remove old material texts
                    materialContainer.children.forEach(child => {
                        if (child !== titleText) {
                            materialContainer.removeChild(child);
                        }
                    });
                    
                    // Add new material texts
                    let yOffset = 25; // Start below title
                    Object.keys(materials).forEach(materialType => {
                        if (materials[materialType] > 0) {
                            const text = `${materialType.toUpperCase()}: 0/${materials[materialType]}`;
                            const materialText = new PIXI.Text(text, titleStyle);
                            materialText.x = 10;
                            materialText.y = yOffset;
                            materialText.materialType = materialType; // Store for updates
                            materialContainer.addChild(materialText);
                            yOffset += options.lineHeight || 16;
                        }
                    });
                };
                
                // Add method to update individual material count
                container.updateMaterialCount = function(materialType, collected, required) {
                    materialContainer.children.forEach(child => {
                        if (child.materialType === materialType) {
                            child.text = `${materialType.toUpperCase()}: ${collected}/${required}`;
                        }
                    });
                };
            }

            return container;
        },

        // Add a HUD element to stage with proper layering
        addToStage: function(stage, hudElement) {
            if (!hudElement || !stage) return;

            // Add background first (if it exists)
            if (hudElement.background) {
                stage.addChild(hudElement.background);
            }
            
            // Add the main element
            stage.addChild(hudElement);
        },

        // Update position configurations (for future extensibility)
        updatePosition: function(positionName, newConfig) {
            this.positions[positionName] = Object.assign({}, this.positions[positionName], newConfig);
        },

        // Add new position configuration
        addPosition: function(positionName, config) {
            this.positions[positionName] = config;
        },

        // Add new element configuration
        addElement: function(elementName, config) {
            this.elements[elementName] = config;
        }
    };
})(); 