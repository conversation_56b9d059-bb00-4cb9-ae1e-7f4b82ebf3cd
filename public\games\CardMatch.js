class CardMatch extends BaseGame {
    constructor() {
        super();
        this.cards = [];
        this.selectedCards = [];
        this.matchedPairs = 0;
        this.canClick = true;
        this.level = 1;
        this.images = [];
        this.cardFrontTexture = null;
        this.cardBackTexture = null;
        this.gxpAwarded = false;
    }

    async loadImages() {
        try {
            this.cardFrontTexture = PIXI.Texture.from('images/games/fruit/card-front.png');
            this.cardBackTexture = PIXI.Texture.from('images/games/fruit/card-back.png');

            // Progressive image tiers - more variety as levels increase
            const imageTiers = {
                tier1: [ // Basic fruits for early levels
                    'images/games/fruit/apple.png',
                    'images/games/fruit/bananas.png',
                    'images/games/fruit/strawberry.png',
                    'images/games/fruit/peach.png',
                    'images/games/fruit/blueberry.png',
                    'images/games/fruit/raspberry.png'
                ],
                tier2: [ // Add more fruits and candies
                    'images/games/fruit/blackberry.png',
                    'images/games/fruit/green_apple.png',
                    'images/games/fruit/candy1.png',
                    'images/games/fruit/candy2.png',
                    'images/games/fruit/candy3.png',
                    'images/games/fruit/mocha3.png',
                    'images/games/fruit/plum.png'
                ],
                tier3: [ // Add food items
                    'images/games/fruit/taco.png',
                    'images/games/fruit/toast.png',
                    'images/games/fruit/salad.png',
                    'images/games/fruit/potatochips.png',
                    'images/games/fruit/poofychips.png'
                ],
                tier4: [ // Add desserts and special items
                    'images/games/fruit/starwberrycupcake.png',
                    'images/games/fruit/pumpkinspicecupcake.png',
                    'images/games/fruit/spacefood.png',
                    'images/games/fruit/Salmon.png',
                    'images/games/fruit/Tofu.png',
                    'images/games/fruit/VegetableSoup.png'
                ]
            };

            this.imageTiers = imageTiers;
            this.images = [];
            this.imageTextures = {}; // Map paths to textures
            
            // Load all images from all tiers
            const allImages = [...imageTiers.tier1, ...imageTiers.tier2, ...imageTiers.tier3, ...imageTiers.tier4];
            
            for (let i = 0; i < allImages.length; i++) {
                try {
                    const texture = PIXI.Texture.from(allImages[i]);
                    this.images.push(texture);
                    this.imageTextures[allImages[i]] = texture;
                } catch (error) {
                    console.warn(`Failed to load image: ${allImages[i]}`, error);
                }
            }
        } catch (error) {
            throw error;
        }
    }

    createCard(texture, x, y) {
        const card = new PIXI.Container();
        card.x = x;
        card.y = y;

        const front = new PIXI.Sprite(this.cardFrontTexture);
        front.width = 80;
        front.height = 100;

        const back = new PIXI.Sprite(this.cardBackTexture);
        back.width = 80;
        back.height = 100;
        back.visible = false;

        const fruit = new PIXI.Sprite(texture);
        fruit.width = 80;
        fruit.height = 100;
        fruit.visible = false;

        card.addChild(front, back, fruit);

        card.interactive = true;
        card.buttonMode = true;
        card.texture = texture;
        card.front = front;
        card.back = back;
        card.fruit = fruit;

        card.on('pointerdown', () => this.onCardClick(card));

        return card;
    }

    onCardClick(card) {
        if (!this.canClick || card.fruit.visible || this.selectedCards.includes(card)) return;

        // Play card click sound
        if (window.GameSounds) {
            window.GameSounds.playCardClick();
        }

        // Add button click effect
        if (window.VisualEffects && typeof window.VisualEffects.createButtonClick === 'function') {
            try {
                window.VisualEffects.createButtonClick(card.x + 40, card.y + 50, this.app.stage);
            } catch (error) {
                console.warn('Button click effect failed:', error);
            }
        }

        // Add ripple effect
        if (window.VisualEffects && typeof window.VisualEffects.createRipple === 'function') {
            try {
                window.VisualEffects.createRipple(card.x + 40, card.y + 50, this.app.stage, 0x00FFFF);
            } catch (error) {
                console.warn('Ripple effect failed:', error);
            }
        }

        // Animate card flip
        this.animateCardFlip(card, () => {
            this.selectedCards.push(card);

            if (this.selectedCards.length === 2) {
                this.canClick = false;
                this.checkMatch();
            }
        });
    }

    animateCardFlip(card, onComplete) {
        if (window.VisualEffects && typeof window.VisualEffects.createCardFlip === 'function') {
            try {
                window.VisualEffects.createCardFlip(card, onComplete);
            } catch (error) {
                console.warn('Card flip effect failed:', error);
                // Fallback: simple flip
                card.front.visible = false;
                card.back.visible = true;
                card.fruit.visible = true;
                if (onComplete) onComplete();
            }
        } else {
            // Fallback: simple flip
            card.front.visible = false;
            card.back.visible = true;
            card.fruit.visible = true;
            if (onComplete) onComplete();
        }
    }

    animateCardFlipBack(card, onComplete) {
        if (window.VisualEffects && typeof window.VisualEffects.createCardFlipBack === 'function') {
            try {
                window.VisualEffects.createCardFlipBack(card, onComplete);
            } catch (error) {
                console.warn('Card flip back effect failed:', error);
                // Fallback: simple flip back
                card.front.visible = true;
                card.back.visible = false;
                card.fruit.visible = false;
                if (onComplete) onComplete();
            }
        } else {
            // Fallback: simple flip back
            card.front.visible = true;
            card.back.visible = false;
            card.fruit.visible = false;
            if (onComplete) onComplete();
        }
    }

    checkMatch() {
        const [card1, card2] = this.selectedCards;

        if (card1.texture === card2.texture) {
            this.matchedPairs++;
            this.score += 50;
            this.selectedCards = [];
            this.canClick = true;

            // Add sparkle effects if VisualEffects is available
            if (window.VisualEffects && typeof window.VisualEffects.createSparkle === 'function') {
                try {
                    window.VisualEffects.createSparkle(card1.x, card1.y, this.app.stage, 0x00FF00);
                    window.VisualEffects.createSparkle(card2.x, card2.y, this.app.stage, 0x00FF00);
                } catch (error) {
                    console.warn('VisualEffects.createSparkle failed:', error);
                    // Fallback: create simple sparkle effect
                    this.createSimpleSparkle(card1.x, card1.y, this.app.stage, 0x00FF00);
                    this.createSimpleSparkle(card2.x, card2.y, this.app.stage, 0x00FF00);
                }
            } else {
                // Fallback: create simple sparkle effect
                this.createSimpleSparkle(card1.x, card1.y, this.app.stage, 0x00FF00);
                this.createSimpleSparkle(card2.x, card2.y, this.app.stage, 0x00FF00);
            }

            if (this.matchedPairs === 6) {
                this.canClick = false;
                this.level++;
                
                // Pause the game during level up effect
                this.gameOver = true;
                
                // Create standardized level up effect with pause functionality
                if (window.VisualEffects && typeof window.VisualEffects.createLevelUpEffect === 'function') {
                    try {
                        window.VisualEffects.createLevelUpEffect(this.app.stage, this.level, () => {
                            // Resume game and start next level
                            this.gameOver = false;
                            this.nextLevel();
                        });
                    } catch (error) {
                        console.warn('VisualEffects.createLevelUpEffect failed:', error);
                        // Fallback: start next level immediately
                        this.gameOver = false;
                        this.nextLevel();
                    }
                } else {
                    // Fallback: start next level immediately
                    this.gameOver = false;
                    this.nextLevel();
                }
            }
        } else {
            // Animate cards flipping back after a delay
            setTimeout(() => {
                this.animateCardFlipBack(card1, () => {
                    // First card flipped back
                });
                
                this.animateCardFlipBack(card2, () => {
                    // Second card flipped back, now re-enable clicking
                    this.selectedCards = [];
                    this.canClick = true;
                });
            }, 1000);
        }
        this.updateHUD();
    }

    // Simple fallback sparkle effect
    createSimpleSparkle(x, y, stage, color = 0xFFFFFF) {
        try {
            const sparkle = new PIXI.Graphics();
            sparkle.beginFill(color);
            sparkle.drawStar(0, 0, 5, 3, 6);
            sparkle.endFill();
            
            sparkle.x = x;
            sparkle.y = y;
            sparkle.alpha = 1;
            sparkle.scale.set(0.5);
            
            stage.addChild(sparkle);
            
            // Simple animation
            let scale = 0.5;
            let alpha = 1;
            
            const animate = () => {
                scale += 0.1;
                alpha -= 0.05;
                
                sparkle.scale.set(scale);
                sparkle.alpha = alpha;
                
                if (alpha <= 0) {
                    stage.removeChild(sparkle);
                } else {
                    requestAnimationFrame(animate);
                }
            };
            
            animate();
        } catch (error) {
            console.warn('Simple sparkle effect failed:', error);
        }
    }

    async startGame() {
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }

        try {
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);
            await this.loadImages();
            this.resetGame();
            this.initGame(true);
            const startScreen = document.getElementById('start-screen');
            const gameArea = document.getElementById('game-area');
            const gameOverScreen = document.getElementById('game-over-screen');
            if (startScreen) startScreen.style.display = 'none';
            if (gameArea) gameArea.style.display = 'block';
            if (gameOverScreen) gameOverScreen.style.display = 'none';
            this.startCountdown();
        } catch (error) {
            showAlert("Failed to start game. Please try again.");
        }
    }

    onCountdownComplete() {
        this.nextLevel();
    }

    nextLevel() {
        if (this.level === 3 && !this.gxpAwarded) {
            this.gxpAwarded = true;
            if (typeof transactResource === 'function') {
                transactResource('gxp', 10, 'add', showAlert, updatePlayerBalances);
            }
            showAlert('Victory! You earned GXP for reaching level 3!');
        }
        
        // Clear the stage and reset game state for the next level
        this.resetGame();
        
        // Step 1: Randomly select 6 unique images from the loaded images
        const selectedImages = this.getRandomImages(6);
        // Step 2: Create pairs of cards using the selected images
        const allTextures = [...selectedImages, ...selectedImages]; // Each image is used twice
        // Step 3: Shuffle the card textures with increasing randomness based on level
        this.shuffleArray(allTextures, this.level);
        // Step 4: Position cards in a 4x3 grid (12 cards)
        for (let i = 0; i < 12; i++) {
            const x = (i % 4) * 100 + 60; // Fixed grid layout
            const y = Math.floor(i / 4) * 120 + this.hudHeight + 30; // Ensure cards are below HUD area (moved down 10px)
            const card = this.createCard(allTextures[i], x, y);
            this.cards.push(card);
            this.app.stage.addChild(card);
        }
        // Start game timer based on level
        this.startTimer();
    }

    shuffleArray(array, level) {
        const shuffleCount = level * 10;
        for (let i = 0; i < shuffleCount; i++) {
            const index1 = Math.floor(Math.random() * array.length);
            const index2 = Math.floor(Math.random() * array.length);
            [array[index1], array[index2]] = [array[index2], array[index1]];
        }
    }

    startTimer() {
        if (this.gameInterval) {
            clearInterval(this.gameInterval);
        }

        let timeLimit;
        if (this.level === 1) {
            timeLimit = 120;
        } else if (this.level >= 2 && this.level <= 3) {
            timeLimit = 90;
        } else if (this.level >= 4 && this.level <= 6) {
            timeLimit = 60;
        } else if (this.level >= 7 && this.level <= 9) {
            timeLimit = 45;
        } else {
            timeLimit = 30;
        }
        this.time = timeLimit;
        this.updateHUD();
        this.gameInterval = setInterval(() => {
            this.time--;
            if (this.time <= 0) {
                clearInterval(this.gameInterval);
                this.time = 0;
                this.updateHUD();
                this.endGame("Time's up!");
            }
            this.updateHUD();
        }, 1000);
    }

    resetGame() {
        this.cards.forEach(card => this.app.stage.removeChild(card));
        this.cards = [];
        this.selectedCards = [];
        this.matchedPairs = 0;
        this.canClick = true;
        this.time = 0;
        this.updateHUD();
    }

    getRandomImages(count) {
        // Determine which tiers to use based on current level
        let availablePaths = [];
        
        if (this.level <= 2) {
            // Levels 1-2: Only tier 1 (basic fruits)
            availablePaths = [...this.imageTiers.tier1];
        } else if (this.level <= 4) {
            // Levels 3-4: Tier 1 + Tier 2 (fruits + candies)
            availablePaths = [...this.imageTiers.tier1, ...this.imageTiers.tier2];
        } else if (this.level <= 6) {
            // Levels 5-6: Tier 1 + Tier 2 + Tier 3 (fruits + candies + food)
            availablePaths = [...this.imageTiers.tier1, ...this.imageTiers.tier2, ...this.imageTiers.tier3];
        } else {
            // Level 7+: All tiers (fruits + candies + food + desserts)
            availablePaths = [...this.imageTiers.tier1, ...this.imageTiers.tier2, ...this.imageTiers.tier3, ...this.imageTiers.tier4];
        }
        
        // Convert paths to textures and filter out any that failed to load
        const availableTextures = availablePaths
            .map(path => this.imageTextures[path])
            .filter(texture => texture !== undefined);
        
        // Shuffle and return the requested number of images
        const shuffledTextures = [...availableTextures];
        this.shuffleArray(shuffledTextures, this.level);
        return shuffledTextures.slice(0, count);
    }

    stopGame() {
        this.cards = [];
        this.selectedCards = [];
        this.matchedPairs = 0;
        super.stopGame();
    }
}