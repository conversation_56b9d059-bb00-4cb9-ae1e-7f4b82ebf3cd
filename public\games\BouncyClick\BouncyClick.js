class BouncyClick extends BaseGame {
    constructor() { 
        super();
        this.balls = [];
        this.colors = [0xFF0000, 0x0000FF, 0x00FF00];
        this.clickSpeed = 0;
        this.lastClickTime = 0;
        this.gxpAwarded = false;
        this.physicsManager = null;
        this.windEffect = null;
        this.maxHearts = 5;
        this.hearts = this.maxHearts;
        this.ballClickedThisFrame = false; 
        this.powerupProgress = 0;
        this.powerupTarget = 10; // 10 clicks to fill powerup bar
        this.activePowerupColor = null;
        this.powerupBlinkInterval = null;
        this.powerupBlinkState = false;
    }

    initGame(resetLevel = true) {
        super.initGame(resetLevel); 
        if (!this.heartMeter) {
            this.heartMeter = HUDConfig.createHudElement('heart_meter', 'hud_med_position_1', {
                count: this.maxHearts,
                value: this.hearts,
                fullIcon: 'images/games/hud/heart.png',
                emptyIcon: 'images/games/hud/heart-empty.png',
                iconWidth: 7,
                iconHeight: 7,
                spacing: 4
            });
        }
        HUDConfig.addToStage(this.app.stage, this.heartMeter);
        this.heartMeter.setCount(this.maxHearts);
        this.heartMeter.setValue(this.hearts);
        this.hearts = this.maxHearts; 
        if (!this.powerupBar) { 
            this.powerupBar = HUDConfig.createHudElement('powerup_bar', 'hud_med_position_2', {
                max: this.powerupTarget,
                value: this.powerupProgress,
                color: 0x00FFFF, // Cyan color for powerup
                underColor: 0x333333 // Dark gray for empty
            }); 
            this.powerupLabel = new PIXI.Text('POWERUP', {
                fontFamily: 'Arial',
                fontSize: 12,
                fill: 0xFFFFFF,
                stroke: 0x000000,
                strokeThickness: 2
            });
            this.powerupLabel.x = 16;
            this.powerupLabel.y = 52;
            this.powerupLabel.zIndex = 1000;
            this.app.stage.addChild(this.powerupLabel);
        }
        HUDConfig.addToStage(this.app.stage, this.powerupBar);
        this.powerupBar.visible = true;
        this.powerupBar.alpha = 1;
        this.powerupBar.ensureVisible();
        this.powerupBar.setValue(this.powerupProgress); 
        if (!this.windIndicator) {
            this.windIndicator = HUDConfig.createHudElement('wind_indicator', 'hud_sm_position_1', {
                width: 32,
                height: 32,
                icon: 'images/games/hud/wind_icon.png'
            });
        }
        HUDConfig.addToStage(this.app.stage, this.windIndicator); 
        this.physicsManager = new PhysicsManager(this.app, this.hudHeight);
        this.physicsManager.setupPhysics();
        this.windEffect = window.VisualEffects.createWindEffect(this.app.stage);
        this.app.stage.sortableChildren = true;
        
        // Ensure canvas click handler is removed during initialization
        if (this.canvasClickHandler) {
            this.app.view.removeEventListener('click', this.canvasClickHandler);
            this.canvasClickHandler = null;
        }
    }

    createBall(color) {
        let radius = Math.max(10, 25 - (this.level - 1) * 2);
        const maxTries = 50;
        let x, y, tries = 0;
        do {
            x = Math.random() * (this.app.screen.width - radius * 2) + radius;
            y = Math.random() * (this.app.screen.height - this.hudHeight - radius * 2) + this.hudHeight + radius;
            tries++;
        } while (this.physicsManager.isOverlapping(x, y, radius) && tries < maxTries);
        
        const ball = new PIXI.Graphics();
        ball.beginFill(color);
        ball.drawCircle(0, 0, radius);
        ball.endFill();
        ball.tint = color;
        ball.originalTint = color; // Store original color for powerup restoration
        ball.interactive = false;
        ball.buttonMode = false;
        ball.x = x;
        ball.y = y;
        ball.radius = radius; // Store radius for collision detection
        ball.width = radius * 2; // Set width for collision detection
        ball.height = radius * 2; // Set height for collision detection
        ball.on('pointerdown', () => this.onBallClick(ball));
        this.app.stage.addChild(ball);
        this.balls.push(ball);
        
        if (window.VisualEffects && typeof window.VisualEffects.createTracer === 'function') {
            window.VisualEffects.createTracer(ball, { iterations: 4, color: 0xFFFFFF, alpha: 0.69, gap: 5 });
        }
        
        // Create physics body for the ball
        this.physicsManager.createBallBody(x, y, radius, ball);
        return ball;
    }

    updateLevel() {
        const oldLevel = this.level;
        let newLevel = 1;
        let scoreThreshold = 0;
        while (this.score >= scoreThreshold && newLevel < 10) {
            scoreThreshold += 100 * newLevel * (newLevel + 1) / 2;
            newLevel++;
        }
        newLevel = Math.max(1, newLevel - 1);
        if (this.score >= scoreThreshold) {
            const additionalLevels = Math.floor((this.score - scoreThreshold) / 500) + 1;
            newLevel += additionalLevels;
        }
        this.level = newLevel;
        if (this.level === 3 && !this.gxpAwarded) {
            this.gxpAwarded = true;
            if (typeof transactResource === 'function') {
                transactResource('gxp', 10, 'add', showAlert, updatePlayerBalances);
            }
            showAlert('Victory! You earned GXP for reaching level 3!');
        }
        if (oldLevel !== this.level) {
            this.gameOver = true;
            if (window.VisualEffects && typeof window.VisualEffects.createLevelUpEffect === 'function') {
                try {
                    window.VisualEffects.createLevelUpEffect(this.app.stage, this.level, () => {
                        this.gameOver = false;
                        this.adjustBallCount();
                    });
                } catch (error) { 
                    this.gameOver = false;
                    this.adjustBallCount();
                }
            } else {
                this.gameOver = false;
                this.adjustBallCount();
            }
        }
    }

    adjustBallCount() {
        const targetCount = Math.min(5 + (this.level - 1) * 5, 50);
        const ballSize = Math.max(10, 25 - (this.level - 1) * 2);
        while (this.balls.length > targetCount) {
            const ball = this.balls.pop();
            const ballBodies = this.physicsManager.getBallBodies();
            const body = ballBodies[ballBodies.length - 1];
            this.app.stage.removeChild(ball);
            this.physicsManager.removeBallBody(body);
        }
        while (this.balls.length < targetCount) {
            const newBall = this.createBall(this.colors[Math.floor(Math.random() * this.colors.length)]);
            if (this.gameInterval) {
                newBall.interactive = true;
                newBall.buttonMode = true;
            }
        }
    }

    onBallClick(ball) {
        // Prevent the canvas click handler from firing for this click
        this.ballClickedThisFrame = true; 
        // Check if this is a powerup activation click
        if (this.activePowerupColor && ball.tint === this.activePowerupColor) {
            this.activateColorBurstPowerup(ball.tint);
            return;
        }
        
        if (window.GameSounds) {
            window.GameSounds.playBubblePop();
        }
        const currentTime = Date.now();
        this.clickSpeed = currentTime - this.lastClickTime;
        this.lastClickTime = currentTime;
        const basePoints = Math.max(10, Math.floor(1000 / this.clickSpeed));
        const levelMultiplier = 1 + (this.level - 1) * 0.2;
        const points = Math.floor(basePoints * levelMultiplier);
        this.score += points; 
        this.powerupProgress++;
        if (this.powerupBar) {
            this.powerupBar.setValue(this.powerupProgress);
        } 
        if (this.powerupProgress >= this.powerupTarget && !this.activePowerupColor) {
            this.activatePowerupMode();
        } 
        if (window.VisualEffects && typeof window.VisualEffects.createExplosion === 'function') {
            try {
                window.VisualEffects.createExplosion(ball.x, ball.y, ball.tint, 10, this.app.stage);
            } catch (error) {
                console.warn('VisualEffects.createExplosion failed:', error);
            }
        }
        if (window.VisualEffects && typeof window.VisualEffects.createScorePopup === 'function') {
            try {
                window.VisualEffects.createScorePopup(ball.x, ball.y, points, this.app.stage, 0x00FF00);
            } catch (error) {
                console.warn('VisualEffects.createScorePopup failed:', error);
            }
        }
        this.app.stage.removeChild(ball);
        const idx = this.balls.indexOf(ball);
        if (idx !== -1) {
            this.balls.splice(idx, 1);
            const ballBodies = this.physicsManager.getBallBodies();
            const body = ballBodies[idx];
            if (body) {
                this.physicsManager.removeBallBody(body);
            }
        }
        this.updateLevel();
        this.updateHUD();
        this.adjustBallCount();
    }

    activatePowerupMode() { 
        this.activePowerupColor = this.colors[Math.floor(Math.random() * this.colors.length)]; 
        this.startPowerupBlinking(); 
        if (window.VisualEffects && typeof window.VisualEffects.createScorePopup === 'function') {
            try {
                const colorName = this.getColorName(this.activePowerupColor);
                window.VisualEffects.createScorePopup(
                    this.app.screen.width / 2, 
                    this.app.screen.height / 2, 
                    `${colorName} POWERUP!`, 
                    this.app.stage, 
                    0x00FFFF
                );
            } catch (error) {
                console.warn('VisualEffects.createScorePopup failed:', error);
            }
        }
    }

    startPowerupBlinking() {
        // Clear any existing blink interval
        if (this.powerupBlinkInterval) {
            clearInterval(this.powerupBlinkInterval);
        } 
        this.powerupBlinkState = false;
        this.powerupBlinkInterval = setInterval(() => {
            this.powerupBlinkState = !this.powerupBlinkState; 
            // Update all balls of the active powerup color
            this.balls.forEach(ball => {
                if (ball.tint === this.activePowerupColor) {
                    ball.tint = this.powerupBlinkState ? 0xFFFFFF : this.activePowerupColor;
                }
            });
        }, 200);  
    }

    activateColorBurstPowerup(color) { 
        if (this.powerupBlinkInterval) {
            clearInterval(this.powerupBlinkInterval);
            this.powerupBlinkInterval = null;
        } 
        // Find all balls of the same color
        const ballsToRemove = this.balls.filter(ball => ball.tint === color);
        
        // Calculate total points for all balls
        let totalPoints = 0;
        ballsToRemove.forEach(ball => {
            const basePoints = Math.max(10, Math.floor(1000 / this.clickSpeed));
            const levelMultiplier = 1 + (this.level - 1) * 0.2;
            const points = Math.floor(basePoints * levelMultiplier);
            totalPoints += points;
            
            // Create explosion effect for each ball
            if (window.VisualEffects && typeof window.VisualEffects.createExplosion === 'function') {
                try {
                    window.VisualEffects.createExplosion(ball.x, ball.y, ball.tint, 15, this.app.stage);
                } catch (error) {
                    console.warn('VisualEffects.createExplosion failed:', error);
                }
            } 
            // Remove ball from stage and physics
            this.app.stage.removeChild(ball);
            const idx = this.balls.indexOf(ball);
            if (idx !== -1) {
                this.balls.splice(idx, 1);
                const ballBodies = this.physicsManager.getBallBodies();
                const body = ballBodies[idx];
                if (body) {
                    this.physicsManager.removeBallBody(body);
                }
            }
        }); 
        this.score += totalPoints; 
        if (window.VisualEffects && typeof window.VisualEffects.createScorePopup === 'function') {
            try {
                window.VisualEffects.createScorePopup(
                    this.app.screen.width / 2, 
                    this.app.screen.height / 2, 
                    `+${totalPoints}`, 
                    this.app.stage, 
                    0x00FFFF
                );
            } catch (error) {
                console.warn('VisualEffects.createScorePopup failed:', error);
            }
        } 
        this.powerupProgress = 0;
        this.activePowerupColor = null;
        if (this.powerupBar) {
            this.powerupBar.setValue(this.powerupProgress);
        } 
        this.balls.forEach(ball => {
            ball.tint = ball.originalTint || ball.tint;
        });
        
        this.updateLevel();
        this.updateHUD();
        this.adjustBallCount();
    }

    getColorName(color) {
        switch (color) {
            case 0xFF0000: return 'RED';
            case 0x0000FF: return 'BLUE';
            case 0x00FF00: return 'GREEN';
            default: return 'COLOR';
        }
    }

    async startGame() {
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        } 
        try {
            this.resetGame();
            await transactResource('credits', 1, 'subtract', updatePlayerBalances); 
            this.initGame();
            const startScreen = document.getElementById('start-screen');
            const gameArea = document.getElementById('game-area');
            const gameOverScreen = document.getElementById('game-over-screen');
            if (startScreen) startScreen.style.display = 'none';
            if (gameArea) gameArea.style.display = 'block';
            if (gameOverScreen) gameOverScreen.style.display = 'none';
            for (let i = 0; i < 5; i++) {
                const color = this.colors[i % this.colors.length];
                this.createBall(color);
            }
            this.startCountdown();
        } catch (error) {  
            showAlert("BouncyClick: Error in startGame: " + (error && error.message ? error.message : error)); 
        }
    }

    resetGame() {
        this.balls.forEach(ball => {
            if (ball && ball.parent) {
                ball.parent.removeChild(ball);
            }
        });
        this.balls = [];
        if (this.physicsManager) {
            this.physicsManager.cleanup();
        }
        this.clickSpeed = 0;
        this.lastClickTime = 0;
        this.gxpAwarded = false;
        this.hearts = this.maxHearts;
        this.ballClickedThisFrame = false; 
        this.powerupProgress = 0;
        this.activePowerupColor = null;
        if (this.powerupBlinkInterval) {
            clearInterval(this.powerupBlinkInterval);
            this.powerupBlinkInterval = null;
        }
        this.powerupBlinkState = false;
        if (this.powerupBar) {
            this.powerupBar.setValue(this.powerupProgress);
        }
        
        if (this.gameInterval) {
            clearInterval(this.gameInterval);
            this.gameInterval = null;
        }
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        // Remove canvas click handler to prevent heart loss during countdown
        if (this.canvasClickHandler) {
            this.app.view.removeEventListener('click', this.canvasClickHandler);
            this.canvasClickHandler = null;
        }
        this.updateHUD();
    }

    onCountdownComplete() { 
        this.balls.forEach(ball => {
            ball.interactive = true;
            ball.buttonMode = true;
        }); 
        this.canvasClickHandler = (event) => {
            // If a ball was clicked this frame, don't process this as a missed click
            if (this.ballClickedThisFrame) {
                this.ballClickedThisFrame = false;
                return;
            }
            
            const rect = this.app.view.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            if (y >= this.hudHeight) {
                let hitBall = false;
                this.balls.forEach(ball => {
                    const dx = x - ball.x;
                    const dy = y - ball.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    if (distance <= ball.width / 2) {
                        hitBall = true;
                    }
                });
                if (!hitBall) {
                    this.onMissedBall();
                }
            }
        };
        this.app.view.addEventListener('click', this.canvasClickHandler); 
        this.physicsManager.startPhysics(); 
        const physicsLoop = () => {
            if (this.physicsManager && this.physicsManager.physicsRunning) {
                this.physicsManager.runPhysics(this.windEffect, this.balls);
                requestAnimationFrame(physicsLoop);
            }
        };
        physicsLoop(); 
        this.gameInterval = setInterval(() => {
            this.time++;
            this.updateHUD();
            if (this.time >= 60) {
                this.endGame("Time's Up!");
            }
        }, 1000);
    }

    startCountdown() {
        this.physicsManager.stopPhysics();
        super.startCountdown();
    }

    stopGame() {
        this.physicsManager.stopPhysics();
        this.physicsManager.cleanup();
        this.balls = []; 
        if (this.powerupBlinkInterval) {
            clearInterval(this.powerupBlinkInterval);
            this.powerupBlinkInterval = null;
        }
        this.activePowerupColor = null;
        this.powerupBlinkState = false;
        
        if (this.canvasClickHandler) {
            this.app.view.removeEventListener('click', this.canvasClickHandler);
            this.canvasClickHandler = null;
        }
        super.stopGame();
    }

    onMissedBall() {
        this.hearts--;
        if (this.heartMeter) this.heartMeter.setValue(this.hearts);
        if (this.hearts <= 0) {
            this.endGame('Game over!');
        }
    }
} 

window.BouncyClick = BouncyClick;