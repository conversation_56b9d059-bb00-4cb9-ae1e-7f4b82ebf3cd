class TreasureFrenzy extends BaseGame {
    constructor() {
        super();
        this.balls = [];
        this.colors = [0xFF0000, 0x0000FF, 0x00FF00]; // Red, Blue, Green
        this.clickSpeed = 0;
        this.lastClickTime = 0;

        this.levelConfigs = {
            1: { ballCount: 3, ballSize: 25, pointsToNext: 100 },
            2: { ballCount: 4, ballSize: 22, pointsToNext: 250 },
            3: { ballCount: 5, ballSize: 20, pointsToNext: 450 },
            4: { ballCount: 6, ballSize: 18, pointsToNext: 700 },
            5: { ballCount: 7, ballSize: 15, pointsToNext: 1000 }
        };
    }

    createBall(color) {
        const ball = new PIXI.Graphics();
        const currentConfig = this.levelConfigs[this.level] || this.levelConfigs[5];

        ball.beginFill(color);
        ball.drawCircle(0, 0, currentConfig.ballSize);
        ball.endFill();
        ball.tint = color;
        ball.interactive = true;
        ball.buttonMode = true;

        ball.x = Math.random() * (this.app.screen.width - currentConfig.ballSize * 2) + currentConfig.ballSize;
        ball.y = Math.random() * (this.app.screen.height - currentConfig.ballSize * 2) + currentConfig.ballSize;

        ball.on('pointerdown', () => this.onBallClick(ball));

        this.app.stage.addChild(ball);
        this.balls.push(ball);

        return ball;
    }

    updateLevel() {
        const oldLevel = this.level;
        for (let level = 5; level >= 1; level--) {
            if (this.score >= this.levelConfigs[level - 1]?.pointsToNext || level === 1) {
                this.level = level;
                break;
            }
        }

        if (oldLevel !== this.level) {
            this.adjustBallCount();
        }
    }

    adjustBallCount() {
        const config = this.levelConfigs[this.level] || this.levelConfigs[5];
        const targetCount = config.ballCount;

        while (this.balls.length > targetCount) {
            const ball = this.balls.pop();
            this.app.stage.removeChild(ball);
        }

        while (this.balls.length < targetCount) {
            this.createBall(this.colors[Math.floor(Math.random() * this.colors.length)]);
        }
    }

    onBallClick(ball) {
        const currentTime = Date.now();
        this.clickSpeed = currentTime - this.lastClickTime;
        this.lastClickTime = currentTime;

        const basePoints = Math.max(10, Math.floor(1000 / this.clickSpeed));
        const levelMultiplier = 1 + (this.level - 1) * 0.2;
        const points = Math.floor(basePoints * levelMultiplier);

        this.score += points;

        this.app.stage.removeChild(ball);
        this.balls = this.balls.filter(b => b !== ball);
        this.createBall(this.colors[Math.floor(Math.random() * this.colors.length)]);

        this.updateLevel();
        this.updateHUD();
    }

    async startGame() {
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }

        try {
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);

            this.initGame();

            const startScreen = document.getElementById('start-screen');
            const gameArea = document.getElementById('game-area');
            const gameOverScreen = document.getElementById('game-over-screen');

            if (startScreen) startScreen.style.display = 'none';
            if (gameArea) gameArea.style.display = 'block';
            if (gameOverScreen) gameOverScreen.style.display = 'none';

            const config = this.levelConfigs[1];
            for (let i = 0; i < config.ballCount; i++) {
                const color = this.colors[i % this.colors.length];
                this.createBall(color);
            }

            this.gameInterval = setInterval(() => {
                this.time++;
                this.updateHUD();

                if (this.time >= 60) {
                    this.endGame("Time's Up!");
                }
            }, 1000);
        } catch (error) {
            console.error('ERROR: Failed to start TreasureFrenzy game:', error);
            showAlert("Failed to start game. Please try again.");
        }
    }

    stopGame() {
        this.balls = [];
        super.stopGame();
    }
}