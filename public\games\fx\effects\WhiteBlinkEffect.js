// White Blink Effect
class WhiteBlinkEffect {
    constructor() {
        // No dependencies needed for this effect
    }

    // NES-style white blink effect for sprites
    create(sprite, duration = 120, onComplete) {
        if (!sprite) return;
        const originalTint = sprite.tint;
        const originalAlpha = sprite.alpha;
        let blinkCount = 0;
        const maxBlinks = 2;
        const blink = () => {
            if (blinkCount >= maxBlinks) {
                sprite.tint = originalTint;
                sprite.alpha = originalAlpha;
                if (typeof onComplete === 'function') onComplete();
                return;
            }
            sprite.tint = 0xFFFFFF;
            sprite.alpha = 1;
            setTimeout(() => {
                sprite.tint = originalTint;
                sprite.alpha = originalAlpha;
                blinkCount++;
                setTimeout(blink, duration / 2);
            }, duration / 2);
        };
        blink();
    }
} 