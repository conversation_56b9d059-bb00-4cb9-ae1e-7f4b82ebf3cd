var gameLog; 
let allLogs = []; // Array of logs or default messages
let currentLogIndex = 0; // Current index in the allLogs array
const shownMessages = new Set(); // Track messages that have already been shown
const defaultMessages = [
  "Tip: Speed up nap times by moving Crushies into a house!",
  "Tip: Play mini games while you wait on adventures!",
  "Tip: Each adventure increases your XP!",
  "I need a Roboccino.",
  "Oh joy, I must clean up the toys. Again.",
  "I enjoy a Robo opera from time to time.",
  "I could use a Robo spa day.",
  "Being a robobutler takes a lot of gears."
];

let tipTypingEffect = null;
let tipTimeoutId = null;

function showCrushinatorTip(message) {
  const tipSpan = $('#playerCountersContainer ul li:nth-child(4) span')[0];
  if (tipTypingEffect) tipTypingEffect.stop();
  if (tipTimeoutId) clearTimeout(tipTimeoutId);
  tipSpan.textContent = '';
  tipTypingEffect = new TypingEffect(tipSpan, {
    speed: 40,
    typeBy: 'letter',
    onComplete: () => {
      tipTimeoutId = setTimeout(() => {
        tipSpan.textContent = '';
        tipTypingEffect = null;
        tipTimeoutId = null;
      }, 15000);
    }
  });
  tipTypingEffect.type(message);
}

function clearCrushinatorTip() {
  const tipSpan = $('#playerCountersContainer ul li:nth-child(4) span')[0];
  if (tipTypingEffect) tipTypingEffect.stop();
  if (tipTimeoutId) clearTimeout(tipTimeoutId);
  tipSpan.textContent = '';
  tipTypingEffect = null;
  tipTimeoutId = null;
}


async function getLatestLogsFromOtherUsers(wax_id) {
  try {
    const currentDate = new Date();
    const threeDaysAgo = new Date(currentDate.getTime() - 3 * 24 * 60 * 60 * 1000);
    const filteredLogs = gameLog
      .filter(log =>
        log.wax_id !== wax_id &&
        ['adventure', 'team', 'reward'].includes(log.type) &&
        new Date(log.created_at) >= threeDaysAgo
      )
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at)); // Sort by created_at in descending order

    // Select logs from unique users
    const uniqueUserLogs = [];
    const uniqueUsers = new Set();
    for (const log of filteredLogs) {
      if (!uniqueUsers.has(log.wax_id)) {
        uniqueUsers.add(log.wax_id);
        uniqueUserLogs.push(log);
        if (uniqueUserLogs.length === 5) break; // Stop once we have 5 unique users
      }
    }

    // Format the logs for the popup
    const formattedLogs = uniqueUserLogs.map(log => {
      const readableTime = getHumanReadableTime(log.created_at);
      const logData = log.data; // Directly access the .data field

      let message;
      switch (log.type) {
        case 'adventure':
          var worldData = "World " + (logData.location[0] + 1);
          var locationData = getLocaleTile(allZones, logData.location[0], logData.location[1], logData.location[2]);
          message = `${log.wax_id} started an adventure in ${worldData}, Zone ${logData.location[1]}, ${locationData.Locale_Name} ${readableTime}`;
          break;
        case 'reward':
          message = `${log.wax_id} got a ${logData.type} reward ${readableTime}`;
          break;
        case 'team':
          message = `${log.wax_id} created a new team ${readableTime}`;
          break;
        default:
          message = `${log.wax_id} performed an action ${readableTime}`;
      }
      return message;
    });

    // Merge logs with default messages
    allLogs = [...formattedLogs, ...defaultMessages];

    // Start rotating logs
    rotateLogs();

  } catch (error) {
    console.error('Error fetching or processing logs:', error);
    // Fallback to default messages in case of error
    allLogs = defaultMessages;
    rotateLogs();
  }
}

function rotateLogs() {
  if (allLogs.length === 0) {
    allLogs = defaultMessages || ["No messages available."];
  }
  const availableMessages = allLogs.filter(message => !shownMessages.has(message));

  if (availableMessages.length === 0) {
    shownMessages.clear();
    availableMessages.push(...allLogs);
  }
  const messageToShow = availableMessages[currentLogIndex % availableMessages.length];
  $('#crushinator-icon').attr('src', 'images/ui/npc/crushinator-communication.gif');
  showCrushinatorTip(messageToShow);
  shownMessages.add(messageToShow);
  // Reset the icon after 2 seconds
  setTimeout(() => {
    $('#crushinator-icon').attr('src', 'images/ui/npc/crushinator-standby.png');
  }, 2000);
  // Move to the next message
  currentLogIndex = (currentLogIndex + 1) % availableMessages.length;
}

$(document).ready(function () {
  // On click, clear tip and only show again with typing effect
  $('#crushinator-icon').on('click', function () {
    clearCrushinatorTip();
    // Only show tip again on next click
    $(this).one('click', function () {
      rotateLogs();
    });
  });
  // Show initial tip on load
  rotateLogs();
});


const getGameLogs = async () => {
  try {
    const response = await axios.get(`${domain_url}/players/logs/`);
    console.log(response);
    gameLog = response.data;
    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error('Error retrieving game logs');
  }
};

const displayGameLogs = async (wax_id) => {
  await getGameLogs();
  await getLatestLogsFromOtherUsers(wax_id);
  const playerLogList = document.getElementById('player-log-list');
  playerLogList.innerHTML = '';
  // Filter, sort by newest, and take last 30
  const filteredLog = gameLog
    .filter(log => log.status === 'new' && wax.userAccount === log.wax_id)
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    .slice(0, 30);

  // Check if no logs are found for the current user
  if (filteredLog.length === 0) {
    const noLogsMessage = document.createElement('div');
    noLogsMessage.classList.add('card', 'mb-3');
    const cardBody = document.createElement('div');
    cardBody.classList.add('card-body', 'player-log-item');
    const message = document.createElement('p');
    message.classList.add('card-text', 'text-center', 'text-muted');
    message.style.fontStyle = 'italic';
    message.textContent = "Looks like you're new. No logs found.";
    cardBody.appendChild(message);
    noLogsMessage.appendChild(cardBody);
    playerLogList.appendChild(noLogsMessage);
    return;
  }

  filteredLog.forEach(log => {
    const card = document.createElement('div');
    card.classList.add('card', 'mb-3');
    const cardBody = document.createElement('div');
    cardBody.classList.add('card-body','player-log-item');
    const title = document.createElement('span');
    title.classList.add('card-title', 'player-log-header');
    title.textContent = (log.type).toUpperCase();
    const image = document.createElement('img');
    image.src = '../images/ui/info_icon.png';
    if(log.type === 'reward'){
      image.src = '../images/ui/reward_icon.png';
    }
    if(log.type === 'team'){
        image.src = '../images/ui/ready_icon.gif';
    }
    image.width = '12';
    const desc = document.createElement('p');
    desc.classList.add('card-text');
    desc.textContent = log.data.desc;
    const date = document.createElement('p');
    date.classList.add('card-text', 'text-muted');
    const createdAt = new Date(log.created_at);
    date.textContent = `${createdAt.toLocaleDateString()} - ${createdAt.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit', timeZoneName: 'short' })} ${createdAt.toLocaleString('en-US', { timeZoneName: 'short' }).split(' ')[2]}`;
    cardBody.appendChild(image);
    cardBody.appendChild(title);
    cardBody.appendChild(desc);
    cardBody.appendChild(date);
    card.appendChild(cardBody);
    playerLogList.appendChild(card);
  });
};
