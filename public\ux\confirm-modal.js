const CONFIRM_MODAL_DEFAULTS = {
  baseClass: 'modal confirm-modal',
  dialogClass: 'modal-dialog modal-dialog-centered',
  contentClass: 'modal-content',
  headerClass: 'modal-header',
  titleClass: 'modal-title',
  bodyClass: 'modal-body',
  footerClass: 'modal-footer',
  confirmButtonClass: 'btn-danger',
  confirmButtonText: 'Confirm',
  cancelButtonClass: 'btn-secondary',
  cancelButtonText: 'Cancel'
};

function createConfirmModal(title, message, onConfirm, onCancel, options = {}) {
  const modalId = `confirm-${title.toLowerCase().replace(/\s+/g, '-')}-modal`;
  const existingModal = document.getElementById(modalId);
  if (existingModal) {
    existingModal.remove();
  }
  const modalContainer = document.createElement('div');
  modalContainer.classList.add(...CONFIRM_MODAL_DEFAULTS.baseClass.split(' '));
  modalContainer.id = modalId;
  const modalDialog = document.createElement('div');
  modalDialog.classList.add(...CONFIRM_MODAL_DEFAULTS.dialogClass.split(' '));
  modalDialog.setAttribute('role', 'document');
  modalContainer.appendChild(modalDialog);
  const modalContent = document.createElement('div');
  modalContent.classList.add(...CONFIRM_MODAL_DEFAULTS.contentClass.split(' '));
  modalDialog.appendChild(modalContent);
  const modalHeader = document.createElement('div');
  modalHeader.classList.add(...CONFIRM_MODAL_DEFAULTS.headerClass.split(' '));
  modalContent.appendChild(modalHeader);
  const modalTitle = document.createElement('h5');
  modalTitle.classList.add(...CONFIRM_MODAL_DEFAULTS.titleClass.split(' '));
  modalTitle.textContent = title;
  modalHeader.appendChild(modalTitle);
  const modalBody = document.createElement('div');
  modalBody.classList.add(...CONFIRM_MODAL_DEFAULTS.bodyClass.split(' '));
  modalBody.innerHTML = message;
  modalContent.appendChild(modalBody);
  const modalFooter = document.createElement('div');
  modalFooter.classList.add(...CONFIRM_MODAL_DEFAULTS.footerClass.split(' '));
  modalContent.appendChild(modalFooter);
  const cancelButton = document.createElement('button');
  cancelButton.classList.add(...CONFIRM_MODAL_DEFAULTS.cancelButtonClass.split(' '));
  cancelButton.textContent = CONFIRM_MODAL_DEFAULTS.cancelButtonText;
  cancelButton.addEventListener('click', () => {
    modalContainer.style.display = 'none';
    if (onCancel) onCancel();
  });
  modalFooter.appendChild(cancelButton);
  const confirmButton = document.createElement('button');
  confirmButton.classList.add(...CONFIRM_MODAL_DEFAULTS.confirmButtonClass.split(' '));
  confirmButton.textContent = CONFIRM_MODAL_DEFAULTS.confirmButtonText;
  confirmButton.addEventListener('click', () => {
    modalContainer.style.display = 'none';
    if (onConfirm) onConfirm();
  });
  modalFooter.appendChild(confirmButton);
  document.getElementById('main-content').appendChild(modalContainer);
  modalContainer.style.display = 'block';

  return modalContainer;
}

function showCancelAdventureConfirmation(adventureId, deleteFunction) {
  createConfirmModal(
    'Cancel Adventure',
    'Are you sure you want to cancel this adventure? This action cannot be undone.',
    () => {
      const adventureItem = document.querySelector(`.inventory-item[data-adventure-id="${adventureId}"]`);
      if (adventureItem) {
        adventureItem.remove();
      }
      deleteFunction(adventureId);
    }
  );
}

function showDisbandTeamConfirmation(teamId, deleteTeamFunction, reloadFunction) {
  createConfirmModal(
    'Disband Team',
    'Are you sure you want to disband this team? All creatures will be returned to your inventory. This action cannot be undone.',
    async () => {
      const teamItem = document.querySelector(`.inventory-item[data-team-id="${teamId}"]`);
      if (teamItem) {
        teamItem.remove();
      }
      await deleteTeamFunction(teamId);
      await reloadFunction('ready');
    }
  );
}

function showPayGXPConfirmation(amount, zoneNumber, confirmFunction) {
  createConfirmModal(
    'Confirm GXP Payment',
    `Are you sure you want to spend ${amount} GXP to help unlock Zone ${zoneNumber}? This action cannot be undone.`,
    () => {
      confirmFunction();
    }
  );
}
