/**
 * CollectList.js - Universal collectibles HUD element for mini-games
 * 
 * Features:
 * - Displays collectibles with 4x4 pixel icons and numbers
 * - Supports horizontal or vertical layout
 * - Configurable styling and positioning
 * - Reusable across different games
 * 
 * Usage:
 * const collectList = new CollectList(options);
 * collectList.updateCollectibles({ wood: { collected: 2, required: 3, icon: 'wood.png' } });
 */

class CollectList extends PIXI.Container {
    constructor(options = {}) {
        super();
        
        // Default options
        this.options = Object.assign({
            // Layout options
            layout: 'vertical', // 'horizontal' or 'vertical'
            title: 'COLLECT',
            showTitle: true,
            
            // Styling
            fontSize: 12,
            textColor: 0xFFFFFF,
            strokeColor: 0x000000,
            strokeThickness: 2,
            titleColor: 0xFFFFFF,
            titleStrokeColor: 0x000000,
            titleStrokeThickness: 2,
            
            // Spacing and sizing
            iconSize: 12, // 12x12 pixel icons as requested
            spacing: 8, // Space between items
            itemSpacing: 4, // Space between icon and text within item
            titleSpacing: 12, // Space between title and items
            
            // Positioning
            padding: { x: 10, y: 5 },
            
            // Animation
            animateUpdates: true,
            updateDuration: 300, // milliseconds
            
            // Custom styling
            customStyle: null, // Override default text style
            customTitleStyle: null // Override default title style
        }, options);
        
        // Internal state
        this.collectibles = {};
        this.items = new Map(); // Store item containers for easy updates
        this.titleText = null;
        
        // Create the display
        this.createDisplay();
    }
    
    createDisplay() {
        // Create title if needed
        if (this.options.showTitle && this.options.title) {
            this.createTitle();
        }
        
        // Create container for collectible items
        this.itemsContainer = new PIXI.Container();
        this.addChild(this.itemsContainer);
        
        // Position items container
        if (this.titleText) {
            this.itemsContainer.y = this.titleText.height + this.options.titleSpacing;
        }
    }
    
    createTitle() {
        const titleStyle = this.options.customTitleStyle || new PIXI.TextStyle({
            fontFamily: 'Arial',
            fontSize: this.options.fontSize,
            fill: this.options.titleColor,
            stroke: this.options.titleStrokeColor,
            strokeThickness: this.options.titleStrokeThickness,
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowBlur: 2,
            dropShadowDistance: 1
        });
        
        this.titleText = new PIXI.Text(this.options.title, titleStyle);
        this.titleText.x = this.options.padding.x;
        this.titleText.y = this.options.padding.y;
        this.addChild(this.titleText);
    }
    
    /**
     * Update the collectibles display
     * @param {Object} collectibles - Object with collectible data
     * @param {string} collectibles[].icon - Path to icon image
     * @param {number} collectibles[].collected - Number collected
     * @param {number} collectibles[].required - Number required
     * @param {string} collectibles[].label - Optional custom label (defaults to key name)
     */
    updateCollectibles(collectibles) {
        this.collectibles = collectibles;
        
        // Clear existing items
        this.itemsContainer.removeChildren();
        this.items.clear();
        
        // Create new items
        let x = this.options.padding.x;
        let y = this.options.padding.y;
        
        if (this.options.layout === 'horizontal') {
            // For horizontal layout, create icon-only pattern with count numbers
            Object.keys(collectibles).forEach((key, index) => {
                const data = collectibles[key];
                
                // Create icon container
                const iconContainer = new PIXI.Container();
                const icon = PIXI.Sprite.from(data.icon);
                icon.width = this.options.iconSize;
                icon.height = this.options.iconSize;
                iconContainer.addChild(icon);
                iconContainer.x = x;
                iconContainer.y = y;
                this.itemsContainer.addChild(iconContainer);
                x += this.options.iconSize + this.options.itemSpacing;
                
                // Create count text (just the numbers)
                const textContainer = new PIXI.Container();
                const text = `${data.collected}/${data.required}`;
                
                const textStyle = this.options.customStyle || new PIXI.TextStyle({
                    fontFamily: 'Arial',
                    fontSize: this.options.fontSize,
                    fill: this.options.textColor,
                    stroke: this.options.strokeColor,
                    strokeThickness: this.options.strokeThickness,
                    dropShadow: true,
                    dropShadowColor: 0x000000,
                    dropShadowBlur: 2,
                    dropShadowDistance: 1
                });
                
                const textElement = new PIXI.Text(text, textStyle);
                textElement.y = 0; // Align with icon
                textContainer.addChild(textElement);
                textContainer.x = x;
                textContainer.y = y;
                this.itemsContainer.addChild(textContainer);
                x += textElement.width + this.options.spacing;
                
                // Store references for updates
                const itemData = {
                    iconContainer: iconContainer,
                    textContainer: textContainer,
                    icon: icon,
                    text: textElement,
                    key: key
                };
                this.items.set(key, itemData);
            });
        } else {
            // Vertical layout (original behavior)
            Object.keys(collectibles).forEach((key, index) => {
                const item = this.createCollectibleItem(key, collectibles[key]);
                item.x = x;
                item.y = y;
                y += item.height + this.options.spacing;
                this.itemsContainer.addChild(item);
                this.items.set(key, item);
            });
        }
    }
    
    createCollectibleItem(key, data) {
        const container = new PIXI.Container();
        
        // Create icon
        const icon = PIXI.Sprite.from(data.icon);
        icon.width = this.options.iconSize;
        icon.height = this.options.iconSize;
        container.addChild(icon);
        
        // Create text - use shorter format for horizontal layout
        const label = data.label || key.toUpperCase();
        let text;
        if (this.options.layout === 'horizontal') {
            // Use shorter format: "WOOD 2/3" instead of "WOOD: 2/3"
            text = `${label} ${data.collected}/${data.required}`;
        } else {
            text = `${label}: ${data.collected}/${data.required}`;
        }
        
        const textStyle = this.options.customStyle || new PIXI.TextStyle({
            fontFamily: 'Arial',
            fontSize: this.options.fontSize,
            fill: this.options.textColor,
            stroke: this.options.strokeColor,
            strokeThickness: this.options.strokeThickness,
            dropShadow: true,
            dropShadowColor: 0x000000,
            dropShadowBlur: 2,
            dropShadowDistance: 1
        });
        
        const textElement = new PIXI.Text(text, textStyle);
        textElement.x = this.options.iconSize + this.options.itemSpacing;
        textElement.y = 0; // Align with icon
        container.addChild(textElement);
        
        // Store references for updates
        container.icon = icon;
        container.text = textElement;
        container.key = key;
        
        return container;
    }
    
    /**
     * Update a specific collectible count
     * @param {string} key - Collectible key
     * @param {number} collected - New collected count
     * @param {number} required - New required count (optional)
     */
    updateCollectibleCount(key, collected, required = null) {
        const itemData = this.items.get(key);
        if (!itemData) return;
        
        const data = this.collectibles[key];
        if (!data) return;
        
        // Update data
        data.collected = collected;
        if (required !== null) {
            data.required = required;
        }
        
        // Update text - only show count numbers for horizontal layout
        let text;
        if (this.options.layout === 'horizontal') {
            // Just show the count: "2/3"
            text = `${collected}/${data.required}`;
        } else {
            // Vertical layout keeps the full format
            const label = data.label || key.toUpperCase();
            text = `${label}: ${collected}/${data.required}`;
        }
        
        // Update the text element (could be in itemData.text or itemData.textContainer.children[0])
        const textElement = itemData.text || (itemData.textContainer ? itemData.textContainer.children[0] : null);
        if (textElement) {
            textElement.text = text;
        }
        
        // Animate update if enabled
        if (this.options.animateUpdates) {
            this.animateUpdate(itemData);
        }
    }
    
    /**
     * Animate the update of a collectible item
     */
    animateUpdate(itemData) {
        // Animate both icon and text containers
        const containers = [];
        if (itemData.iconContainer) containers.push(itemData.iconContainer);
        if (itemData.textContainer) containers.push(itemData.textContainer);
        if (itemData.icon) containers.push(itemData.icon);
        if (itemData.text) containers.push(itemData.text);
        
        containers.forEach(container => {
            if (container && container.scale) {
                const originalScale = container.scale.x;
                
                // Scale up briefly
                container.scale.set(originalScale * 1.2);
                
                // Return to normal scale
                setTimeout(() => {
                    container.scale.set(originalScale);
                }, this.options.updateDuration);
            }
        });
    }
    
    /**
     * Set the layout (horizontal or vertical)
     */
    setLayout(layout) {
        if (this.options.layout !== layout) {
            this.options.layout = layout;
            this.updateCollectibles(this.collectibles);
        }
    }
    setTitleVisible(visible) {
        if (this.titleText) {
            this.titleText.visible = visible;
            if (visible) {
                this.itemsContainer.y = this.titleText.height + this.options.titleSpacing;
            } else {
                this.itemsContainer.y = this.options.padding.y;
            }
        }
    }
    getTotalWidth() {
        if (this.options.layout === 'horizontal') {
            let width = this.options.padding.x;
            this.items.forEach(item => {
                width += item.width + this.options.spacing;
            });
            return width - this.options.spacing + this.options.padding.x;
        } else {
            let maxWidth = 0;
            this.items.forEach(item => {
                maxWidth = Math.max(maxWidth, item.width);
            });
            return maxWidth + (this.options.padding.x * 2);
        }
    }
    getTotalHeight() {
        if (this.options.layout === 'vertical') {
            let height = this.options.padding.y;
            if (this.titleText && this.titleText.visible) {
                height += this.titleText.height + this.options.titleSpacing;
            }
            this.items.forEach(item => {
                height += item.height + this.options.spacing;
            });
            return height - this.options.spacing + this.options.padding.y;
        } else {
            let maxHeight = 0;
            this.items.forEach(item => {
                maxHeight = Math.max(maxHeight, item.height);
            });
            if (this.titleText && this.titleText.visible) {
                maxHeight += this.titleText.height + this.options.titleSpacing;
            }
            return maxHeight + (this.options.padding.y * 2);
        }
    }
}

window.CollectList = CollectList;
