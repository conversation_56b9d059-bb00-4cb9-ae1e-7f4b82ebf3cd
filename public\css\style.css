html, body, * {
  cursor: url('../images/ui/pointers/cursor-small-new.png') 0 0, url('../images/ui/pointers/cursor-small-new.png') 0 0, auto !important;
} 


html{
  background-color: transparent !important; /* or initial */
}

body{
  margin: 0;
  padding: 0;
  background-image: url('../images/ui/map-viewer/white-pattern2.png');
  background-repeat: repeat;
  background-size: 100% 100%;
  -webkit-font-smoothing: antialiased;
}
 

.card.mb-3 {
  border: none !important; background:none !important;
}

/* Map section */
.container-fluid {
  padding-left: 20px;
  padding-right: 20px;
}

.form-group {
  margin-right: 10px;
  margin-left: 10px;
}

.overlay-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 128px;
  height: 128px;
  background-image: url('../images/ui/map-viewer/Overlay_Border.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.overlay-zone {
  position: absolute;
  top: 0;
  left: 0;
  width: 128px;
  height: 128px;
  background-image: url('../images/ui/map-viewer/Overlay_Select_Lg.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Tab System Container */
.tab-container {
  display: none;
  padding: 20px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-content {
  background: #fff;
}

/* Tab Counter Badge */
.tab-counter {
  background: #f0f0f0;
  font-size: 11px;
  border-radius: 10px;
  padding: 3px 6px;
  margin-left: 6px;
  color: #666;
  font-weight: 500;
}

/* Tab Navigation */
.tab-buttons {
  display: flex;
  gap: 6px;
  padding: 4px;
}

/* Tab Buttons */
.tab-button {
  padding: 8px 14px;
  cursor: pointer;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  color: #555;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.tab-button:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
}

.tab-button img {
  opacity: 0.8;
}

/* Active Tab State */
.active-tab {
  background-color: #4a9ff5;
  color: white;
  box-shadow: 0 1px 3px rgba(74, 159, 245, 0.2);
} 

.active-tab .tab-counter {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* Contains icons shown on world map squares like unlocked zones */
.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

/* Center Three.js map in card */
.threejs-map-centered {
  display: flex;
  justify-content: center;
  align-items: center; 
  position: relative;  
  border-radius: inherit;
  background-color: black;
}

#threejs-map-container canvas {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  max-height: 100%; 
}

@media (max-width: 600px) {
    .card{
      margin-bottom:1em !important;
    }
    .player-stats-list li {
        flex: 1 1 100%; /* On small screens, make each item take full width */
    }
    /* #playerCountersContainer{
      margin-right: 0.75em;
     } */
}
