function abbreviateNumber(number) {
	var SI_SYMBOL = ["", "k", "M", "B", "T", "Q", "QT"];
	var tier = Math.log10(Math.abs(number)) / 3 | 0;
	if (tier == 0) return number;
	var suffix = SI_SYMBOL[tier];
	var scale = Math.pow(10, tier * 3);
	var scaled = number / scale;
	return scaled.toFixed(1) + suffix;
}

function getRandomName(array) {
	var x = array[Math.round(Math.random() * (array.length - 1))];
	return x;
}

function randomIntFloor(min, max) {
	return Math.floor(Math.random() * (max - min + 1) + min);
}


function numberWithCommas(x) {
	return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

const getHumanReadableTime = (timestamp) => {
  const currentDate = new Date();
  const createdAt = new Date(timestamp);
  const timeDiff = Math.floor((currentDate - createdAt) / 1000); // Difference in seconds

  if (timeDiff < 60) {
    return 'Just now';
  } else if (timeDiff < 3600) {
    return 'A few minutes ago';
  } else if (timeDiff < 2 * 3600) {
    return 'An hour ago';
  } else if (timeDiff < 6 * 3600) {
    return 'A few hours ago';
  } else if (timeDiff < 24 * 3600) {
    return 'Today';
  } else if (timeDiff < 48 * 3600) {
    return 'Yesterday';
  } else if (timeDiff < 72 * 3600) {
    return '2 days ago';
  } else if (timeDiff < 96 * 3600) {
    return '3 days ago';
  } else {
    return 'More than 3 days ago';
  }
};

function getTimeLeft(current, init) {
  const secondsLeft = (init - current) / 10 * 60 * 2;
  const minutes = Math.floor(secondsLeft / 60);
  const seconds = Math.floor(secondsLeft % 60);
  const timeString = `About ${minutes}m ${seconds}s left`;
  return timeString;
}

function copyrightTime() {
  const now = new Date();
  const laTime = new Date(now.toLocaleString('en-US', { timeZone: 'America/Los_Angeles' }));
  const formattedDate = (laTime.getMonth() + 1) + '/' + laTime.getDate();
  const year = laTime.getFullYear();
  let hour = laTime.getHours();
  const ampm = hour >= 12 ? 'PM' : 'AM';
  hour = hour % 12 || 12; // Convert 24-hour format to 12-hour format
  const copyrightText = document.getElementById('copyright');
  copyrightText.innerHTML = '&copy; ' + year + ' Summershiloh. ' + hour + ampm + ' PST ' + formattedDate + '/' + year;
}

function convertDateSimple(isoDateStr){
 const dateObj = new Date(isoDateStr);
 const month = dateObj.toLocaleString('en-US', { month: '2-digit' });
 const day = dateObj.toLocaleString('en-US', { day: '2-digit' });
 const year = dateObj.toLocaleString('en-US', { year: 'numeric' });
 const hour = dateObj.toLocaleString('en-US', { hour: 'numeric', hour12: true });
 // const formattedDateStr = `${month}-${day}-${year} at ${hour}`;
	const formattedDateStr = `${month}-${day}-${year}`;
 return formattedDateStr;
}

async function getRequiredXPForNextLevel(level){
  var nextLevel = level + 1;
  if (nextLevel < 1 || nextLevel > 30) {
     return null;
   }
   return xpTable[nextLevel];
}

function generateTeamName() {
  let name;
  do {
    const flower = flowers[Math.floor(Math.random() * flowers.length)];
    const color = colors[Math.floor(Math.random() * colors.length)];
    const word1 = color;
    const word2 = verbs[Math.floor(Math.random() * verbs.length)];
    const romanNumeral = romanNumerals[Math.floor(Math.random() * romanNumerals.length)];
    name = `${word1} ${word2} ${flower} ${romanNumeral}`;
  } while (name.length > 22);
  return name;
}

function getImmutableData(asset, get_data, data) {
  for (var i = 0; i < data.length; i++) {
    if (asset === data[i].asset_id) {
      return data[i].template.immutable_data[get_data];
    }
  }
  return null;
}

function getAssetInfo(asset, get_data, data) {
  for (var i = 0; i < data.length; i++) {
    if (asset === data[i].asset_id) {
      return data[i].data[get_data];
    }
  }
  return null;
}

async function getVehicleInfoByTeamId(info, team_id, teamData, vehiclesData) {
  if (team_id === undefined || team_id === null) {
    console.error("ERROR: team_id is undefined or null in getVehicleInfoByTeamId");
    return null;
  }

  if (!teamData || !Array.isArray(teamData) || teamData.length === 0) {
    console.error("ERROR: teamData is invalid in getVehicleInfoByTeamId:", teamData);
    return null;
  }

  var team;
  for (var a in teamData) {
    if (teamData[a].team_id === team_id) {
      team = teamData[a];
      break;
    }
  }

  if (!team) {
    console.error("ERROR: Team not found for team_id:", team_id); // Log if team is not found
    return null; // Return early to avoid further errors
  }

  if (!team.data || !team.data.vehicles || !team.data.vehicles.length) {
    console.error("Team data or vehicles array is invalid:", team); // Log if team data is invalid
    return null; // Return early to avoid further errors
  }

  var vehicleId = team.data.vehicles[0];
  console.log("Vehicle ID found:", vehicleId); // Log the vehicle ID

  if (!vehicleId) {
    console.error("No vehicle ID found in team data:", team); // Log if vehicle ID is missing
    return null; // Return early to avoid further errors
  }

  var result = getAssetInfo(vehicleId, info, vehiclesData);
  console.log("Result from getAssetInfo:", result); // Log the result of getAssetInfo

  if (!result) {
    console.error("No result returned from getAssetInfo for vehicleId:", vehicleId); // Log if result is invalid
  }

  return result;
}

 // houses data util
 async function getOccupiedHouses(teamData) {
   const occupiedHouses = [];
   for (let team of teamData) {
     if (team.data.house) {
       occupiedHouses.push(team.data.house);
     }
   }
   return occupiedHouses;
 }

 // adventure counting
async function countAdventuresInWorlds(status, data) {
  const adventureCounts = {};
  for (let adventure of data) {
    if (adventure.status === status) {
      const world = adventure.mapgrid_4;
      if (!adventureCounts[world]) {
        adventureCounts[world] = 0;
      }
      adventureCounts[world]++;
    }
  }
  return adventureCounts;
}

 // team data
 function getTeamById(teamId, teamData) {
   for (let i = 0; i < teamData.length; i++) {
     if (teamData[i].team_id === teamId) {
       console.log("Found the matching team info!" + teamData[i])
       return teamData[i];
     }
   }
 }

function findTeamData(myTeams, team, type, position) {
  for (let i = 0; i < myTeams.length; i++) {
    if (team === myTeams[i].team_id) {
      if (type === 'vehicles') {
        return myTeams[i].data.vehicles[position];
      } else if (type === 'creatures') {
        return myTeams[i].data.creatures[position];
      } else {
        throw new Error('Invalid type. Please choose either "vehicles" or "creatures".');
      }
    }
  }
}

const inTeamFilter = (item) => {
    const schema = item.schema.schema_name;
    if (schema === 'vehicles' || schema === 'creature') {
        const inTeam = isInTeam(schema, item, myTeams);
        return inTeam.inTeam;
    }
    return false;
};

const notInTeamFilter = (item) => {
    const schema = item.schema.schema_name;
    if (schema === 'vehicles' || schema === 'creature') {
        const inTeam = isInTeam(schema, item, myTeams);
        return !inTeam.inTeam;
    }
    return true; // Include all items that are not vehicles or creatures
};


function isTeamFull(newTeamObj) {
  if(!newTeamObj.vehicles){return false;}
  if (newTeamObj.creatures.length < newTeamObj.capacity) {
    console.log('There is still room in team vehicle.')
    return false;
  } else {
    console.log('There is no room in team vehicle.')
    return true;
  }
}

function isInTeam(schema, item, teamData) {
  if (!teamData.length) return {
    inTeam: false,
    teamId: 'No team!'
  };
  for (let i = 0; i < teamData.length; i++) {
    if (schema === "vehicles" && teamData[i].data.vehicles.includes(item.asset_id)) {
      return {
        inTeam: true,
        teamId: teamData[i].team_id
      };
    } else if (schema === "creature" && teamData[i].data.creatures.includes(item.asset_id)) {
      return {
        inTeam: true,
        teamId: teamData[i].team_id
      };
    }
  }
  return {
    inTeam: false,
    teamId: 'No team!'
  };
}

// vehicles (team related)

function isVehicleTypeAllowed(asset_id, terrain){
  var vehicleType = getImmutableData(asset_id, 'terrain', vehiclesData);
  if(vehicleType === 'water'){
    if(terrain === 'water' || terrain === 'tr_water' || terrain === 'land'){
    return true;
  } else{
    showAlert(`Your ${terrain} vehicle is not allowed on this terrain.`);
    return false;
  }
  } else if(vehicleType === 'land' && terrain === 'water' || vehicleType === 'land' && terrain === 'space' || nav.world === 3 && vehicleType === 'land' && terrain === 'land'){
    showAlert(`Your ${terrain} vehicle is not allowed on this terrain. Land vehicle is not allowed on water or in space.`);
    return false;
  } else if(vehicleType === 'space'){
    return true;
  }
  return vehicleType === terrain;
}

function getMaxCapacity(asset_id, data) {
  for (let i = 0; i < data.length; i++) {
    if (data[i].asset_id === asset_id) {
      return Number(data[i].data.capacity);
    }
  }
  return 0;
}

function findTeamById(teamData, teamid) {
  return teamData.find(team => team.team_id == teamid);
}

function isHouseInTeam(houseId, teamData) {
  if (!teamData || !teamData.length) {
    return {
      inTeam: false,
      teamId: 'No team!'
    };
  }

  for (let i = 0; i < teamData.length; i++) {
    if (teamData[i].data && teamData[i].data.house === houseId) {
      return {
        inTeam: true,
        teamId: teamData[i].team_id
      };
    }
  }

  return {
    inTeam: false,
    teamId: 'No team!'
  };
}

function showInventoryLoadingSpinner(container) {
  container.empty();
  container.append('<br><br><div class="spinner-border text-primary" role="status"><span class="sr-only"></span></div>');
}