var book_templates = [540739, 532611, 714740, 714738, 714751, 714757, 714760, 714756, 714746];
var book_contents = {};
var book_pages = {}; // Store paginated content for each book
var current_book_state = {
  templateId: null,
  currentPage: 0,
  totalPages: 0
};

// Loop through book_templates and extract the html contents / template
for (var i = 0; i < book_templates.length; i++) {
  var templateId = book_templates[i];
  var templateFileName = `../data/books/${templateId}.html`;
  getBookHTML(templateFileName, templateId);
}

function getBookHTML(templateFileName, templateId) {
  fetch(templateFileName)
    .then((response) => response.text())
    .then((template) => {
      book_contents[templateId] = template;
      // Create paginated version of the content
      createBookPages(templateId, template);
    })
    .catch((error) => {
      console.error("Error reading template:", error);
    });
}

function createBookPages(templateId, content) {
  // Create a temporary div to parse the HTML content
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = content;
  
  // Extract all paragraph elements
  const paragraphs = tempDiv.querySelectorAll('p');
  const pages = [];
  
  // Split paragraphs into pages of 3
  for (let i = 0; i < paragraphs.length; i += 3) {
    const pageParagraphs = Array.from(paragraphs).slice(i, i + 3);
    const pageContent = pageParagraphs.map(p => p.outerHTML).join('');
    pages.push(pageContent);
  }
  
  book_pages[templateId] = pages;
}

function displayBookModal(templateId, bookTitle) {
  if (book_contents[templateId] && book_pages[templateId]) {
    // Reset to first page when opening a new book
    current_book_state.templateId = templateId;
    current_book_state.currentPage = 0;
    current_book_state.totalPages = book_pages[templateId].length;
    
    $('#world-description-title').text(bookTitle);
    updateBookPage();
    $('#world-description').css('display', 'block');
  } else {
    console.error('Book content not found for the given ID:', templateId);
  }
}

function updateBookPage() {
  const { templateId, currentPage, totalPages } = current_book_state;
  
  if (!templateId || !book_pages[templateId]) return;
  
  const pageContent = book_pages[templateId][currentPage];
  const pageNumber = currentPage + 1;
  
  // Create navigation controls
  let navigationHTML = '';
  
  if (totalPages > 1) {
    navigationHTML = `
      <div class="book-navigation">
        <button class="btn btn-secondary" onclick="previousBookPage()" ${currentPage === 0 ? 'disabled' : ''}>
          ←
        </button>
        <span class="page-indicator">
          Page ${pageNumber} of ${totalPages}
        </span>
        <button class="btn btn-secondary" onclick="nextBookPage()" ${currentPage === totalPages - 1 ? 'disabled' : ''}>
          →
        </button>
      </div>
    `;
  }
  
  $('#world-description-body').html(pageContent + navigationHTML);
}

function nextBookPage() {
  if (current_book_state.currentPage < current_book_state.totalPages - 1) {
    current_book_state.currentPage++;
    updateBookPage();
  }
}

function previousBookPage() {
  if (current_book_state.currentPage > 0) {
    current_book_state.currentPage--;
    updateBookPage();
  }
}
