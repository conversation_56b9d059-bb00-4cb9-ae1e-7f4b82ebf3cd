// DrawingHUD.js - Complete HUD for drawing canvas with all tools
class DrawingHUD {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.container = new PIXI.Container();
        this.isMenuOpen = false;
        this.tools = {};
        // Prevent drawing when interacting with HUD
        this.container.interactive = true;
        this.container.on('pointerover', () => { this.canvas.isOverHUD = true; });
        this.container.on('pointerout', () => { this.canvas.isOverHUD = false; });
        
        // Create the main HUD container
        this.createHUDContainer();
        
        // Initialize all tools
        this.initializeTools({ ...options, defaultBrushSize: 2 });
        
        // Position HUD at bottom of canvas
        this.container.y = canvas.height - 65;
        
        // Add to canvas
        canvas.container.addChild(this.container);
    }

    createHUDContainer() {
        // Background panel
        this.background = new PIXI.Graphics();
        this.background.beginFill(0xFFFFFF, 0.92);
        this.background.drawRoundedRect(0, 0, this.canvas.width, 48, 8); // Smaller height
        this.background.endFill();
        this.background.lineStyle(1, 0xCCCCCC, 1);
        this.background.drawRoundedRect(0, 0, this.canvas.width, 48, 8);
        this.container.addChild(this.background);

        // Menu toggle button (left side)
        this.createMenuToggle();
    }

    createMenuToggle() {
        this.menuToggle = new PIXI.Graphics();
        this.menuToggle.beginFill(0x4A90E2);
        this.menuToggle.drawRoundedRect(0, 0, 28, 28, 4);
        this.menuToggle.endFill();
        this.menuToggle.lineStyle(1, 0x2E5C8A, 1);
        this.menuToggle.drawRoundedRect(0, 0, 28, 28, 4);
        // Menu icon (hamburger)
        const icon = new PIXI.Graphics();
        icon.beginFill(0xFFFFFF);
        icon.drawRect(6, 8, 16, 2);
        icon.drawRect(6, 13, 16, 2);
        icon.drawRect(6, 18, 16, 2);
        icon.endFill();
        this.menuToggle.addChild(icon);
        this.menuToggle.x = 8;
        this.menuToggle.y = 10;
        this.menuToggle.interactive = true;
        this.menuToggle.cursor = 'pointer';
        this.menuToggle.on('pointerdown', this.toggleMenu.bind(this));
        this.container.addChild(this.menuToggle);
    }

    initializeTools(options) {
        // Create tool containers
        this.toolsContainer = new PIXI.Container();
        this.container.addChild(this.toolsContainer);
        let x = 44; // Start after menu toggle
        const y = 8;
        // Color picker
        this.tools.colorPicker = new ColorPicker(this.toolsContainer, {
            onColorChange: (color) => {
                this.canvas.setColor(color);
                if (this.tools.brushPhysics) {
                    this.tools.brushPhysics.setColor(color);
                }
            },
            defaultColor: options.defaultColor || 0x222222
        });
        this.tools.colorPicker.swatchContainer.x = x;
        this.tools.colorPicker.swatchContainer.y = y;
        this.tools.colorPicker.customPickerContainer.x = x;
        this.tools.colorPicker.customPickerContainer.y = y;
        x += (this.tools.colorPicker.colors.length * 22) + 32; // swatches + custom button + margin
        // Brush selector (reduced width)
        this.tools.brushSelector = new BrushSelector(this.toolsContainer, {
            onBrushChange: (brushType, size) => {
                this.canvas.setBrushSize(size);
                if (this.tools.brushPhysics) {
                    this.tools.brushPhysics.setBrushType(brushType);
                }
            },
            defaultBrush: options.defaultBrush || 'small',
            defaultBrushSize: options.defaultBrushSize || 2,
            trackWidth: 56 // reduced width
        });
        this.tools.brushSelector.brushContainer.x = x + 20;
        this.tools.brushSelector.brushContainer.y = y + 2;
        x += 70; // reduced width + margin
        // Smoothing slider (reduced width)
        this.tools.smoothingSlider = new SmoothingSlider(this.toolsContainer, {
            onSmoothingChange: (smoothing) => {
                this.canvas.setSmoothing(smoothing);
            },
            defaultSmoothing: options.defaultSmoothing || 0.5,
            trackWidth: 56 // reduced width
        });
        this.tools.smoothingSlider.sliderContainer.x = x + 20;
        this.tools.smoothingSlider.sliderContainer.y = y + 2;
        x += 90; // reduced width + margin
        // Action buttons
        this.createActionButtons(x, y + 2);
        // Initialize brush physics if Matter.js is available
        if (typeof Matter !== 'undefined') {
            this.tools.brushPhysics = new BrushPhysics({
                flowRate: 0.1,
                maxParticles: 100
            });
            this.tools.brushPhysics.setBrushType('small');
        }
        // Initialize image saver
        this.tools.imageSaver = new ImageSaver(this.canvas, {
            onSave: (filename) => {
                console.log('Image saved:', filename);
            },
            onError: (error) => {
                console.error('Save error:', error);
            }
        });
        // Initially hide tools
        this.toolsContainer.visible = false;
    }

    createActionButtons(startX, y) {
        let buttonX = startX;
        const buttonSpacing = 40; // less wide
        // Undo button (icon only)
        this.undoButton = this.createButton('', buttonX, y, () => {
            if (this.canvas.undo) this.canvas.undo();
        }, 'images/ui/moveout_icon_small.png', true);
        buttonX += buttonSpacing;
        // Clear button (icon only)
        this.clearButton = this.createButton('', buttonX, y, () => {
            this.canvas.clear();
            if (this.tools.brushPhysics) {
                this.tools.brushPhysics.clearParticles();
            }
        }, 'images/ui/cancel_small_icon.png', true);
        buttonX += buttonSpacing;
        // Save button (icon only)
        this.saveButton = this.createButton('', buttonX, y, () => {
            this.tools.imageSaver.saveWithBackground('drawing.png');
        }, 'images/ui/approve_small_icon.png', true);
        buttonX += buttonSpacing;
        // Copy to clipboard button (fallback text)
        // this.copyButton = this.createButton('Copy', buttonX, y, () => {
        //     this.tools.imageSaver.saveToClipboard();
        // });
    }

    createButton(text, x, y, onClick, iconPath, isSquare) {
        const button = new PIXI.Graphics();
        // White background and gray border
        if (isSquare) {
            button.beginFill(0xFFFFFF);
            button.drawRoundedRect(0, 0, 32, 32, 6);
            button.endFill();
            button.lineStyle(1, 0xCCCCCC, 1);
            button.drawRoundedRect(0, 0, 32, 32, 6);
            button.x = x;
            button.y = y;
        } else {
            button.beginFill(0xFFFFFF);
            button.drawRoundedRect(0, 0, 48, 24, 4);
            button.endFill();
            button.lineStyle(1, 0xCCCCCC, 1);
            button.drawRoundedRect(0, 0, 48, 24, 4);
            button.x = x;
            button.y = y;
        }
        button.interactive = true;
        button.cursor = 'pointer';
        button.on('pointerdown', onClick);
        if (iconPath) {
            // Use PIXI.Sprite for icon
            const icon = PIXI.Sprite.from(iconPath);
            if (isSquare) {
                icon.width = 24;
                icon.height = 24;
                icon.x = 4;
                icon.y = 4;
            } else {
                icon.width = 20;
                icon.height = 20;
                icon.x = 14;
                icon.y = 2;
            }
            button.addChild(icon);
        } else if (text) {
            // Fallback to text for other buttons
            const buttonText = new PIXI.Text(text, {
                fontFamily: 'Arial',
                fontSize: 11,
                fill: 0x4A4A4A
            });
            buttonText.anchor.set(0.5);
            buttonText.x = 24;
            buttonText.y = 12;
            button.addChild(buttonText);
        }
        this.toolsContainer.addChild(button);
        return button;
    }

    toggleMenu() {
        this.isMenuOpen = !this.isMenuOpen;
        this.toolsContainer.visible = this.isMenuOpen;
        this.background.visible = this.isMenuOpen;
        // Animate menu toggle
        if (this.isMenuOpen) {
            this.menuToggle.tint = 0x2E5C8A;
        } else {
            this.menuToggle.tint = 0xFFFFFF;
        }
    }

    update(deltaTime) {
        // Update brush physics if available
        if (this.tools.brushPhysics) {
            this.tools.brushPhysics.update(deltaTime);
        }
    }

    getTools() {
        return this.tools;
    }

    setCanvas(canvas) {
        this.canvas = canvas;
        if (this.tools.imageSaver) {
            this.tools.imageSaver.canvas = canvas;
        }
    }

    show() {
        this.container.visible = true;
    }

    hide() {
        this.container.visible = false;
    }

    resize(width, height) {
        this.container.y = height - 120;
        this.background.clear();
        this.background.beginFill(0xFFFFFF, 0.9);
        this.background.drawRoundedRect(0, 0, width, 120, 10);
        this.background.endFill();
        this.background.lineStyle(2, 0xCCCCCC, 1);
        this.background.drawRoundedRect(0, 0, width, 120, 10);
    }
}

window.DrawingHUD = DrawingHUD; 