/**
 * base-modals.js
 *
 * Core modal creation functions for the Cute Crushies game.
 * Contains the base functionality for creating and managing modals.
 */

// Default configuration for standard modals
const MODAL_DEFAULTS = {
  baseClass: 'modal',
  dialogClass: 'modal-dialog modal-dialog-centered',
  contentClass: 'modal-content',
  headerClass: 'modal-header',
  titleClass: 'modal-title',
  bodyClass: 'modal-body',
  footerClass: 'modal-footer',
  cancelButtonClass: 'btn-secondary',
  cancelButtonText: 'Cancel'
};

/**
 * Factory function to create modals with customizable options
 *
 * @param {Object} config - Configuration object for the modal
 * @param {string} config.title - The title of the modal
 * @param {Object} config.content - The content of the modal (body and footer)
 * @param {string} config.mainContainerId - The ID of the container to append the modal to
 * @param {Object} config.options - Additional options for the modal
 * @param {Function} config.onCancel - Function to execute when the cancel button is clicked
 * @param {boolean} config.addCancelButton - Whether to add a cancel button to the modal
 * @param {Object} config.classes - Custom classes to override the default classes
 * @returns {HTMLElement} - The created modal element
 */
function createModalFactory(config) {
  const {
    title,
    content,
    mainContainerId,
    options = {},
    onCancel,
    addCancelButton = true,
    classes = {}
  } = config;

  // Merge default classes with custom classes
  const modalClasses = {
    ...MODAL_DEFAULTS,
    ...classes
  };

  // Create modal container
  const modalContainer = document.createElement('div');
  modalContainer.classList.add(modalClasses.baseClass);
  modalContainer.id = title.toLowerCase().replace(/\s+/g, '-');

  // Create modal dialog
  const modalDialog = document.createElement('div');
  modalDialog.classList.add(...modalClasses.dialogClass.split(' '));
  modalDialog.setAttribute('role', 'document');
  modalContainer.appendChild(modalDialog);

  // Create modal content
  const modalContent = document.createElement('div');
  modalContent.classList.add(...modalClasses.contentClass.split(' '));
  modalDialog.appendChild(modalContent);

  // Create modal header
  const modalHeader = document.createElement('div');
  modalHeader.classList.add(...modalClasses.headerClass.split(' '));
  modalHeader.id = `${modalContainer.id}-header`;
  modalContent.appendChild(modalHeader);

  // Create modal title
  const modalTitle = document.createElement('h5');
  modalTitle.classList.add(...modalClasses.titleClass.split(' '));
  modalTitle.id = `${modalContainer.id}-title`;
  modalTitle.textContent = title;
  modalHeader.appendChild(modalTitle);

  // Create modal body
  const modalBody = document.createElement('div');
  modalBody.classList.add(...modalClasses.bodyClass.split(' '));
  modalBody.id = `${modalContainer.id}-body`;
  modalBody.innerHTML = content.body || '';
  modalContent.appendChild(modalBody);

  // Create modal footer if content.footer exists
  if (content.footer !== undefined) {
    const modalFooter = document.createElement('div');
    modalFooter.classList.add(...modalClasses.footerClass.split(' '));
    modalFooter.id = `${modalContainer.id}-footer`;
    modalFooter.innerHTML = content.footer || '';

    // Add cancel button if requested
    if (addCancelButton) {
      const cancelButton = document.createElement('button');
      cancelButton.classList.add(...modalClasses.cancelButtonClass.split(' '));
      cancelButton.textContent = modalClasses.cancelButtonText;

      // Set a specific ID for the cancel button based on the modal ID
      const cancelButtonId = `${modalContainer.id}-cancel-button`;
      cancelButton.id = cancelButtonId;

      cancelButton.addEventListener('click', () => {
        if ($(modalContainer).modal) {
          $(modalContainer).modal('hide');
        }
        modalContainer.style.display = 'none';
        if (onCancel) onCancel(options);
      });

      modalFooter.appendChild(cancelButton);
    }

    modalContent.appendChild(modalFooter);
  }

  // Append to main container
  const mainContainer = document.getElementById(mainContainerId);
  if (mainContainer) {
    mainContainer.appendChild(modalContainer);
  } else {
    console.error(`Main container with ID ${mainContainerId} not found`);
  }

  return modalContainer;
}

/**
 * Creates a standard modal with the given title, content, and container ID
 *
 * @param {string} title - The title of the modal
 * @param {Object} modaltext - The content of the modal (body and footer)
 * @param {string} mainId - The ID of the container to append the modal to
 * @returns {HTMLElement} - The created modal element
 */
function createModal(title, modaltext, mainId) {
  return createModalFactory({
    title,
    content: {
      body: modaltext.body,
      footer: modaltext.footer
    },
    mainContainerId: mainId
  });
}
 
function createSpecialModal(title, modaltext, mainId) {
  return createModalFactory({
    title,
    content: {
      body: modaltext.body,
      footer: modaltext.footer
    },
    mainContainerId: mainId,
    onCancel: () => {
      teamSelectedForAdventure = "";
      enableSetAdventure = false;
      
      // Refresh ThreeJS map if it's active and we're in locale view
      if (typeof refreshLocaleView === 'function') {
        refreshLocaleView();
      }
    }
  });
}
 
function showInfoModal(team_id) {
  // In the future, this could use team_id to fetch and display team-specific information
  var message = "This is info on the team:";
  var alertElement = document.getElementById('main-info');
  alertElement.innerHTML = `${message}<br><button class="mt-4 btn-secondary" onclick="this.parentElement.style.display='none'">OK</button>`;
  alertElement.style.display = 'block';
}
