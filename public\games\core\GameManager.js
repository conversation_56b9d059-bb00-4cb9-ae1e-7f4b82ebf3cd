// Game Manager - Handles game initialization, modal management, and game switching
class GameManager {
    constructor() {
        this.currentGame = null;
        this.gameRegistry = new Map();
        this.active_mini_games = [1, 2, 5, 18, 19, 20];
        this.setupEventListeners();
    }

    // Register a game class with the manager
    registerGame(gameName, gameClass) {
        this.gameRegistry.set(gameName, gameClass);
    }

    // Setup event listeners for game controls
    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupButtonListeners();
            this.createGameButtons();
        });
    }

    // Setup button event listeners
    setupButtonListeners() {
        const startButton = document.getElementById('start-button');
        const playAgainButton = document.getElementById('play-again-button');
        const mainMenuButton = document.getElementById('main-menu-button');

        if (startButton) {
            startButton.onclick = () => {
                if (this.currentGame) {
                    this.currentGame.startGame();
                }
            };
        }
        
        if (playAgainButton) {
            playAgainButton.onclick = () => {
                if (this.currentGame) {
                    this.currentGame.startGame();
                }
            };
        }
        
        if (mainMenuButton) {
            mainMenuButton.onclick = () => {
                document.getElementById('game-over-screen').style.display = 'none';
                document.getElementById('start-screen').style.display = 'flex';
            };
        }

        this.setupExitButton();
    }

    // Setup exit button
    setupExitButton() {
        const exitButton = document.getElementById('game-modal-cancel-button');
        if (exitButton) {
            exitButton.textContent = "Exit Game";
            exitButton.onclick = () => this.exitGame();
        } else {
            const gameModalHeader = document.querySelector('#game-modal .modal-header');
            if (gameModalHeader) {
                const fallbackExitButton = document.createElement('button');
                fallbackExitButton.textContent = "Exit Game";
                fallbackExitButton.className = "btn-secondary";
                fallbackExitButton.onclick = () => this.exitGame();
                gameModalHeader.appendChild(fallbackExitButton);
            }
        }
    }

    // Create game buttons for the main menu
    createGameButtons() {
        if (typeof GameUtils !== 'undefined' && GameUtils.instance) {
            GameUtils.instance.createGameButtons();
        } else {
            const gamesContainer = document.getElementById('game-buttons');
            if (!gamesContainer) return;
            gamesContainer.innerHTML = '';
            // Always use the class property as the source of truth
            this.active_mini_games.forEach(gameId => {
                const game = mini_games.find(game => game.id === gameId);
                if (game) {
                    const button = document.createElement('button');
                    button.setAttribute('type', 'button');
                    button.setAttribute('class', 'btn-secondary flex-fill m-1');
                    button.setAttribute('data-value', game.id);
                    const icon = document.createElement('span');
                    icon.className = `icon game-icon`;
                    button.appendChild(icon);
                    button.appendChild(document.createTextNode(game.name));
                    button.addEventListener('click', () => {
                        if (playerData.credits >= 1) {
                            GameManager.displayGameModal(game.id);
                        } else {
                            showAlert("Not enough credits to play (requires 1 credit).");
                        }
                    });
                    gamesContainer.appendChild(button);
                }
            });
        }
    }

    // Display game modal and initialize game
    static displayGameModal(gameId) {
        const game = mini_games.find(game => game.id === gameId);
        if (!GameUtils.validateGameData(gameId)) return;
        if (GameManager.instance.currentGame) {
            GameManager.instance.currentGame.stopGame();
            GameManager.instance.currentGame = null;
        }
        GameUtils.clearCanvas();
        const gameModal = document.getElementById('game-modal');
        const startScreen = document.getElementById('start-screen');
        const gameOverScreen = document.getElementById('game-over-screen');
        if (!gameModal) return;
        GameManager.instance.updateModalContent(game);
        GameUtils.setModalStates(startScreen, gameOverScreen, gameModal);
        if (!GameUtils.checkPixiLoaded()) return;
        GameManager.instance.initializeGame(game);
        GameManager.instance.setupButtonListeners();
    }

    // Update modal content with game information
    updateModalContent(game) {
        const modalTitle = document.getElementById('game-modal-title');
        const startTitle = document.getElementById('start-title');
        if (modalTitle) modalTitle.textContent = game.name;
        if (startTitle) startTitle.textContent = game.name;
        GameUtils.updateGameInstructions(game);
    }

    // Initialize the specific game
    initializeGame(game) {
        try {
            switch (game.name) {
                case "Card Match":
                    this.currentGame = new CardMatch();
                    break;
                case "Treasure Frenzy":
                    this.currentGame = new TreasureFrenzy();
                    break;
                case "Draw the Word":
                    this.currentGame = new DrawTheWord();
                    break;
                case "Bouncy Click":
                    this.currentGame = new BouncyClick();
                    break;
                case "Forest Scavenger":
                    this.currentGame = new ForestScavenger();
                    break;
                case "Laser Defender":
                    this.currentGame = new LaserDefender();
                    break;
                default:
                    GameUtils.showAlert('Game not implemented yet!');
                    return;
            }
        } catch (error) {
            GameUtils.showAlert('Failed to initialize game. Please try again.');
            return;
        }
    }

    // Exit current game
    exitGame() {
        if (this.currentGame) {
            this.currentGame.stopGame();
            this.currentGame = null;
        }
        const gameModal = document.getElementById('game-modal');
        if (gameModal) {
            gameModal.style.display = "none";
        }
    }

    // Get current game instance
    static getCurrentGame() {
        return GameManager.instance?.currentGame;
    }

    // Stop current game
    static stopCurrentGame() {
        if (GameManager.instance?.currentGame) {
            GameManager.instance.currentGame.stopGame();
            GameManager.instance.currentGame = null;
        }
    }

    // Legacy API for compatibility
    static createGameButtons() {
        if (GameManager.instance) {
            GameManager.instance.createGameButtons();
        }
    }
    static initGame() {}
    static stopGame() {}
    static endGame(result) {}
    static updateHUD() {}
}

document.addEventListener('DOMContentLoaded', function() {
    if (typeof GameUtils !== 'undefined') {
        GameUtils.instance = new GameUtils();
    }
    GameManager.instance = new GameManager();
});

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        GameManager
    };
}