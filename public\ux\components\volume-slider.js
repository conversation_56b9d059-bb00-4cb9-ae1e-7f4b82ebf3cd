const VolumeSlider = {
    init() {
        const sliderContainer = document.createElement('div');
        sliderContainer.id = 'volume-control';
        sliderContainer.className = 'volume-control';
        const volumeIcon = document.createElement('img');
        volumeIcon.src = '../images/ui/audio_enable_icon.png';
        volumeIcon.width = 16;
        volumeIcon.className = 'volume-icon';
        const slider = document.createElement('input');
        slider.type = 'range';
        slider.min = 0;
        slider.max = 100;
        slider.value = 0; // Default to 0% volume
        slider.className = 'volume-slider';
        slider.id = 'volume-slider';
        slider.addEventListener('input', function() {
            const volumeLevel = this.value / 100;
            try {
                if (typeof AudioManager !== 'undefined') {
                    // Store current track state before changing volume
                    const wasPlaying = AudioManager.music.currentTrack &&
                                      !AudioManager.music.currentTrack.paused;

                    // Custom volume setting to prevent auto-playing music
                    AudioManager.config.masterVolume = Math.max(0, Math.min(1, volumeLevel));
                    AudioManager.updateAllVolumes();

                    // If volume is set to 0, pause any playing track
                    if (AudioManager.config.masterVolume === 0 && AudioManager.music.currentTrack) {
                        AudioManager.music.currentTrack.pause();
                    }
                    // Only resume playing if it was already playing before
                    else if (wasPlaying && AudioManager.config.masterVolume > 0 &&
                             AudioManager.config.bgmEnabled && AudioManager.music.currentTrack) {
                        AudioManager.music.currentTrack.play().catch(() => {});
                    }
                } else {
                    console.error('[VOLUME DEBUG] AudioManager is not defined when trying to set volume');
                }
            } catch (error) {
                console.error('[VOLUME DEBUG] Error setting volume:', error);
            }
            updateVolumeIcon(volumeLevel);
        });
        function updateVolumeIcon(level) {
            if (level === 0) {
                volumeIcon.src = '../images/ui/audio_disable_icon.png';
            } else {
                volumeIcon.src = '../images/ui/audio_enable_icon.png';
            }
        }
        updateVolumeIcon(0);
        sliderContainer.appendChild(volumeIcon);
        sliderContainer.appendChild(slider);
        this.makeDraggable(sliderContainer);
        document.body.appendChild(sliderContainer);
    },
    makeDraggable(element) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        const handle = document.createElement('div');
        handle.className = 'volume-handle';
        handle.innerHTML = '⋮⋮';
        element.appendChild(handle);
        handle.onmousedown = dragMouseDown;

        function dragMouseDown(e) {
            e.preventDefault();
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }

        function elementDrag(e) {
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            // Only update the Y position, not the X position
            element.style.top = (element.offsetTop - pos2) + "px";
            // Removed the left position update to prevent horizontal movement
        }

        function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
        }
    }
};  
document.addEventListener('DOMContentLoaded', function() {
        const checkAudioManager = function() {
        if (typeof window.AudioManager !== 'undefined') {
            VolumeSlider.init();
        } else {
            setTimeout(checkAudioManager, 500);
        }
    };
    checkAudioManager();
});
