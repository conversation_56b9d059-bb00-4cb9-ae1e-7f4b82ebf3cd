async function getAllZonesFromWorldId(world_id, zonesData) {
  console.log('getting zones based on world id: ' + world_id)
	var zonesObject = [];
	for (var m in zonesData) {
		if (zonesData[m].mapgrid_4 == world_id) {
			zonesObject.push(zonesData[m]);
		}
	}
  console.log('selected this zone object: ' + zonesObject)
	return zonesObject;
}

function getZoneName(world, zone, zoneData) {
    for (var i = 0; i < zoneData.length; i++) {
        if (zoneData[i].mapgrid_4 == world && zoneData[i].mapgrid_16 == zone) {
            return zoneData[i].zone_name;
        }
    }
}

function getZoneInfo(info, world, zone, data) {
	for (var i = 0; i < data.length; i++) {
		if (data[i].mapgrid_4 == world && data[i].mapgrid_16 == zone) {
			return data[i][info];
		}
	}
}

// create zone helper functions
function createZoneSquare(zone) {
  const zoneSquare = $('<div class="zonesquare"></div>');
  zoneSquare.attr('id', zone.mapgrid_16);
  zoneSquare.text(zone.mapgrid_16 + 1);
  return zoneSquare;
}

function addTeamInfoToZone(zoneSquare, zone, nav, teamCount) { 
  if (!teamCount || !teamCount.playerTeams) {
    console.error('ERROR: teamCount or teamCount.playerTeams is undefined');
    return; // Exit early to prevent error
  }

  if (!teamCount.playerTeams[nav.world]) {
    // Initialize the missing structure to prevent errors
    teamCount.playerTeams[nav.world] = {};
    teamCount.playerTeams[nav.world][zone.mapgrid_16] = 0;
  }

  if (!teamCount.playerTeams[nav.world][zone.mapgrid_16]) {
    // Initialize to zero if undefined
    teamCount.playerTeams[nav.world][zone.mapgrid_16] = 0;
  }

  if (!teamCount.otherTeams || !teamCount.otherTeams[nav.world]) {
    // Initialize the missing structure
    if (!teamCount.otherTeams) teamCount.otherTeams = {};
    teamCount.otherTeams[nav.world] = {};
    teamCount.otherTeams[nav.world][zone.mapgrid_16] = 0;
  }

  if (!teamCount.otherTeams[nav.world][zone.mapgrid_16]) { 
    // Initialize to zero if undefined
    teamCount.otherTeams[nav.world][zone.mapgrid_16] = 0;
  }

  const playerTeams = teamCount.playerTeams[nav.world][zone.mapgrid_16];
  const otherTeams = teamCount.otherTeams[nav.world][zone.mapgrid_16]; 

  let teamText = "";

  if (playerTeams > 0) {
    teamText += `<img src="../images/ui/player_icon.png" width="12" /> Player Teams: ${playerTeams}`;
  }

  if (otherTeams > 0) {
    if (teamText) teamText += " ";
    teamText += `<img src="../images/ui/others_icon.png" width="12" /> Other Teams: ${otherTeams}`;
  }

  if (teamText) {
    const teamDiv = $(`<div class="map-presence">${teamText}</div>`);
    zoneSquare.append(teamDiv);
  }
}

function findLocaleInfo(navigation, id, data) {
  for (var i = 0; i < data.length; i++) {
    if (data[i].mapgrid_4 == navigation.world && data[i].mapgrid_16 == navigation.zone) {
      var locales = data[i].data.locales;
      for (var j = 0; j < locales.length; j++) {
        if (locales[j].Locale == id) {
          return locales[j];
        }
      }
    }
  }
}

function getLocationName(zones, mapgrid_4, mapgrid_16, mapgrid_256) {
for (var a in zones) {
  if (zones[a].mapgrid_4 == mapgrid_4 && zones[a].mapgrid_16 == mapgrid_16){
    var locales = zones[a].data.locales;
    for (var i = 0; i < locales.length; i++) {
      if (locales[i].Locale == mapgrid_256){
        return locales[i].Locale_Name;
      }
    }
  }
}
return null;
}

function getTerrainTypeByTeamId(team_id, team_data){
  var team;
  for(var a in team_data){
    if(team_data[a].team_id === team_id){
      team = team_data[a];
          }
    }
  if (!team) {
    throw new Error('Team not found');
  }
  if (team.data.vehicles.terrain) {
    return team.data.vehicles.terrain;
  } else {
    console.log("no terrain attribute found in team jsonb")
    return 'land'; // or some default value
  }
}

function getLocaleData(zoneData, world, zone) {
  console.log('getLocaleData called with:', { world, zone, zoneDataLength: zoneData ? zoneData.length : 'undefined' });
  
  if (!zoneData || !Array.isArray(zoneData)) {
    console.warn('getLocaleData: zoneData is not a valid array');
    return undefined;
  }
  
  for (var i = 0; i < zoneData.length; i++) {
    if (zoneData[i].mapgrid_4 == world && zoneData[i].mapgrid_16 == zone) {
      console.log('getLocaleData: Found matching zone:', zoneData[i]);
      return zoneData[i].data.locales;
    }
  }
  
  console.warn(`getLocaleData: No zone found for world ${world}, zone ${zone}`);
  return undefined;
}

function getLocaleTile(zoneData, grid_4, grid_16, grid_256){
  var locales = getLocaleData(zoneData, grid_4, grid_16);
  if (!locales || !locales[grid_256]) {
    console.warn(`No locale data found for grid coordinates: ${grid_4}, ${grid_16}, ${grid_256}`);
    return null;
  }
  return locales[grid_256];
} 

function addZoneToMap(zoneSquare) {
  const col = $('<div class="col-3 txt-shadow"></div>');
  col.append(zoneSquare);
  $('.map').append(col);
}
