class PhysicsManager {
    constructor(app, hudHeight) {
        this.app = app;
        this.hudHeight = hudHeight;
        this.engine = null;
        this.world = null;
        this.walls = [];
        this.physicsRunning = false;
        this.ballBodies = [];
    }

    setupPhysics() {
        // Create Matter.js engine
        this.engine = Matter.Engine.create({
            gravity: { x: 0, y: 0.1 } // Small gravity for movement
        });
        this.world = this.engine.world;

        // Create boundaries (top, bottom, left, right)
        const wallThickness = 20;
        const width = this.app.screen.width;
        const height = this.app.screen.height;
        
        // Left
        const leftWall = Matter.Bodies.rectangle(-wallThickness / 2, height / 2, wallThickness, height, { isStatic: true });
        // Right
        const rightWall = Matter.Bodies.rectangle(width + wallThickness / 2, height / 2, wallThickness, height, { isStatic: true });
        // Top (just below HUD)
        const topWall = Matter.Bodies.rectangle(width / 2, this.hudHeight - wallThickness / 2, width, wallThickness, { isStatic: true });
        // Bottom (match TreasureFrenzy.js ground position)
        const bottomWall = Matter.Bodies.rectangle(width / 2, height - wallThickness / 2, width, wallThickness, { isStatic: true });
        
        this.walls = [leftWall, rightWall, topWall, bottomWall];
        Matter.World.add(this.world, this.walls);

        this.physicsRunning = false; // Start paused
    }

    runPhysics(windEffect, balls) {
        if (!this.physicsRunning) {
            return;
        }
        
        // Apply wind to all balls
        if (windEffect && windEffect.applyWind) {
            windEffect.applyWind(this.ballBodies);
        }
        
        // Update physics engine
        Matter.Engine.update(this.engine, 1000 / 60);
        
        // Sync PIXI balls with Matter.js bodies
        balls.forEach((ball, i) => {
            const body = this.ballBodies[i];
            if (ball && body && body.position) {
                ball.x = body.position.x;
                ball.y = body.position.y;
                ball.rotation = body.angle;
            }
        });
    }

    // Helper to check overlap between a new ball and existing balls
    isOverlapping(x, y, radius) {
        for (let i = 0; i < this.ballBodies.length; i++) {
            const body = this.ballBodies[i];
            if (!body) continue;
            const dx = x - body.position.x;
            const dy = y - body.position.y;
            const dist = Math.sqrt(dx * dx + dy * dy);
            if (dist < radius * 2 + 2) return true; // 2px buffer
        }
        return false;
    }

    createBallBody(x, y, radius, ballSprite) {
        const density = Math.random() * 0.002 + 0.001; // Random weight
        const restitution = Math.random() * 0.5 + 0.7; // Bounciness
        const friction = Math.random() * 0.05 + 0.01;
        
        const body = Matter.Bodies.circle(x, y, radius, {
            density,
            restitution,
            friction,
            frictionAir: 0.01
        });
        
        // Give random velocity
        const velocityX = (Math.random() - 0.5) * 8;
        const velocityY = (Math.random() - 0.5) * 8;
        Matter.Body.setVelocity(body, {
            x: velocityX,
            y: velocityY
        });
        
        // Give random spin
        Matter.Body.setAngularVelocity(body, (Math.random() - 0.5) * 0.2);
        
        // Store reference to PIXI ball
        body.ballSprite = ballSprite;
        
        Matter.World.add(this.world, body);
        this.ballBodies.push(body);
        
        return body;
    }

    removeBallBody(body) {
        if (body) {
            Matter.World.remove(this.world, body);
            const index = this.ballBodies.indexOf(body);
            if (index !== -1) {
                this.ballBodies.splice(index, 1);
            }
        }
    }

    removeAllBallBodies() {
        this.ballBodies.forEach(body => {
            if (body) Matter.World.remove(this.world, body);
        });
        this.ballBodies = [];
    }

    startPhysics() {
        this.physicsRunning = true;
    }

    stopPhysics() {
        this.physicsRunning = false;
    }

    cleanup() {
        this.stopPhysics();
        this.removeAllBallBodies();
        if (this.engine) {
            Matter.Engine.clear(this.engine);
        }
    }

    getBallBodies() {
        return this.ballBodies;
    }

    setBallBodies(bodies) {
        this.ballBodies = bodies;
    }
}

// Export for use in other modules
window.PhysicsManager = PhysicsManager; 