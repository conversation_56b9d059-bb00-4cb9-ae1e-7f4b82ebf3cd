const pool = require("./db");
const queries = require("./queries");

const updatePlayerCredits = async (req, res) => {
  try {
    const id = req.params.id;
    const { credits, amount, operation } = req.body;

    // First, get the current player credits
    const playerResult = await pool.query(queries.getby.getPlayerById, [id]);
    if (playerResult.rows.length === 0) {
      return res.status(404).send("Player not found");
    }

    const currentCredits = Number(playerResult.rows[0].credits);
    let newCredits;

    // Handle different operations
    if (operation === 'subtract' && amount !== undefined) {
      // If subtracting, check if player has enough credits
      const amountToSubtract = Number(amount);

      if (isNaN(amountToSubtract) || amountToSubtract < 0) {
        return res.status(400).send("Invalid amount to subtract");
      }

      if (currentCredits < amountToSubtract) {
        console.log(`[updatePlayerCredits] Insufficient credits for player ${id}: has ${currentCredits}, needs ${amountToSubtract}`);
        return res.status(400).send("Insufficient credits");
      }

      newCredits = currentCredits - amountToSubtract;
    } else if (operation === 'add' && amount !== undefined) {
      // If adding credits
      const amountToAdd = Number(amount);

      if (isNaN(amountToAdd) || amountToAdd < 0) {
        return res.status(400).send("Invalid amount to add");
      }

      newCredits = currentCredits + amountToAdd;
    } else if (credits !== undefined) {
      // Direct balance update (legacy support)
      newCredits = Number(credits);

      if (isNaN(newCredits)) {
        return res.status(400).send("Invalid credits value");
      }
    } else {
      return res.status(400).send("Missing required parameters");
    }

    // Final check to ensure balance is not negative
    if (newCredits < 0) {
      console.log(`[updatePlayerCredits] Rejected negative balance update for player ${id}: ${newCredits}`);
      return res.status(400).send("Credits balance cannot be negative");
    }

    // Proceed with update
    const updateResults = await pool.query(queries.up.updatePlayerCredits, [newCredits, id]);

    console.log(`[updatePlayerCredits] Updated credits for player ${id}: ${currentCredits} -> ${newCredits}`);
    return res.status(200).send("Player credits updated successfully");
  } catch (error) {
    console.error("Error updating player credits:", error);
    return res.status(500).send(error.message || "Internal Server Error");
  }
};

const updatePlayerNectar = async (req, res) => {
  try {
    const id = req.params.id;
    const { nectar, amount, operation } = req.body;

    // First, get the current player nectar
    const playerResult = await pool.query(queries.getby.getPlayerById, [id]);
    if (playerResult.rows.length === 0) {
      return res.status(404).send("Player not found");
    }

    const currentNectar = Number(playerResult.rows[0].nectar);
    let newNectar;

    // Handle different operations
    if (operation === 'subtract' && amount !== undefined) {
      // If subtracting, check if player has enough nectar
      const amountToSubtract = Number(amount);

      if (isNaN(amountToSubtract) || amountToSubtract < 0) {
        return res.status(400).send("Invalid amount to subtract");
      }

      if (currentNectar < amountToSubtract) {
        console.log(`[updatePlayerNectar] Insufficient nectar for player ${id}: has ${currentNectar}, needs ${amountToSubtract}`);
        return res.status(400).send("Insufficient nectar");
      }

      newNectar = currentNectar - amountToSubtract;
    } else if (operation === 'add' && amount !== undefined) {
      // If adding nectar
      const amountToAdd = Number(amount);

      if (isNaN(amountToAdd) || amountToAdd < 0) {
        return res.status(400).send("Invalid amount to add");
      }

      newNectar = currentNectar + amountToAdd;
    } else if (nectar !== undefined) {
      // Direct balance update (legacy support)
      newNectar = Number(nectar);

      if (isNaN(newNectar)) {
        return res.status(400).send("Invalid nectar value");
      }
    } else {
      return res.status(400).send("Missing required parameters");
    }

    // Final check to ensure balance is not negative
    if (newNectar < 0) {
      console.log(`[updatePlayerNectar] Rejected negative balance update for player ${id}: ${newNectar}`);
      return res.status(400).send("Nectar balance cannot be negative");
    }

    // Proceed with update
    const updateResults = await pool.query(queries.up.updatePlayerNectar, [newNectar, id]);

    console.log(`[updatePlayerNectar] Updated nectar for player ${id}: ${currentNectar} -> ${newNectar}`);
    return res.status(200).send("Player nectar updated successfully");
  } catch (error) {
    console.error("Error updating player nectar:", error);
    return res.status(500).send(error.message || "Internal Server Error");
  }
};

const updatePlayerGXP = async (req, res) => {
  try {
    const id = req.params.id;
    const { gxp, amount, operation } = req.body;

    // First, get the current player GXP
    const playerResult = await pool.query(queries.getby.getPlayerById, [id]);
    if (playerResult.rows.length === 0) {
      return res.status(404).send("Player not found");
    }

    const currentGXP = Number(playerResult.rows[0].gxp);
    let newGXP;

    // Handle different operations
    if (operation === 'subtract' && amount !== undefined) {
      // If subtracting, check if player has enough GXP
      const amountToSubtract = Number(amount);

      if (isNaN(amountToSubtract) || amountToSubtract < 0) {
        return res.status(400).send("Invalid amount to subtract");
      }

      if (currentGXP < amountToSubtract) {
        console.log(`[updatePlayerGXP] Insufficient GXP for player ${id}: has ${currentGXP}, needs ${amountToSubtract}`);
        return res.status(400).send("Insufficient GXP");
      }

      newGXP = currentGXP - amountToSubtract;
    } else if (operation === 'add' && amount !== undefined) {
      // If adding GXP
      const amountToAdd = Number(amount);

      if (isNaN(amountToAdd) || amountToAdd < 0) {
        return res.status(400).send("Invalid amount to add");
      }

      newGXP = currentGXP + amountToAdd;
    } else if (gxp !== undefined) {
      // Direct balance update (legacy support)
      newGXP = Number(gxp);

      if (isNaN(newGXP)) {
        return res.status(400).send("Invalid GXP value");
      }
    } else {
      return res.status(400).send("Missing required parameters");
    }

    // Final check to ensure balance is not negative
    if (newGXP < 0) {
      console.log(`[updatePlayerGXP] Rejected negative balance update for player ${id}: ${newGXP}`);
      return res.status(400).send("GXP balance cannot be negative");
    }

    // Proceed with update
    const gxpUpdateResults = await pool.query(queries.up.updatePlayerGXP, [newGXP, id]);

    console.log(`[updatePlayerGXP] Updated GXP for player ${id}: ${currentGXP} -> ${newGXP}`);
    return res.status(200).send("Player GXP updated successfully");
  } catch (error) {
    console.error("Error updating player GXP:", error);
    return res.status(500).send(error.message || "Internal Server Error");
  }
};

module.exports = {
  updatePlayerCredits,
  updatePlayerNectar,
  updatePlayerGXP
};
