// get all rows
const getGameStats = "SELECT * FROM gamestats";
const getPlayers = "SELECT * FROM new_players";
const getTeams = "SELECT * FROM teams";
const getAdventures = "SELECT * FROM adventures";
const getNaps = "SELECT * FROM naps";
const getHouses = "SELECT * FROM houses";
const getRewards = "SELECT * FROM rewards";
const getEscrow = "SELECT * FROM escrow";
const getMapZones = "SELECT * FROM map_zones ORDER BY mapgrid_16 ASC";
const getGameLog = "SELECT * FROM os_log ORDER BY created_at DESC";
const getRentals = "SELECT * FROM rentals";
const getGlobalLimits = "SELECT * FROM os_global_cap";
const getPlayerCaps = "SELECT * FROM os_player_cap";
const getPlayerCapsByWaxId = "SELECT * FROM os_player_cap WHERE wax_id = $1";
const getSystemLogs = "SELECT * FROM os_msg ORDER BY created_at DESC";

module.exports = {
	getMapZones,
	getGameStats,
	getPlayers,
	getTeams,
	getAdventures,
	getNaps,
	getHouses,
	getRewards,
	getEscrow,
	getGameLog,
	getRentals,
	getGlobalLimits,
	getPlayerCaps,
	getPlayerCapsByWaxId,
	getSystemLogs
}
