<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reward Caps Dashboard</title>
  <link href="../images/ui/logos/cc-logo-25.png" rel="icon" type="image/png">
  <link href="https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;600&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    :root {
      --windows-blue: #0078D7;
      --windows-light-blue: #E6F2FF;
      --windows-gray: #F0F0F0;
      --windows-dark-gray: #E0E0E0;
      --windows-border: #D1D1D1;
      --alert-red: #E81123;
      --alert-red-light: #FFEBEE;
      --success-green: #107C10;
      --success-green-light: #E6F4E6;
    }

    body {
      font-family: 'Segoe UI', sans-serif;
      background-color: var(--windows-gray);
      margin: 0;
      padding: 20px;
      color: #333;
    }

    .dashboard-container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .dashboard-header {
      background-color: var(--windows-blue);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .dashboard-header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }

    .dashboard-content {
      padding: 20px;
    }

    .tab-container {
      margin-bottom: 20px;
    }

    .tab-buttons {
      display: flex;
      border-bottom: 1px solid var(--windows-border);
      margin-bottom: 20px;
    }

    .tab-button {
      padding: 10px 20px;
      background-color: var(--windows-gray);
      border: none;
      border-bottom: 3px solid transparent;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.2s;
    }

    .tab-button:hover {
      background-color: var(--windows-dark-gray);
    }

    .tab-button.active {
      background-color: white;
      border-bottom: 3px solid var(--windows-blue);
      color: var(--windows-blue);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .caps-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    .caps-table th {
      background-color: var(--windows-light-blue);
      padding: 12px 15px;
      text-align: left;
      font-weight: 600;
      border-bottom: 2px solid var(--windows-border);
    }

    .caps-table td {
      padding: 12px 15px;
      border-bottom: 1px solid var(--windows-border);
    }

    .caps-table tr:hover {
      background-color: var(--windows-gray);
    }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-green {
      background-color: var(--success-green);
    }

    .status-red {
      background-color: var(--alert-red);
    }

    .row-green {
      background-color: var(--success-green-light);
    }

    .row-red {
      background-color: var(--alert-red-light);
    }

    .alert-icon {
      color: var(--alert-red);
      margin-left: 5px;
    }

    .progress {
      height: 8px;
      margin-top: 5px;
    }

    .log-container {
      background-color: var(--windows-light-blue);
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
      position: relative;
    }

    .log-navigation {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
    }

    .log-nav-button {
      background-color: var(--windows-blue);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
    }

    .log-nav-button:disabled {
      background-color: var(--windows-dark-gray);
      cursor: not-allowed;
    }

    .refresh-button {
      background-color: var(--windows-blue);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 15px;
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
      }

      .tab-buttons {
        flex-wrap: wrap;
      }

      .tab-button {
        flex: 1 0 auto;
        text-align: center;
      }

      .caps-table {
        font-size: 14px;
      }

      .caps-table th, .caps-table td {
        padding: 8px 10px;
      }
    }
  </style>
</head>
<body>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>Reward Caps Dashboard</h1>
      <button class="refresh-button" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i> Refresh Data
      </button>
    </div>

    <div class="dashboard-content">
      <div class="log-container">
        <h3>System Logs</h3>
        <div id="current-log">Loading latest system log...</div>
        <div class="log-navigation">
          <button class="log-nav-button" id="prev-log" onclick="navigateLog(-1)">
            <i class="fas fa-chevron-left"></i> Previous
          </button>
          <span id="log-counter">Log 1 of 1</span>
          <button class="log-nav-button" id="next-log" onclick="navigateLog(1)">
            Next <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>

      <div class="tab-container">
        <div class="tab-buttons">
          <button class="tab-button active" onclick="openTab('global-caps')">Global Caps</button>
          <button class="tab-button" onclick="openTab('player-caps')">Player Caps</button>
          <button class="tab-button" onclick="openTab('system-logs')">All System Logs</button>
        </div>

        <div id="global-caps" class="tab-content active">
          <h2>Global Reward Limits</h2>
          <table class="caps-table" id="global-caps-table">
            <thead>
              <tr>
                <th>Status</th>
                <th>Reward Type</th>
                <th>Current Count</th>
                <th>Max Limit</th>
                <th>Progress</th>
                <th>Expires At</th>
                <th>Duration (hours)</th>
              </tr>
            </thead>
            <tbody id="global-caps-body">
              <tr>
                <td colspan="7">Loading data...</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div id="player-caps" class="tab-content">
          <h2>Player Reward Limits</h2>
          <div class="mb-3">
            <input type="text" class="form-control" id="player-search" placeholder="Search by WAX ID...">
          </div>
          <table class="caps-table" id="player-caps-table">
            <thead>
              <tr>
                <th>Status</th>
                <th>WAX ID</th>
                <th>Reward Type</th>
                <th>Current Count</th>
                <th>Max Limit</th>
                <th>Progress</th>
                <th>Expires At</th>
              </tr>
            </thead>
            <tbody id="player-caps-body">
              <tr>
                <td colspan="7">Loading data...</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div id="system-logs" class="tab-content">
          <h2>System Logs</h2>
          <div class="mb-3">
            <div class="row">
              <div class="col-md-4">
                <select class="form-select" id="log-type-filter">
                  <option value="">All Types</option>
                  <option value="INFO">INFO</option>
                  <option value="WARNING">WARNING</option>
                  <option value="ERROR">ERROR</option>
                  <option value="CRITICAL">CRITICAL</option>
                </select>
              </div>
              <div class="col-md-4">
                <select class="form-select" id="log-category-filter">
                  <option value="">All Categories</option>
                  <option value="REWARD">REWARD</option>
                  <option value="LIMIT">LIMIT</option>
                  <option value="SYSTEM">SYSTEM</option>
                </select>
              </div>
              <div class="col-md-4">
                <select class="form-select" id="log-reward-filter">
                  <option value="">All Reward Types</option>
                  <option value="GXP">GXP</option>
                  <option value="DUST">DUST</option>
                  <option value="NFT">NFT</option>
                  <option value="NECTAR">NECTAR</option>
                </select>
              </div>
            </div>
          </div>
          <table class="caps-table" id="system-logs-table">
            <thead>
              <tr>
                <th>Type</th>
                <th>Category</th>
                <th>Reward Type</th>
                <th>Message</th>
                <th>Created At</th>
              </tr>
            </thead>
            <tbody id="system-logs-body">
              <tr>
                <td colspan="5">Loading data...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="../data/urls.js"></script>
  <script src="caps-dashboard.js"></script>
</body>
</html>
