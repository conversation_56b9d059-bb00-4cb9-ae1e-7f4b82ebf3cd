
#journal-button, #stats-button{
  background:rgba(255, 195, 143, 0.83);
  border-radius:6px;
}
#journal-button.active-tab, #stats-button.active-tab{
  color: inherit;
  background: #fbeec2;
}

/* Card Background */
.player-log-bg {
    padding: 1.5em;
    background: #9e683c;
    background-image: url('../images/ui/bg/wood_panel.png');
    box-shadow:
        inset 0 0 200px rgba(193, 69, 0, 0.5),
        0 4px 12px rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    border: 2px solid #b98b70;
}

/* Log List */
.player-log-list {
    height: 240px;
    overflow: auto;
    padding-top: 1em; 
} 

.player-log-item {
  border: 18px solid;
  border-image: url('../images/ui/bg/log-item.png') 18 round;
  background: #fee28a; 
  transition: transform 0.08s ease-in-out;
}

.player-log-item:hover {
  transform: translateY(2px);
} 

.player-log-header {
    font-weight: bold;
    margin-bottom: 5px;
    margin-left: 5px;
    color: #3a2616;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

/* Stats List */
.player-stats-list {
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 10px;
}

.player-stats-list ul{
  list-style-type: none;
 padding-left: 0;
}

.player-stats-list li {
    list-style-type: none;
    padding: 5px 5px 3px 32px;
    background-image: url('../images/ui/boost_icon.png');
    background-repeat: no-repeat;
    background-position: 10px center;
    background-size: 16px 16px;
    background-color: rgba(255, 195, 143, 0.83);
    border-top: 3px solid #bd5905;
    border-bottom: 2px solid #ff9a54;
    border-radius: 6px;
    transition: transform 0.08s ease-in-out;
}

.player-stats-list li:hover {
    transform: translateY(2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 205, 153, 0.9);
}

.player-stats-list li[style*="background:none"] {
  background: none !important;
  background-image: none !important;
  padding-left: 0 !important;
}

.player-stats-list .stat-row {
  background-color: rgba(255,195,143,0.83);
  border-top: 3px solid #bd5905;
  border-bottom: 2px solid #ff9a54;
  border-radius: 6px;
  padding-left: 5px;
  background-image: none !important;
  display: flex;
  align-items: center; 
  margin-left: -5px;
  padding: 5px 5px 5px 16px;
} 
