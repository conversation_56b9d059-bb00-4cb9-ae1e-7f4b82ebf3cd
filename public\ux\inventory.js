function createBaseItemContainer(item, selectedSchema) {
    const div = $("<div>", { id: item.asset_id, class: "inventory-item" });
    const img_url = getAssetImage(item.asset_id, [item]);
    const img = $("<img>", { src: img_url });
    applyImageClassBySchema(img, selectedSchema);
    div.append(img);
    return div;
}

function applyImageClassBySchema(img, schema) {
    switch (schema) {
        case 'vehicles':
            img.addClass("img-vehicle");
            break;
        case 'houses':
            img.addClass("img-house");
            break;
        case 'creature':
            img.addClass("img-creature");
            break;
        default:
            img.addClass("img-item");
            break;
    }
}

function createItemDetails(item) {
    const details = $("<div>", { class: "item-details" });
    const name = $("<span>", { class: "asset-name" }).html(item.name + ' #' + item.template_mint);
    details.append(name);
    return { details, name };
}

function addBookElements(item, details, buttons) {
    const bookButton = createStandardButton({
        text: 'Read',
        icon: '../images/ui/world_scroll.png',
        iconProps: { width: '12px' },
        dataAttr: 'book-id',
        dataValue: item.template.template_id,
        onClick: function (e) {
            e.stopPropagation();
            const templateId = $(this).attr('data-book-id');
            if (templateId) {
                displayBookModal(templateId, item.name);
            }
        }
    });
    buttons.append(bookButton);
}

function addTeamElements(item, div, details, name, buttons) {
    const schema = item.schema.schema_name;
    const inTeam = isInTeam(schema, item, myTeams);

    try {
        if (inTeam.inTeam) { 
            name.append($("<span class='in-team'>").html(`Team #${inTeam.teamId}`));
            const dialogBtn = createDialogButton(item);
            buttons.append(dialogBtn);
        } else {
            if (schema === 'vehicles') {
                const ccbtn = createTeamButton(item, div, addToTeam);
                buttons.append(ccbtn);
            } else if (schema === 'creature') {
                div.on("click", () => {
                    const id = div.attr("id");
                    AudioManager.playCreatureSound(item.data.species);
                    addToTeam(item.schema.schema_name, item, id);
                });
                
                // Add dialog button for creatures
                const dialogBtn = createDialogButton(item);
                buttons.append(dialogBtn);
            }
        }
    } catch (error) {
        console.error(error);
    }
}

function createInventoryItemElement(item, selectedSchema) {
    const div = createBaseItemContainer(item, selectedSchema);
    const { details, name } = createItemDetails(item);
    const buttons = $("<div>", { class: "buttons" });
    const schema = item.schema.schema_name;
    if (schema === 'items' && item.data.type === 'book') {
        addBookElements(item, details, buttons);
    }
    if (schema === 'vehicles' || schema === 'creature' || schema === 'houses') {
        addTeamElements(item, div, details, name, buttons);
    }
    div.append(details);
    div.append(buttons);
    return div;
}


function renderInventoryItems(container, items, selectedSchema) {
    container.empty();
    if (items.length === 0) {
        container.append(createInventoryStatusMessage('No items found', selectedSchema));
        return;
    }
    items.forEach(item => {
        const itemElement = createInventoryItemElement(item, selectedSchema);
        container.append(itemElement);
    });
}

async function displayInventory(selectedSchema, insertionDiv, filterCondition = null) {
    const containerDiv = $(`#${insertionDiv}`);
    showInventoryLoadingSpinner(containerDiv);

    // Add filter dropdown if it doesn't exist
    if ($('#inventory-filter-dropdown').length === 0) {
        addInventoryFilterDropdown(selectedSchema, insertionDiv);
    } else {
        // Update filter options based on selected schema
        updateFilterDropdownOptions(selectedSchema, insertionDiv);
    }

    const filteredItems = await fetchAndFilterInventoryItems(selectedSchema, filterCondition);
    console.log('Items to render in displayInventory:', filteredItems.map(item => ({ id: item.id, template_mint: item.template_mint })));
    renderInventoryItems(containerDiv, filteredItems, selectedSchema);
}

function setSelectedSchema(value) {
    $('#inventory button').removeClass('inventory-active-view');
    $(`#inventory button[value="${value}"]`).addClass('inventory-active-view');
    currentInventoryView = value;
    displayInventory(value, 'general-inventory');
}

function getAdventureLocationInfo(adventure) {
  // Debug logging to understand the data
  console.log('getAdventureLocationInfo called with adventure:', {
    adventure_id: adventure.adventure_id,
    mapgrid_4: adventure.mapgrid_4,
    mapgrid_16: adventure.mapgrid_16,
    mapgrid_256: adventure.mapgrid_256
  });
  
  console.log('allZones status:', {
    exists: typeof allZones !== 'undefined',
    isArray: Array.isArray(allZones),
    length: allZones ? allZones.length : 'undefined'
  });

  const locationName = getLocationName(allZones, adventure.mapgrid_4, adventure.mapgrid_16, adventure.mapgrid_256);
  const tile = getLocaleTile(allZones, adventure.mapgrid_4, adventure.mapgrid_16, adventure.mapgrid_256);
  
  console.log('Location info results:', {
    locationName,
    tile: tile ? { Locale: tile.Locale, Tile: tile.Tile } : null
  });

  return {
    locationName: locationName || 'Unknown Location',
    tile: tile,
    formattedLocation: `World ${adventure.mapgrid_4 + 1}, Zone ${adventure.mapgrid_16 + 1}, Sq# ${adventure.mapgrid_256 + 1}`
  };
}

function createAdventureBaseContainer(adventure) {
  const div = $("<div>")
    .attr("id", adventure.adventure_id)
    .addClass("inventory-item")
    .attr("data-world", adventure.mapgrid_4)
    .attr("data-zone", adventure.mapgrid_16)
    .attr("data-locale", adventure.mapgrid_256);

  div.on('click', function() {
    nav.world = $(this).attr('data-world');
    nav.zone = $(this).attr('data-zone');
    displayZones(nav.world);
  });

  const img = $("<img>", { class: "img-ui-icon", src: "images/ui/adventure_icon.png" });
  const buttons = $("<div>", { class: "buttons" });
  const details = $("<div>", { class: "item-details" });

  div.append(img);
  div.append(details);

  const name = $("<span>", { class: 'adventure-id' }).html('Adventure ID# ' + adventure.adventure_id);
  details.append(name);

  return { div, details, buttons };
}

function createInProgressAdventureElements(adventure, container, locationInfo) {
  const { div, details, buttons } = container;
  const { locationName, tile, formattedLocation } = locationInfo;

  // Create tile image - handle null tile case
  let tile_img;
  if (tile && tile.Locale && tile.Tile) {
    tile_img = $('<div>', { class: "mapsquare" })
      .attr('id', tile.Locale + '-ux')
      .addClass(tile.Tile);
  } else {
    // Fallback for missing tile data
    tile_img = $('<div>', { class: "mapsquare" })
      .attr('id', 'unknown-locale-ux')
      .addClass('unknown-tile')
      .text('?');
    console.warn(`Missing tile data for adventure ${adventure.adventure_id}, using fallback`);
  }

  // Create team info (show team name if available)
  let teamName = '';
  if (typeof myTeams !== 'undefined' && Array.isArray(myTeams)) {
    const teamObj = myTeams.find(team => team.team_id === adventure.team_id || team.id === adventure.team_id);
    teamName = teamObj ? teamObj.team_name : '';
  }
  const teamDisplay = teamName ? `${teamName} (Team #${adventure.team_id})` : `Team #${adventure.team_id}`;
  const team = $("<span>", { class: 'team-id' })
    .html(teamDisplay + ' is travelling to ' + locationName);
  details.append(team);
  details.append(tile_img);

  // Create timer
  const timeLeft = getTimeLeft(adventure.current_steps, adventure.init_steps);
  const timer = $("<span>", { class: 'team-id' }).html(timeLeft);
  details.append(timer);

  // Create location info
  const location = $("<span>", { class: 'team-destination' }).html(formattedLocation);
  details.append(location);

  // Create progress bar
  const bar = createProgressBar(adventure.status, Number(adventure.current_steps), Number(adventure.init_steps));
  div.append(bar);

  // Create buttons
  const cancelButton = createAdventureCancelButton(adventure.adventure_id, deleteAdventure);
  const viewButton = createViewButton(adventure, displayTeamViewModal);
  buttons.append(cancelButton, viewButton);
}

function createCompletedAdventureElements(adventure, container, locationInfo) {
  const { details, buttons } = container;
  const { locationName, tile, formattedLocation } = locationInfo;

  // Create tile image - handle null tile case
  let tile_img;
  if (tile && tile.Locale && tile.Tile) {
    tile_img = $('<div>', { class: "mapsquare" })
      .attr('id', tile.Locale + 'claim icon')
      .addClass(tile.Tile);
  } else {
    // Fallback for missing tile data
    tile_img = $('<div>', { class: "mapsquare" })
      .attr('id', 'unknown-locale-claim')
      .addClass('unknown-tile')
      .text('?');
    console.warn(`Missing tile data for completed adventure ${adventure.adventure_id}, using fallback`);
  }

  // Create team info (show team name if available)
  let teamName = '';
  if (typeof myTeams !== 'undefined' && Array.isArray(myTeams)) {
    const teamObj = myTeams.find(team => team.team_id === adventure.team_id || team.id === adventure.team_id);
    teamName = teamObj ? teamObj.team_name : '';
  }
  const teamDisplay = teamName ? `${teamName} (Team #${adventure.team_id})` : `Team #${adventure.team_id}`;
  const team = $("<span>", { class: 'team-id' })
    .html(teamDisplay + ' has arrived at ' + locationName);
  details.append(team);
  details.append(tile_img);
  const location = $("<span>", { class: 'team-destination' }).html(formattedLocation);
  details.append(location);
  const claimButton = createClaimButton(adventure.adventure_id, updateRewardStatus, deleteAdventure);
  buttons.append(claimButton);
}

function createAdventureItem(adventure) {
  const locationInfo = getAdventureLocationInfo(adventure);
  const container = createAdventureBaseContainer(adventure);

  if (adventure.status === "In Progress") {
    createInProgressAdventureElements(adventure, container, locationInfo);
  } else if (adventure.status === "Complete") {
    createCompletedAdventureElements(adventure, container, locationInfo);
  }
  // No other statuses should exist for adventures

  container.div.append(container.buttons);
  return container.div;
}

async function fetchModalInventoryData(selectedSchema, modalType) {
  const items = await fetchTable(wax.userAccount, 'cutecrushies', selectedSchema);
  let occupiedHouses = [];
  if(modalType === 'selectHouse') {
    allTeams = await getTeams();
    occupiedHouses = await getOccupiedHouses(allTeams);
  }
  return { items, occupiedHouses };
}

function createModalHouseItem(item, teamId, modalType) {
  const $div = $("<div>", { class: "inventory-item" });
  const $img = $("<img>", {
    src: `https://ipfs.io/ipfs/${item.data.img}`,
    class: "img-house"
  });

  $div.append($img);

  const $details = $("<div>", { class: "item-details" });
  const $name = $("<span>", { class: "asset-name" }).html(item.name + ' #' + item.template_mint);
  $details.append($name);
  $div.append($details);

  if (modalType === 'selectHouse') {
    const $setHouseButton = createHouseMoveInButton(item.asset_id, teamId);
    $div.append($setHouseButton);
  }

  return $div;
}

function renderModalInventoryItems(container, items, occupiedHouses, teamId, modalType) {
  container.empty();
  
  // Filter out occupied houses
  const availableItems = items.filter(item => !occupiedHouses.includes(item.asset_id));
  
  if (availableItems.length === 0) {
    container.append(createInventoryStatusMessage('No available items found'));
    return;
  }
  
  availableItems.forEach(item => {
    const itemElement = createModalHouseItem(item, teamId, modalType);
    container.append(itemElement);
  });
}

async function displayModalInventory(teamId, modalType, selectedSchema) {
  const $inventory = $("#modal-inventory");
  showInventoryLoadingSpinner($inventory);
  const { items, occupiedHouses } = await fetchModalInventoryData(selectedSchema, modalType);
  renderModalInventoryItems($inventory, items, occupiedHouses, teamId, modalType);
}
