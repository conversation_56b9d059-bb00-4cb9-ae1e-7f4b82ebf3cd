try {=
    const {
        shuffleFisherYates,
        getRandomElem
    } = require("./misc");
    // Make these functions available globally within this module
    global.shuffleFisherYates = shuffleFisherYates;
    global.getRandomElem = getRandomElem;
} catch (error) {
    // List files in current directory to help debug
    const fs = require('fs');
}

function getRandomRewardFromPool(poolName) {
    // Check if shuffleFisherYates is available
    if (typeof shuffleFisherYates === 'function') {
        shuffleFisherYates(poolName);
    } else {
        // Inline implementation of Fisher-Yates shuffle
        let i = poolName.length;
        while (i--) {
            const ri = Math.floor(Math.random() * (i + 1));
            [poolName[i], poolName[ri]] = [poolName[ri], poolName[i]];
        }
    }

    // Check if getRandomElem is available
    let x;
    if (typeof getRandomElem === 'function') {
        x = getRandomElem(poolName);
    } else {
        // Inline implementation of getRandomElem
        x = poolName[Math.round(Math.random() * (poolName.length - 1))];
    }

    if (x == null) {
        x = poolName[0];
    }
    return x;
}

function getStandardDustReward() {
    var dustMaximum = [3, 5, 10];

    // Check if getRandomElem is available
    let r;
    if (typeof getRandomElem === 'function') {
        r = getRandomElem(dustMaximum);
    } else {
        // Inline implementation of getRandomElem
        r = dustMaximum[Math.round(Math.random() * (dustMaximum.length - 1))];
    }

    if (r == null) {
        r = 1;
    }
    return r;
}

function getStandardGXPReward() {
    var gxpMax = [5, 10, 1000];

    // Check if getRandomElem is available
    let r;
    if (typeof getRandomElem === 'function') {
        r = getRandomElem(gxpMax);
    } else {
        // Inline implementation of getRandomElem
        r = gxpMax[Math.round(Math.random() * (gxpMax.length - 1))];
    }

    return r;
}

async function calculateChance(difficulty) {
    var chanceWeight = [0.08, 0.15, 0.16, 0.19, 0.21];
    var a;
    var result = Math.floor(Math.random() * 25) + chanceWeight[difficulty] * 33;
    if (result > 26) {
        a = "NFT";
    } else {
        a = "DUST";
    }
    return a;
}

module.exports = {
    getRandomRewardFromPool,
    getStandardDustReward,
    getStandardGXPReward,
    calculateChance
};