.btn-primary {
  background-color: orange;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1em;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  background-color: darkorange;
}

.btn-primary img {
  width: 16px;
  height: 16px;
}
 

/* we use standard-btn standard-small to style in our gray default button style  */
.btn-secondary {
  border: 18px solid;
  border-image: url('../images/ui/buttons/default_button.png') 18 round; 
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
  background: #eee;
  color: #333;
  user-select: none;
  transition: all 0.08s cubic-bezier(0.4, 0, 0.2, 1.5);  
  margin: 10px;
  max-width: fit-content;
  /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
}

.btn-secondary:active {
  border-image: url('../images/ui/buttons/default_button_pressed.png') 18 round;
  transform: scale(0.92) translateY(3px);  
  background: #eee;
  /* box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1); */
}
 
/* .standard-small { width: 50px; }  */

.btn-secondary img {
  width: 12px;  
  height: 12px;
}
 

/* Tertiary Button */
.btn-tertiary {
  color: black;
  border: none;
  border-radius: 5px;
  padding: 5px 10px;
  font-size: 0.875em;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px; /* Space between icon and text */
}

.btn-tertiary:hover {
  background-color: #e2e2e2;
}

.btn-tertiary img {
  width: 12px; /* Standardize icon size */
  height: 12px;
}

/* Menu Button (Unique - Keep as is) */
nav button {
  border: none;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 20px;
  padding: 0;
}

nav button span {
  display: block;
  width: 25px;
  height: 3px;
  margin-bottom: 5px;
  background-color: #fff;
}

/* Replace with .btn-tertiary */
.inventory-item .buttons {
  margin: 10px;
}

.game-button {
  border: 2px solid #ededed;
  padding:3em;
  border-radius:12px;
  margin:.25em;
}

.game-button:hover{
  color:white;
  border: 2px solid white;
  padding:3em;
  border-radius:12px;
  margin:.25em;
	-webkit-animation: color-change-3x 4s linear infinite alternate both;
	        animation: color-change-3x 4s linear infinite alternate both;
}

.btn-orange {
  background: orange !important;
  color: white !important;
}

/* Standardize icon size (Keep as is) */
.button-icon img {
  width: 12px !important;
}

/* Replace with .btn-secondary */
.row_button {
  display: flex;
  justify-content: space-around;
  font-size: 12px;
  text-transform: uppercase;
  color: gray;
  padding: 8px;
  background: gray;
  margin: 5px;
  border-radius: 2px;
}

.row_button:hover,
a.row_button:hover {
  background-color: rgb(255, 234, 71);
  border-color: rgb(255, 234, 71);
  text-decoration: none;
  color: black;
}

/* Keep as is (Bootstrap utility class) */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
 
.nes-btn {
  padding: 0.5em 2.5em;
  text-transform: uppercase;
  text-align: center;
  font-family: 'Press Start 2P', cursive;
  text-decoration: none !important;
  border: 4px solid seashell;
  border-radius: 2px;
  background: #e9e9e9;
  color: black;
  border-bottom: 4px solid gray;
  position: relative; /* Needed for transform */
  transition: all 0.1s ease; /* Smooth transition for press effect */
}

/* Pressed effect on click */
.nes-btn:active {
  border: 4px solid #d0d0d0;
  border-bottom-width: 2px; 
  transform: translateY(2px);  
  background: #d0d0d0;  
  /* box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);   */
}

/* Ensure no hover color change (Keep as is) */
.nes-btn a {
  text-decoration: none !important;
  color: inherit; /* Inherit the button color */
}

.nes-btn a:hover {
  color: inherit; /* Keep the color the same on hover */
  text-decoration: none !important;
}

.login-btn {
  background-image: url('../images/ui/buttons/login_button.png');
  background-size: 128px 64px;
  width: 128px;
  height: 64px;
  border: none;
  cursor: pointer;
  display: inline-block;
} 

.login-btn:active {
  background-image: url('../images/ui/buttons/login_button_pressed.png');
}

.story-btn {
  background-image: url('../images/ui/buttons/story_button.png');
  background-size: 128px 64px;
  width: 128px;
  height: 64px;
  border: none;
  cursor: pointer;
  display: inline-block;
} 

.story-btn:active {
  background-image: url('../images/ui/buttons/story_button_pressed.png');
}

.arrow-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.arrow-btn {
  width: 80px;
  height: 80px; /* Adjust based on your image size */
  padding: 0;
  border: none;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  image-rendering: pixelated;
}

.left-arrow {
  background-image: url('../images/ui/buttons/btn-left-arrow.png');
}

.left-arrow:active {
  background-image: url('../images/ui/buttons/btn-left-arrow-pressed.png');
}

.right-arrow {
  background-image: url('../images/ui/buttons/btn-right-arrow.png');
}

.right-arrow:active {
  background-image: url('../images/ui/buttons/btn-right-arrow-pressed.png');
}

.icon {
   display: inline-block;
   width: 12px;
   height: 12px;
   margin-right: 5px;
   background-size: contain;
   background-repeat: no-repeat;
}

.ready-icon {
    background-image: url('../images/ui/blue_flag.png');
}

.nap-icon { 
    background-image: url('../images/ui/bed_icon.png');
}

.reward-icon {
    background-image: url('../images/ui/reward_icon.png');
}

.adventure-icon {
    background-image: url('../images/ui/in_progress_icon_small.png');
}

.game-icon {
    background-image: url('../images/ui/hud/hud_games.png');
}

.leader-icon {
      background-image: url('../images/ui/leader_icon.png');
}

.plus-icon {
      background-image: url('../images/ui/plus.png');
}

.world_nav_buttons {
    width: 100%;
    border: none;
    white-space: nowrap;
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    padding: 5px 10px;
    font-size: 14px;
}

.world_nav_buttons:active {
    background: white;
}

.buttons{
  width:33%;
}


#new-team-list button{
  border-color:#e2e2e2;
  background: white;
  color: black;
  padding: 5px 14px;
  border-radius: 5px;
}

#new-team-list button:hover{
  color:black;
  background: #e2e2e2;
  padding: 5px 14px;
  border-color: #e2e2e2;
  border-radius: 5px;
}

#game-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem; 
  overflow-y: scroll;
}

#game-buttons button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  max-height: 150px;
  text-align: center;
  padding: 1rem;
}
 
 