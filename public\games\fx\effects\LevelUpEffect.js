// Level Up Effect
class LevelUpEffect {
    constructor() {
        // No dependencies needed for this effect
    }

    // Create level up effect
    create(stage, level, onComplete) {
        // Play level up sound
        if (window.GameSounds) {
            window.GameSounds.playLevelUp();
        }
        
        // Create container for the effect
        const effectContainer = new PIXI.Container();
        
        // Create background rectangle
        const background = new PIXI.Graphics();
        background.beginFill(0x000000, 1.0); // Fully opaque black background
        background.lineStyle(3, 0xFFD700);
        background.drawRoundedRect(0, 0, 400, 120, 10);
        background.endFill();
        
        // Create level up text
        const levelUpText = new PIXI.Text('Level ' + level + '!', {
            fontFamily: 'Arial',
            fontSize: 48,
            fontWeight: 'bold',
            fill: 0xFFD700,
            stroke: 0x000000,
            strokeThickness: 4
        });
        
        // Position elements using countdown-style centering
        // Get the game area dimensions (similar to countdown positioning)
        const gameArea = document.getElementById('game-area');
        const gameAreaRect = gameArea ? gameArea.getBoundingClientRect() : null;
        
        // Use game area dimensions if available, otherwise fall back to stage dimensions
        const containerWidth = gameAreaRect ? gameAreaRect.width : stage.width;
        const containerHeight = gameAreaRect ? gameAreaRect.height : stage.height;
        
        // Center the background rectangle (similar to countdown overlay positioning)
        background.x = containerWidth / 2 - background.width / 2;
        background.y = containerHeight / 2 - background.height / 2;
        
        // Center the text within the background
        levelUpText.x = background.x + (background.width / 2) - (levelUpText.width / 2) - 10; // Shift 10px left
        levelUpText.y = background.y + (background.height / 2) - (levelUpText.height / 2);
        
        // Add elements to container
        effectContainer.addChild(background);
        effectContainer.addChild(levelUpText);
        
        // Make container interactive for click-to-skip
        effectContainer.interactive = true;
        effectContainer.buttonMode = true;
        
        // Add to stage
        stage.addChild(effectContainer);
        
        // Animation variables
        let scale = 0.5;
        let alpha = 0;
        let phase = 0; // 0: fade in, 1: hold, 2: fade out
        let holdTime = 0;
        const HOLD_DURATION = 5000; // 5 seconds in milliseconds
        
        // Click handler to skip wait
        const skipHandler = () => {
            if (phase === 1) {
                phase = 2;
                effectContainer.removeListener('pointerdown', skipHandler);
            }
        };
        
        effectContainer.on('pointerdown', skipHandler);
        
        // Animate level up effect
        const animate = () => {
            if (phase === 0) {
                // Fade in and scale up
                scale += 0.1;
                alpha += 0.05;
                
                if (scale >= 1.0) {
                    scale = 1.0;
                    phase = 1;
                }
            } else if (phase === 1) {
                // Hold phase - count time
                holdTime += 16; // Assuming 60fps (16ms per frame)
                
                if (holdTime >= HOLD_DURATION) {
                    phase = 2;
                    effectContainer.removeListener('pointerdown', skipHandler);
                }
            } else if (phase === 2) {
                // Fade out
                alpha -= 0.05;
                
                if (alpha <= 0) {
                    stage.removeChild(effectContainer);
                    if (onComplete) {
                        onComplete();
                    }
                    return;
                }
            }
            
            effectContainer.scale.set(scale);
            effectContainer.alpha = alpha;
            
            requestAnimationFrame(animate);
        };
        
        animate();
    }
} 