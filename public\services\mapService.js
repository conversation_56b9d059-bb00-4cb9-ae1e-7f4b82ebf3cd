async function displayWorlds() {
  try {
    // Ensure we have adventure data before proceeding
    if (!allAdventures || allAdventures.length === 0) {
      await getAdventures();
    }

    // Ensure we have zone data before proceeding
    if (!allZones || allZones.length === 0) {
      await getMapData();
    }

    const adventureCount = await countAdventuresInWorlds('In Progress', allAdventures);
    nav.view = 'worlds';
    nav.zone = 0;
    updateNavButtons(0, 0);

    // Safely handle AudioManager operations
    try {
      if (typeof AudioManager !== 'undefined') {
        AudioManager.logAudioState();
        // No music should play in the worlds view
        if (AudioManager.music && AudioManager.music.currentTrack) {
          AudioManager.music.currentTrack.pause();
        }
        setTimeout(() => {
          if (typeof AudioManager !== 'undefined') {
            AudioManager.logAudioState();
          }
        }, 500);
      }
    } catch (audioError) {
      console.error('Error handling audio in displayWorlds:', audioError);
    }

    $('.map').empty();

  for (var i = 0; i < 4; i++) {
    var world = $('<div class="worldsquare txt-shadow"></div>');
    world.attr('id', i);
    if (nav.world === i) {
      world.addClass("world-enabled-icon");
    } else {
      world.addClass("world-disabled-icon");
    }
    world.attr('onclick', 'displayZones(this.id)');
    world.text(i + 1);

    // Count unlocked zones
    let unlockedZonesCount = 0;
    let totalZones = 0;

    // Make sure allZones is populated
    if (allZones && allZones.length > 0) {
      for (var j in allZones) {
        if (allZones[j].mapgrid_4 === i) {
          totalZones++;
          if (allZones[j].status !== "locked") {
            unlockedZonesCount++;
          }
        }
      }
    }

    // Create a container for bottom-right overlays
    var overlayContainer = $('<div class="world-overlay-container"></div>');

    // Unlocked zones label with lock icon
    var unlockedZonesText = $(
      `<span class="unlocked-zones-text"><img src="../images/nav/lock_map_icon.png" style="vertical-align:middle;width:10px;height:10px;margin-right:2px;">${unlockedZonesCount}/${totalZones} zones unlocked</span>`
    );

    // World info icon and label
    var infoButton = $(
      `<a href="/#" class="world-info-link" id="info-${i}">
        <img src="../images/ui/world_scroll.png" style="vertical-align:middle;width:14px;height:14px;">
        <span class="world-info-text">World info</span>
      </a>`
    );
    infoButton.click(function(e) {
      e.stopPropagation();
      displayWorldDescriptionModal(world_descriptions, Number(this.id.replace('info-', '')));
    });

    overlayContainer.append(unlockedZonesText);
    overlayContainer.append(infoButton);
    world.append(overlayContainer);

    // Add adventure count if there are adventures in this world
    if (adventureCount && adventureCount[i] > 0) {
      const countText = `Adventures: ${adventureCount[i]}`;
      world.append(`<div class="world-presence">${countText}</div>`);
    }

    $('.map').append(world);
  }
  } catch (error) {
    console.error('Error in displayWorlds:', error);
  }
 }

 async function displayZones(worldId) {
  // Ensure we have adventure data before proceeding
  if (!allAdventures || allAdventures.length === 0) {
    await getAdventures();
  }

  // Ensure we have zone data before proceeding
  if (!allZones || allZones.length === 0) {
    await getMapData();
  }

  nav.view = 'zones';
  nav.world = Number(worldId);
  nav.locale = 0;

  // No longer playing BGM music in displayZones function
  // BGM music will only play in displayLocales function

  // Make sure team counts are updated
  await displayTeamsOnMap(displayTeamIcon, displayVehicleMovingIcon, displayTreasureIcon, displayTeamReadyIcon);
  currentzonesJson = await getAllZonesFromWorldId(nav.world, allZones);
  updateNavButtons(nav.world, nav.zone);
  $("#zone_btn").addClass("active");
  $('.map').empty();

  // Check if we have zones for this world
  const worldZones = allZones.filter(zone => zone.mapgrid_4 === nav.world);
  if (worldZones.length === 0) {
    $('.map').append('<div class="alert alert-info">No zones available for this world yet.</div>');
    return;
  }

  let zoneCounter = 0;
  // Process each zone
  for (const zone of allZones) {
    if (zone.mapgrid_4 !== nav.world) continue;
    // Create zone element
    const zoneSquare = createZoneSquare(zone);
    // Add team information if zone is not locked
    if (zone.status !== "locked") {
      try {
        // Use the global teamCounts variable
        if (window.teamCounts) {
          addTeamInfoToZone(zoneSquare, zone, nav, window.teamCounts);
        }
      } catch (error) {
        console.error('ERROR in addTeamInfoToZone:', error.message);
      }
    }

    // Handle locked vs unlocked zones
    if (zone.status === "locked") {
      setupLockedZone(zoneSquare, zone);
    } else {
      setupUnlockedZone(zoneSquare, zone);
    }

    // Add zone to the map
    addZoneToMap(zoneSquare);
    zoneCounter++;

    // Add row break every 4 zones
    if (zoneCounter % 4 === 0) {
      $('.map').append('<div class="w-100"></div>');
    }
  }
 }

 async function displayLocales(zoneId) {
  // Ensure we have adventure data before proceeding
  if (!allAdventures || allAdventures.length === 0) {
    await getAdventures();
  }

  // Ensure we have zone data before proceeding
  if (!allZones || allZones.length === 0) {
    await getMapData();
  }

  // Safely handle AudioManager operations
  try {
    if (typeof AudioManager !== 'undefined') {
      AudioManager.logAudioState();
      AudioManager.toggleBGM(true);
      AudioManager.playBGM(nav.world);
      setTimeout(() => {
        if (typeof AudioManager !== 'undefined') {
          AudioManager.logAudioState();
        }
      }, 500);
    }
  } catch (audioError) {
    console.error('Error handling audio in displayLocales:', audioError);
  }

  nav.view = 'locales';
  nav.zone = Number(zoneId);
  updateNavButtons(nav.world, zoneId);
  $("#locale_btn").addClass("active");
  $('#mapEditorZoneSelected').html('<strong>Selected Zone:</strong> World #' + nav.world + ', Zone #' + nav.zone)
  var activeMoveIcons = [];
  for (var m in activeAdventures) {
    if (activeAdventures[m].status === 'In Progress' && Number(activeAdventures[m].mapgrid_4) === nav.world && Number(activeAdventures[m].mapgrid_16) === nav.zone) {
      activeMoveIcons.push(activeAdventures[m].mapgrid_256);
    }
  }

  $('.map').empty();
  var locales = [];

  for (a in allZones) {
    if (allZones[a].mapgrid_4 === nav.world && allZones[a].mapgrid_16 === nav.zone) {
      locales = allZones[a].data.locales;
    }
  }

  // --- CLAIMABLE LOGIC REFACTOR ---
  const claimableState = window.claimable.getClaimableState(nav.world, zoneId);
  // --- END CLAIMABLE LOGIC REFACTOR ---

  for (var i = 0; i < 256; i++) {
    var localeSquare = $('<div class="mapsquare"></div>');
    localeSquare.attr('id', locales[i].Locale);
    localeSquare.addClass(locales[i].Tile);

    if (activeMoveIcons.includes(i)) {
      var adv_icon = $("<div>").addClass("arrow-pointer-icon");
      localeSquare.text(i + 1).prepend(adv_icon);
    } else {
      localeSquare.text(i + 1);
    }

    // --- CLAIMABLE ICON INJECTION (REFACTORED) ---
    window.claimable.maybeRenderClaimable(localeSquare, i, claimableState, async function(claimableIcon) {
      // Prevent double claim
      if (claimableState.found) return;
      await transactResource('gxp', 10, 'add', showAlert, updatePlayerBalances);
      window.claimable.setClaimableFound(nav.world, zoneId);
      showAlert("You found 10 GXP! There are no more claimables for you today.");
      // Remove the claimable icon after claim
      $(claimableIcon).remove();
    });
    // --- END CLAIMABLE ICON INJECTION ---

    $('.map').append(localeSquare);
    localeSquare.mouseover(async function() {
      var locale = findLocaleInfo(nav, this.id, allZones);
      var capitalizedTerrain = locale.Terrain.charAt(0).toUpperCase() + locale.Terrain.slice(1);
      $("#locale_btn").html(`<span class="nav-icon locale-icon"></span> ${locale.Locale_Name}<br>Square #${locale.Locale + 1}<br>Type: ${capitalizedTerrain}`);
      $(this).addClass('tile-glow');
    });

    localeSquare.click(function() {
      newDestination.world = nav.world;
      newDestination.zone = nav.zone;
      newDestination.locale = Number(this.id); // Convert to number to prevent type issues
      nav.locale = Number(this.id);
      var locale = findLocaleInfo(nav, this.id, allZones);

      if (mapEditorEnable == true) {
        insertLocaleChangesIntoQueue();
        $('#map-editor-selection').html('<strong>Selected Area:</strong> World: ' + newDestination.world + ', Zone: ' + newDestination.zone + ', Locale: ' + newDestination.locale);
        $('#mapEditorLocaleName').val(locale.Locale_Name);
        $(this).attr("class", "mapsquare lava");
      }

      var team_vehicle = findTeamData(myTeams, teamSelectedForAdventure, 'vehicles', 0);
      var title = "Start New Adventure";
      var body = 'Send Team #' + teamSelectedForAdventure + '</span> to \n' +
        locale.Locale_Name + ' (' + (Number(newDestination.world) + 1) + ', '
        + (Number(newDestination.zone) + 1) + ', ' + (Number(newDestination.locale) + 1) + ')?';
      if (enableSetAdventure && isEnoughNectar(playerData) && isVehicleTypeAllowed(team_vehicle, locale.Terrain, showAlert)) {
        displaySetLocationModal(title, body);
      }
    });

    localeSquare.mouseout(function() {
      hideTooltip();
      $(this).removeClass('tile-glow');
    });
  }

  await displayTeamsOnMap(displayTeamIcon, displayVehicleMovingIcon, displayTreasureIcon, displayTeamReadyIcon);
 }