const AudioManager = {
    config: {
        masterVolume: 0,
        sfxVolume: 1,
        musicVolume: 1,
        bgmEnabled: true
    },
    sounds: {
        ui: {
            hover: new Audio('../audio/sfx/StartButton.wav'),
            logo: new Audio('../audio/sfx/LogoSound.wav'),
            startButton: new Audio('../audio/sfx/StartButton.wav'),
            cancel: new Audio('../audio/sfx/Cancel.wav'),
            pay: new Audio('../audio/sfx/Pay.wav'),
            map: new Audio('../audio/sfx/Map.wav'),
            dooropen: new Audio('../audio/sfx/DoorOpen.wav'),
            rewardclaim: new Audio('../audio/sfx/RewardClaim.wav'),
            adventure: new Audio('../audio/sfx/Adventure.wav'),
            plasticButton: new Audio('../audio/sfx/PlasticButton.wav'),
            dialog1: new Audio('../audio/sfx/Dialogue-1.wav'),
            dialog2: new Audio('../audio/sfx/Dialogue-2.wav'),
            robotdialog: new Audio('../audio/sfx/Robot-Dialogue.wav')
        },
        creatures: {
            cat: new Audio('../audio/sfx/Cat.wav'),
            dog: new Audio('../audio/sfx/Dog.wav'),
            robot: new Audio('../audio/sfx/Robot.wav'),
            bird: new Audio('../audio/sfx/Bird.wav'),
            dream_jelly: new Audio('../audio/sfx/Jelly.wav'),
            monkey: new Audio('../audio/sfx/Monkey.wav'),
            ent: new Audio('../audio/sfx/Ent.wav'),
            default: new Audio('../audio/sfx/Speech.wav')
        },
        game: {
            lowDanger: new Audio('../../audio/sfx/games/low_danger.wav'),
            medDanger: new Audio('../../audio/sfx/games/med_danger.wav'),
            highDanger: new Audio('../../audio/sfx/games/high_danger.wav'),
            explosion: new Audio('../../audio/sfx/games/explosion.wav'),
            station: new Audio('../../audio/sfx/games/station.wav'),
            ship: new Audio('../../audio/sfx/games/ship.wav'),
            treasure: new Audio('../../audio/sfx/games/treasure.wav'),
            marshmellow: new Audio('../../audio/sfx/games/marshmellow.wav'),
            gem: new Audio('../../audio/sfx/games/gem.wav')
        }
    },

    music: {
        tracks: {
            mainTheme: './audio/bgm/MainTheme.mp3',
            0: '../audio/bgm/0.wav',
            1: '../audio/bgm/1.wav',
            2: '../audio/bgm/2.wav',
            3: '../audio/bgm/3.wav'
        },
        currentTrack: null,
        currentWorld: null
    },

    init() {
        this.updateAllVolumes();
    },

    updateAllVolumes() {
        const effectiveVolume = this.config.masterVolume * this.config.sfxVolume;
        const effectiveMusicVolume = this.config.masterVolume * this.config.musicVolume;

        Object.values(this.sounds.ui).forEach(sound => {
            sound.volume = effectiveVolume;
        });
        Object.values(this.sounds.creatures).forEach(sound => {
            sound.volume = effectiveVolume;
        });
        Object.values(this.sounds.game).forEach(sound => {
            sound.volume = effectiveVolume;
        });

        if (this.music.currentTrack) {
            this.music.currentTrack.volume = effectiveMusicVolume;
        }
    },

    setMasterVolume(volume) {
        this.config.masterVolume = Math.max(0, Math.min(1, volume));
        this.updateAllVolumes();

        if (this.config.masterVolume === 0 && this.music.currentTrack) {
            this.music.currentTrack.pause();
        } else if (this.config.masterVolume > 0 && this.config.bgmEnabled && this.music.currentTrack) {
            this.music.currentTrack.play().catch(() => {});
        }
    },

    setSFXVolume(volume) {
        this.config.sfxVolume = Math.max(0, Math.min(1, volume));
        this.updateAllVolumes();
    },

    setMusicVolume(volume) {
        this.config.musicVolume = Math.max(0, Math.min(1, volume));
        this.updateAllVolumes();
    },

    playCreatureSound(creature) {
        const normalizedCreature = creature?.toLowerCase().replace(/\s+/g, '_');
        const sound = this.sounds.creatures[normalizedCreature] || this.sounds.creatures.default;
        if (sound) {
            sound.currentTime = 0;
            sound.play();
        }
        return sound;
    },

    playGameSound(soundName) {
        const sound = this.sounds.game[soundName];
        if (sound) {
            sound.currentTime = 0;
            sound.play();
        }
        return sound;
    },

    playUISound(soundName) {
        const sound = this.sounds.ui[soundName];
        if (sound) {
            sound.currentTime = 0;
            sound.play();
        }
        return sound;
    },

    playBGM(world) {
        const worldIndex = Number(world);

        if (this.music.currentWorld === worldIndex && this.music.currentTrack) {
            return;
        }

        this.music.currentWorld = worldIndex;

        if (!this.config.bgmEnabled || this.config.masterVolume === 0) {
            return;
        }

        if (this.music.currentTrack) {
            this.music.currentTrack.pause();
            this.music.currentTrack.currentTime = 0;
        }

        let trackPath;
        if (this.music.tracks[worldIndex] !== undefined) {
            trackPath = this.music.tracks[worldIndex];
        } else {
            trackPath = this.music.tracks.mainTheme;
        }

        this.music.currentTrack = new Audio(trackPath);
        this.music.currentTrack.volume = this.config.masterVolume * this.config.musicVolume;
        this.music.currentTrack.loop = true;

        if (this.config.masterVolume > 0) {
            this.music.currentTrack.play().catch(() => {});
        }
    },

    toggleBGM(enabled) {
        this.config.bgmEnabled = enabled;

        if (!enabled && this.music.currentTrack) {
            this.music.currentTrack.pause();
        } else if (enabled && this.music.currentTrack && this.config.masterVolume > 0) {
            this.music.currentTrack.play().catch(() => {});
        } else if (enabled && this.music.currentWorld !== null && !this.music.currentTrack) {
            this.playBGM(this.music.currentWorld);
        }
    },

    playStartTheme() {
        if (this.music.currentWorld === 'mainTheme' && this.music.currentTrack) {
            return;
        }
        this.playBGM('mainTheme');
    },

    logAudioState() {}
};

// Initialize the audio manager
try {
    AudioManager.init();
    window.AudioManager = AudioManager;
} catch (error) {}

// Legacy functions for backward compatibility
function playSound(creature) {
    return AudioManager.playCreatureSound(creature);
}

function playBGM() {
    AudioManager.playBGM(nav.world);
}

function disableBGM(enable) {
    AudioManager.toggleBGM(!enable);
}

// Game sound shortcuts
const playTreasureSound = () => AudioManager.playGameSound('treasure');
const playMarshmellowSound = () => AudioManager.playGameSound('marshmellow');
const playLowDangerSound = () => AudioManager.playGameSound('lowDanger');
const playMedDangerSound = () => AudioManager.playGameSound('medDanger');
const playHighDangerSound = () => AudioManager.playGameSound('highDanger');
const playExplosionSound = () => AudioManager.playGameSound('explosion');
const playStationSound = () => AudioManager.playGameSound('station');
const playGemSound = () => AudioManager.playGameSound('gem');
const playShipSound = () => AudioManager.playGameSound('ship');