@keyframes bounceInAnim {
	0% {
		animation-timing-function: ease-in;
		opacity: 0;
		transform: translateY(-75px);
	}
	38% {
		animation-timing-function: ease-out;
		opacity: 1;
		transform: translateY(0);
	}
	55% {
		animation-timing-function: ease-in;
		transform: translateY(-19px);
	}
	72% {
		animation-timing-function: ease-out;
		transform: translateY(0);
	}
	81% {
		animation-timing-function: ease-in;
		transform: translateY(-8px);
	}
	90% {
		animation-timing-function: ease-out;
		transform: translateY(0);
	}
	95% {
		animation-timing-function: ease-in;
		transform: translateY(-2.4px);
	}
	100% {
		animation-timing-function: ease-out;
		transform: translateY(0);
	}
}

@keyframes pulse {
  0% {
    border-color: #0D6EFD;
  }
  50% {
    border-color: white;
  }
  100% {
    border-color: #0D6EFD;
  }
}

@keyframes gold-pulse {
  0% {
    border-color: gold;
  }
  50% {
    border-color: white;
  }
  100% {
    border-color: gold;
  }
}

@keyframes bounceInAnim {
	0% {
		animation-timing-function: ease-in;
		opacity: 0;
		transform: translateY(-45px);
	}
	38% {
		animation-timing-function: ease-out;
		opacity: 1;
		transform: translateY(0);
	}
	55% {
		animation-timing-function: ease-in;
		transform: translateY(-19px);
	}
	72% {
		animation-timing-function: ease-out;
		transform: translateY(0);
	}
	81% {
		animation-timing-function: ease-in;
		transform: translateY(-8px);
	}
	90% {
		animation-timing-function: ease-out;
		transform: translateY(0);
	}
	95% {
		animation-timing-function: ease-in;
		transform: translateY(-2.4px);
	}
	100% {
		animation-timing-function: ease-out;
		transform: translateY(0);
	}
}

/* Apply the floating animation */
.nes-logo.floating {
  animation: float 3s ease-in-out infinite; /* Adjust the duration for the floating speed */
}

/* Add wiggle effect on hover */
.nes-logo.floating:hover {
  animation: float 3s ease-in-out infinite, wiggle 0.5s ease-in-out infinite;
}
/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px); /* Adjust the distance for the floating effect */
  }
}

/* Wiggle animation on hover */
@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(2deg); /* Adjust the angle for the wiggle effect */
  }
  75% {
    transform: rotate(-2deg); /* Adjust the angle for the wiggle effect */
  }
}


.mesmerize {
  -webkit-animation: color-change-2x 12s cubic-bezier(0.600, 0.040, 0.980, 0.335) infinite alternate both;
  animation: color-change-2x 12s cubic-bezier(0.600, 0.040, 0.980, 0.335) infinite alternate both;
}

@-webkit-keyframes color-change-2x {
	0% {
    background: #F1F1F1;
	}
	100% {
    background: #F1F1F1;
	}
}
@keyframes color-change-2x {
	0% {
    background: #F1F1F1;
	}
	100% {
    background: #F1F1F1;
	}
}

.scanlines {
      position: relative;
      overflow: hidden;
      &:before,
      &:after {
          display: block;
          pointer-events: none;
          content: '';
          position: absolute;
      }
      &:before {
          width: 100%;
          height: $scan-width * 1;
          z-index: $scan-z-index + 1;
          background: $scan-color;
          opacity: $scan-opacity;
          animation: scanline 6s linear infinite;
          @include scan-moving($scan-moving-line);
      }
      &:after {
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          z-index: $scan-z-index;
          background: linear-gradient(
              to bottom,
              transparent 50%,
              $scan-color 51%
          );
          background-size: 100% $scan-width*2;
          @include scan-crt($scan-crt);
      }
  }

  @keyframes scanline {
      0% {
          transform: translate3d(0,200000%,0);
      }
  }

  @keyframes scanlines {
      0% {
          background-position: 0 50%;
      }
  }

   .rotate-center {
   	-webkit-animation: rotate-center 5s ease-in-out infinite both;
   	        animation: rotate-center 5s ease-in-out infinite both;
   }
   @-webkit-keyframes rotate-center {
     0% {
       -webkit-transform: rotate(0);
               transform: rotate(0);
     }
     100% {
       -webkit-transform: rotate(360deg);
               transform: rotate(360deg);
     }
   }
   @keyframes rotate-center {
     0% {
       -webkit-transform: rotate(0);
               transform: rotate(0);
     }
     100% {
       -webkit-transform: rotate(360deg);
               transform: rotate(360deg);
     }
   }
