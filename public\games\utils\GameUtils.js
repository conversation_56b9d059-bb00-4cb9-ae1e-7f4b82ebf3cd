// Game Utilities - Common functions and DOM element management
class GameUtils {
    constructor() {
        this.cacheElements();
    }

    // Cache DOM elements for better performance
    cacheElements() {
        this.timeElement = document.getElementById('game-timer');
        this.levelElement = document.getElementById('game-level');
        this.scoreElement = document.getElementById('game-score');
        this.endTimeElement = document.getElementById('end-time');
        this.endLevelElement = document.getElementById('end-level');
        this.endScoreElement = document.getElementById('end-score');
        this.resultMessage = document.getElementById('result-message');
    }

    // Initialize DOM elements when page loads
    static init() {
        document.addEventListener('DOMContentLoaded', function() {
            GameUtils.instance = new GameUtils();
        });
    }

    // Create game buttons for the main menu
    createGameButtons() {
        const gamesContainer = document.getElementById('game-buttons');
        if (!gamesContainer) return;
        
        gamesContainer.innerHTML = '';

        // Always use GameManager's list as the source of truth
        const miniGamesList = (window.GameManager && GameManager.instance && GameManager.instance.active_mini_games)
            ? GameManager.instance.active_mini_games
            : [1, 2, 5, 18, 19, 20]; // fallback

        miniGamesList.forEach(gameId => {
            const game = mini_games.find(game => game.id === gameId);
            if (game) {
                const button = document.createElement('button');
                button.setAttribute('type', 'button');
                button.setAttribute('class', 'btn-secondary flex-fill m-1');
                button.setAttribute('data-value', game.id);

                const icon = document.createElement('span');
                icon.className = `icon game-icon`;
                button.appendChild(icon);

                button.appendChild(document.createTextNode(game.name));

                button.addEventListener('click', () => {
                    if (playerData.credits >= 1) {
                        GameManager.displayGameModal(game.id);
                    } else {
                        showAlert("Not enough credits to play (requires 1 credit).");
                    }
                });
                gamesContainer.appendChild(button);
            }
        });
        // Add Test Battle UX button
        const testBattleBtn = document.createElement('button');
        testBattleBtn.setAttribute('type', 'button');
        testBattleBtn.setAttribute('class', 'btn-primary flex-fill m-1');
        testBattleBtn.innerHTML = '<i class="fas fa-flask"></i> Test Battle UX';
        testBattleBtn.onclick = function() {
            window.open('services/new-battle/index.html', '_blank', 'width=1200,height=900');
        };
        gamesContainer.appendChild(testBattleBtn);
    }

    // Update HUD elements
    updateHUD(time, level, score) {
        if (this.timeElement) this.timeElement.textContent = time;
        if (this.levelElement) this.levelElement.textContent = level;
        if (this.scoreElement) this.scoreElement.textContent = score;
    }

    // Update end game screen
    updateEndGame(time, level, score, result) {
        if (this.endTimeElement) this.endTimeElement.textContent = time;
        if (this.endLevelElement) this.endLevelElement.textContent = level;
        if (this.endScoreElement) this.endScoreElement.textContent = score;
        if (this.resultMessage) this.resultMessage.textContent = result;
    }

    // Show alert with consistent styling
    static showAlert(message, type = 'info') {
        if (typeof showAlert === 'function') {
            showAlert(message);
        } else {
            alert(message);
        }
    }

    // Validate game data
    static validateGameData(gameId) {
        const game = mini_games.find(game => game.id === gameId);
        if (!game) {
            this.showAlert('Game not found!');
            return false;
        }
        return true;
    }

    // Check if PIXI is loaded
    static checkPixiLoaded() {
        if (typeof PIXI === 'undefined') {
            this.showAlert('Game engine not loaded properly. Please refresh the page and try again.');
            return false;
        }
        return true;
    }

    // Clear canvas
    static clearCanvas() {
        const gameCanvas = document.getElementById('game-canvas');
        if (gameCanvas) {
            gameCanvas.innerHTML = '';
        }
    }

    // Set modal display states
    static setModalStates(startScreen, gameOverScreen, gameModal) {
        if (gameModal) gameModal.style.display = "block";
        if (startScreen) startScreen.style.display = "flex";
        if (gameOverScreen) gameOverScreen.style.display = "none";
    }

    // Update game instructions
    static updateGameInstructions(game) {
        const startScreenInstructions = document.querySelector('#start-screen p');
        const gameInstructions = document.getElementById('game-instructions');

        if (startScreenInstructions && game.instructions) {
            startScreenInstructions.textContent = game.instructions;
        }

        if (gameInstructions && game.instructions) {
            // Keep the info icon but update the text
            const infoIcon = gameInstructions.querySelector('img');
            gameInstructions.innerHTML = '';
            if (infoIcon) {
                gameInstructions.appendChild(infoIcon);
            }
            gameInstructions.appendChild(document.createTextNode(' ' + game.instructions));
            
            // Reset visibility and add click functionality
            gameInstructions.style.opacity = '1';
            gameInstructions.style.visibility = 'visible';
            gameInstructions.style.cursor = 'pointer';
            
            // Remove any existing click listeners
            gameInstructions.removeEventListener('click', gameInstructions.fadeOutHandler);
            
            // Add click listener to fade out instructions
            gameInstructions.fadeOutHandler = () => {
                gameInstructions.style.transition = 'opacity 0.5s ease-out';
                gameInstructions.style.opacity = '0';
                setTimeout(() => {
                    gameInstructions.style.visibility = 'hidden';
                }, 500);
            };
            gameInstructions.addEventListener('click', gameInstructions.fadeOutHandler);

            // --- Add auto-dismissal after 5 seconds ---
            if (gameInstructions.autoDismissTimeout) {
                clearTimeout(gameInstructions.autoDismissTimeout);
            }
            gameInstructions.autoDismissTimeout = setTimeout(() => {
                if (gameInstructions.style.visibility !== 'hidden') {
                    gameInstructions.fadeOutHandler();
                }
            }, 5000);
            // --- End auto-dismissal ---
        }
    }

    /**
     * Create a HUD element (ResourceBar, HeartMeter for hearts or powerups) and add it to a PIXI container/stage.
     * Usage:
     *   const hpBar = GameUtils.createHudElement('bar', app.stage, { ... });
     *   const heartMeter = GameUtils.createHudElement('hearts', app.stage, { ... });
     *   const powerupMeter = GameUtils.createHudElement('powerups', app.stage, { ... });
     *
     * type: 'bar' | 'hearts' | 'powerups'
     * parent: PIXI.Container (e.g., app.stage)
     * options: see ResourceBar/HeartMeter constructors
     */
    static createHudElement(type, parent, options) {
        let hudElement = null;
        if (type === 'bar') {
            hudElement = new ResourceBar(options);
        } else if (type === 'hearts') {
            hudElement = new HeartMeter(Object.assign({
                fullIcon: 'images/games/hud/heart.png',
                emptyIcon: 'images/games/hud/heart-empty.png'
            }, options));
        } else if (type === 'powerups') {
            hudElement = new HeartMeter(Object.assign({
                fullIcon: 'images/games/hud/powerup.png',
                emptyIcon: 'images/games/hud/powerup-empty.png'
            }, options));
        }
        if (hudElement && parent) {
            parent.addChild(hudElement);
        }
        return hudElement;
    }

    /**
     * Create a HUD element using the HUDConfig system with proper backgrounds and positioning.
     * Usage:
     *   const hpBar = GameUtils.createHudElementWithConfig('resource_bar', 'hud_med_position_1', { ... });
     *   const heartMeter = GameUtils.createHudElementWithConfig('heart_meter', 'hud_med_position_2', { ... });
     *
     * elementName: 'resource_bar' | 'heart_meter' | 'powerup_bar' | 'wind_indicator'
     * positionName: 'hud_med_position_1' | 'hud_med_position_2' | 'hud_sm_position_1' | 'hud_lg_position_1'
     * customOptions: additional options to override defaults
     */
    static createHudElementWithConfig(elementName, positionName, customOptions = {}) {
        if (typeof HUDConfig === 'undefined') {
            console.error('HUDConfig not loaded. Please ensure hud-config.js is loaded before GameUtils.');
            return null;
        }
        
        return HUDConfig.createHudElement(elementName, positionName, customOptions);
    }

    /**
     * Add a HUD element to stage with proper layering using HUDConfig.
     * Usage:
     *   GameUtils.addHudElementToStage(app.stage, hudElement);
     */
    static addHudElementToStage(stage, hudElement) {
        if (typeof HUDConfig === 'undefined') {
            console.error('HUDConfig not loaded. Please ensure hud-config.js is loaded before GameUtils.');
            return;
        }
        
        HUDConfig.addToStage(stage, hudElement);
    }
}

// Initialize GameUtils when the script loads
GameUtils.init(); 