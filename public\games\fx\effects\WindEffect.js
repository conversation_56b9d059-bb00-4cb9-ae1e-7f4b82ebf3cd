// WindEffect.js - Simulates wind and displays an arrow for direction/strength
function WindEffect(options) {
    options = options || {};
    this.arrowX = 480;
    this.arrowY = 55;
    this.arrowScale = options.arrowScale || 1.2;
    this.arrow = null;
    this.windAngle = 0; // radians
    this.windStrength = 0; // 0 to 1
    this.windTimer = null;
    this.stage = null;
    this.running = false;
    this._changeWind = this._changeWind.bind(this);
}

WindEffect.prototype.addToStage = function(stage) {
    if (this.arrow) return;
    this.stage = stage;
    var texture = PIXI.Texture.from('images/games/bouncyclick/red-arrow.png');
    var arrow = new PIXI.Sprite(texture);
    arrow.anchor.set(0.5, 0.5);
    arrow.x = this.arrowX;
    arrow.y = this.arrowY;
    arrow.scale.set(this.arrowScale);
    arrow.zIndex = 1000;
    stage.addChild(arrow);
    this.arrow = arrow;
    this.running = true;
    this._changeWind();
};

WindEffect.prototype._changeWind = function() {
    // Random angle (0 = up, PI/2 = right, etc)
    this.windAngle = Math.random() * Math.PI * 2;
    // Strength: 0.2 to 1.0
    this.windStrength = 0.2 + Math.random() * 0.8;
    // Rotate arrow so 0 = up, matches force direction
    if (this.arrow) {
        this.arrow.rotation = this.windAngle;
        // Optionally, scale arrow by strength for effect
        this.arrow.alpha = 0.7 + 0.3 * this.windStrength;
    }
    // Next change in 3-7 seconds
    var next = 3000 + Math.random() * 4000;
    var self = this;
    if (this.windTimer) clearTimeout(this.windTimer);
    this.windTimer = setTimeout(function() {
        if (self.running) self._changeWind();
    }, next);
};

WindEffect.prototype.applyWind = function(bodies) {
    if (!bodies || !bodies.length) return;
    // Apply a small force to each body in the direction the arrow points
    var forceMag = this.windStrength * 0.0005; // Tune as needed
    var fx = Math.cos(this.windAngle) * forceMag;
    var fy = Math.sin(this.windAngle) * forceMag;
    for (var i = 0; i < bodies.length; i++) {
        var body = bodies[i];
        if (body && body.position) {
            Matter.Body.applyForce(body, body.position, { x: fx, y: fy });
        }
    }
};

WindEffect.prototype.remove = function() {
    this.running = false;
    if (this.windTimer) clearTimeout(this.windTimer);
    if (this.arrow && this.stage) {
        this.stage.removeChild(this.arrow);
        this.arrow = null;
    }
};

// Attach to window for global access
if (typeof window !== 'undefined') {
    window.WindEffect = WindEffect;
} 