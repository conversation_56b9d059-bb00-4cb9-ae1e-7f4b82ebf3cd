const {
	Router
} = require('express');

const allQuery = require("../controls/controller");
const router = Router();

router.get("/zones/", allQuery.getMapZones);
router.get("/", allQuery.getPlayers);
router.get("/logs/", allQuery.getGameLog);
router.get("/", allQuery.getEscrow);

// get individual item routes
router.get("/:id", allQuery.getPlayerById);
router.get("/player_settings/:id", allQuery.getPlayerSettingsById)
router.get("/stats/:id", allQuery.getPlayerStatsById);

// add individual item routes
router.post("/", allQuery.addPlayer);
router.post("/zones/", allQuery.addZone);
router.post("/teams/", allQuery.addTeam);
router.post("/adventures/", allQuery.addAdventure);
router.post("/logs/", allQuery.addGameLog);
router.post("/escrow/:id", allQuery.addEscrow);

// delete routes
router.delete("/:id", allQuery.removePlayer);
router.delete("/adventures/:team_id", allQuery.removeAdventure);
router.delete("/escrow/:id", allQuery.removeEscrow);
router.delete("/logs/", (req, res, next) => {
  console.log("DELETE /logs/ route hit!"); // Debug log
  allQuery.removeGameLog(req, res);
});

// update routes
router.put("/:id", allQuery.updatePlayer);
router.put("/credits/:id", allQuery.updatePlayerCredits);
router.put("/nectar/:id", allQuery.updatePlayerNectar);
router.put("/xp/:id", allQuery.updatePlayerXP);
router.put("/gxp/:id", allQuery.updatePlayerGXP);
router.put("/lv/:id", allQuery.updatePlayerLevel);
router.put("/zones/:id", allQuery.updateMapZones);
router.put("/zones/ul/:id", allQuery.unlockMapZoneCheck);
router.put("/zones/updatedata/:mapgrid_4/:mapgrid_16", allQuery.updateMapZoneData);
router.put("/zones/updatelocale/:mapgrid_4/:mapgrid_16", allQuery.updateMapZoneLocale);
router.put("/teams/sethouse/:team_id", allQuery.updateTeamHouse);
router.put("/naps/:team_id", allQuery.updateTeamNap);
router.put("/escrow/:id", allQuery.updateEscrow);

module.exports = router;
