/**
 * Routes for system logs
 */

const { Router } = require('express');
const systemLogsController = require("../../controls/logs/system-logs");

const router = Router();

// Get all system logs
router.get("/", systemLogsController.getSystemLogs);

// Get recent system logs (last 24 hours)
router.get("/recent", systemLogsController.getRecentSystemLogs);

// Get system logs by msg_type
router.get("/type/:msg_type", systemLogsController.getSystemLogsByType);

// Get system logs by category
router.get("/category/:category", systemLogsController.getSystemLogsByCategory);

// Get system logs by reward_type
router.get("/reward/:reward_type", systemLogsController.getSystemLogsByRewardType);

// Add a new system log
router.post("/", systemLogsController.addSystemLog);

// Clean up expired logs
// router.post("/cleanup", systemLogsController.cleanupExpiredLogs);

module.exports = router;
