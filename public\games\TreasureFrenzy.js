class TreasureFrenzy extends BaseGame {
    constructor() {
        super();
        this.treasures = [];
        this.treasureBodies = []; // Matter.js physics bodies
        this.treasureTextures = {
            closed: PIXI.Texture.from('images/games/treasurefrenzy/Treasure-Closed.png'),
            open: PIXI.Texture.from('images/games/treasurefrenzy/Treasure-Open.png')
        };
        this.levelConfigs = {
            1: { treasureCount: 3, minClicks: 2, maxClicks: 2, spawnHeight: -100 },
            2: { treasureCount: 4, minClicks: 3, maxClicks: 3, spawnHeight: -120 },
            3: { treasureCount: 5, minClicks: 3, maxClicks: 5, spawnHeight: -140 },
            4: { treasureCount: 8, minClicks: 3, maxClicks: 3, spawnHeight: -160 },
            5: { treasureCount: 10, minClicks: 2, maxClicks: 5, spawnHeight: -180 }
        };
        this.gxpAwarded = false;
        this.isLevelingUp = false; // Flag to prevent multiple level up effects
        
        // Physics engine setup
        this.engine = null;
        this.world = null;
        this.ground = null;
        this.walls = [];
        this.physicsRunning = false;
    }

    initGame() {
        super.initGame();
        this.setupPhysics();
    }

    setupPhysics() {
        // Create Matter.js engine
        this.engine = Matter.Engine.create({
            gravity: { x: 0, y: 0.5 } // Reduced gravity for better gameplay
        });
        this.world = this.engine.world;

        // Create ground (invisible boundary at bottom)
        const groundY = this.app.screen.height - 20;
        this.ground = Matter.Bodies.rectangle(
            this.app.screen.width / 2,
            groundY + 10,
            this.app.screen.width,
            20,
            { 
                isStatic: true,
                friction: 0.8,
                restitution: 0.6 // Bouncy ground
            }
        );
        Matter.World.add(this.world, this.ground);

        // Create invisible walls on sides
        const wallThickness = 20;
        const leftWall = Matter.Bodies.rectangle(
            -wallThickness / 2,
            this.app.screen.height / 2,
            wallThickness,
            this.app.screen.height,
            { isStatic: true, friction: 0.3, restitution: 0.4 }
        );
        const rightWall = Matter.Bodies.rectangle(
            this.app.screen.width + wallThickness / 2,
            this.app.screen.height / 2,
            wallThickness,
            this.app.screen.height,
            { isStatic: true, friction: 0.3, restitution: 0.4 }
        );
        this.walls = [leftWall, rightWall];
        Matter.World.add(this.world, [leftWall, rightWall]);

        // Start physics engine
        this.physicsRunning = true;
        this.runPhysics();
    }

    runPhysics() {
        if (!this.physicsRunning) return;
        
        Matter.Engine.update(this.engine, 1000 / 60); // 60 FPS
        
        // Update PIXI sprites to match physics bodies
        this.treasures.forEach((treasure, index) => {
            if (treasure && this.treasureBodies[index]) {
                const body = this.treasureBodies[index];
                treasure.x = body.position.x;
                treasure.y = body.position.y;
                treasure.rotation = body.angle;
            }
        });
        
        requestAnimationFrame(() => this.runPhysics());
    }

    createTreasure() {
        let config;
        
        if (this.level <= 5) {
            config = this.levelConfigs[this.level];
        } else {
            // For levels beyond 5, add more treasures and keep clicks at 1-5
            const additionalTreasures = this.level - 5;
            config = {
                treasureCount: 10 + additionalTreasures,
                minClicks: 1,
                maxClicks: 5,
                spawnHeight: -180 - (additionalTreasures * 10)
            };
        }
        
        // Create PIXI sprite
        const treasure = new PIXI.Sprite(this.treasureTextures.closed);
        treasure.anchor.set(0.5);
        treasure.width = 64;
        treasure.height = 64;
        treasure.interactive = false;
        treasure.buttonMode = false;
        treasure.requiredClicks = Math.floor(Math.random() * (config.maxClicks - config.minClicks + 1)) + config.minClicks;
        treasure.currentClicks = 0;
        treasure.isOpen = false;
        treasure.on('pointerdown', () => this.onTreasureClick(treasure));
        
        // Create Matter.js physics body
        const body = Matter.Bodies.rectangle(
            Math.random() * (this.app.screen.width - 80) + 40,
            config.spawnHeight,
            64,
            64,
            {
                friction: 0.1,
                restitution: 0.8, // Very bouncy
                density: 0.001,
                frictionAir: 0.01
            }
        );
        
        // Add some random velocity for more dynamic movement
        Matter.Body.setVelocity(body, {
            x: (Math.random() - 0.5) * 4,
            y: Math.random() * 2 + 1
        });
        
        // Add some random angular velocity for spinning
        Matter.Body.setAngularVelocity(body, (Math.random() - 0.5) * 0.2);
        
        // Store reference to sprite in body for collision detection
        body.treasureSprite = treasure;
        
        // Add to world and stage
        Matter.World.add(this.world, body);
        this.app.stage.addChild(treasure);
        
        this.treasures.push(treasure);
        this.treasureBodies.push(body);
        
        return treasure;
    }

    onTreasureClick(treasure) {
        if (treasure.isOpen) return;
        
        // Play chest click sound
        if (window.GameSounds) {
            window.GameSounds.playChestClick();
        }
        
        treasure.currentClicks++;
        
        // Create multiple visual effects for better feedback
        const clickProgress = treasure.currentClicks / treasure.requiredClicks;
        
        // White blink effect (existing)
        window.VisualEffects.createWhiteBlink(treasure, 100, () => {
            if (treasure.currentClicks >= treasure.requiredClicks) {
                this.openTreasure(treasure);
            }
        });
        
        // Add sparkle effect at click position
        if (window.VisualEffects && typeof window.VisualEffects.createSparkle === 'function') {
            window.VisualEffects.createSparkle(treasure.x, treasure.y, this.app.stage, 0xFFD700);
        }
        
        // Add ripple effect
        if (window.VisualEffects && typeof window.VisualEffects.createRipple === 'function') {
            window.VisualEffects.createRipple(treasure.x, treasure.y, this.app.stage, 0xFFD700);
        }
        
        // Add button click effect
        if (window.VisualEffects && typeof window.VisualEffects.createButtonClick === 'function') {
            window.VisualEffects.createButtonClick(treasure.x, treasure.y, this.app.stage);
        }
        
        // Add pulse effect that gets stronger as we get closer to opening
        if (window.VisualEffects && typeof window.VisualEffects.createPulseEffect === 'function') {
            const pulseDuration = Math.max(300, 800 - (clickProgress * 500));
            window.VisualEffects.createPulseEffect(treasure, pulseDuration);
        }
        
        // Add explosion effect when treasure is about to open (last click)
        if (treasure.currentClicks >= treasure.requiredClicks - 1 && 
            window.VisualEffects && typeof window.VisualEffects.createExplosion === 'function') {
            setTimeout(() => {
                window.VisualEffects.createExplosion(treasure.x, treasure.y, 0xFFD700, 8, this.app.stage);
            }, 50);
        }
        
        // Show click progress with a small score popup
        if (window.VisualEffects && typeof window.VisualEffects.createScorePopup === 'function') {
            const progressText = `${treasure.currentClicks}/${treasure.requiredClicks}`;
            const progressColor = clickProgress >= 0.8 ? 0xFFD700 : 0xFFFFFF;
            window.VisualEffects.createScorePopup(treasure.x, treasure.y - 40, progressText, this.app.stage, progressColor);
        }
        
        // Add physics impulse on click for more interactivity
        const bodyIndex = this.treasures.indexOf(treasure);
        if (bodyIndex !== -1 && this.treasureBodies[bodyIndex]) {
            const body = this.treasureBodies[bodyIndex];
            Matter.Body.applyForce(body, body.position, {
                x: (Math.random() - 0.5) * 0.02,
                y: -0.01
            });
        }
    }

    openTreasure(treasure) {
        treasure.isOpen = true;
        treasure.texture = this.treasureTextures.open;
        const points = 100 * treasure.requiredClicks;
        this.score += points;
        
        // Create spectacular opening effects
        if (window.VisualEffects && typeof window.VisualEffects.createExplosion === 'function') {
            // Multiple explosions for celebration
            window.VisualEffects.createExplosion(treasure.x, treasure.y, 0xFFD700, 15, this.app.stage);
            setTimeout(() => {
                window.VisualEffects.createExplosion(treasure.x - 20, treasure.y - 20, 0xFFA500, 8, this.app.stage);
            }, 100);
            setTimeout(() => {
                window.VisualEffects.createExplosion(treasure.x + 20, treasure.y + 20, 0xFFFF00, 8, this.app.stage);
            }, 200);
        }
        
        // Add multiple sparkles around the treasure
        if (window.VisualEffects && typeof window.VisualEffects.createSparkle === 'function') {
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const angle = (Math.PI * 2 * i) / 5;
                    const sparkleX = treasure.x + Math.cos(angle) * 30;
                    const sparkleY = treasure.y + Math.sin(angle) * 30;
                    window.VisualEffects.createSparkle(sparkleX, sparkleY, this.app.stage, 0xFFD700);
                }, i * 50);
            }
        }
        
        // Add ripples for extra effect
        if (window.VisualEffects && typeof window.VisualEffects.createRipple === 'function') {
            window.VisualEffects.createRipple(treasure.x, treasure.y, this.app.stage, 0xFFD700);
            setTimeout(() => {
                window.VisualEffects.createRipple(treasure.x, treasure.y, this.app.stage, 0xFFFF00);
            }, 150);
        }
        
        // Create score popup with golden color
        if (window.VisualEffects && typeof window.VisualEffects.createScorePopup === 'function') {
            window.VisualEffects.createScorePopup(treasure.x, treasure.y, points, this.app.stage, 0xFFD700);
        }
        
        // Add pulse effect to the opened treasure
        if (window.VisualEffects && typeof window.VisualEffects.createPulseEffect === 'function') {
            window.VisualEffects.createPulseEffect(treasure, 800);
        }
        
        // Remove from physics world and stage after delay
        setTimeout(() => {
            const bodyIndex = this.treasures.indexOf(treasure);
            if (bodyIndex !== -1) {
                const body = this.treasureBodies[bodyIndex];
                if (body) {
                    Matter.World.remove(this.world, body);
                }
                this.treasureBodies.splice(bodyIndex, 1);
            }
            
            this.app.stage.removeChild(treasure);
            this.treasures = this.treasures.filter(t => t !== treasure);
            
            if (this.treasures.length === 0) {
                this.nextLevelOrEnd();
            }
        }, 600);
        
        this.updateHUD();
    }

    nextLevelOrEnd() {
        // Prevent multiple level up effects from being triggered
        if (this.isLevelingUp) {
            return;
        }
        
        // Allow unlimited progression - remove the level 5 limit
        this.isLevelingUp = true;
        this.level++;
        
        // Pause the game during level up effect
        this.gameOver = true;
        
        // Create standardized level up effect with pause functionality
        if (window.VisualEffects && typeof window.VisualEffects.createLevelUpEffect === 'function') {
            try {
                window.VisualEffects.createLevelUpEffect(this.app.stage, this.level, () => {
                    // Resume game and start new level
                    this.gameOver = false;
                    this.isLevelingUp = false;
                    this.startLevel();
                });
            } catch (error) {
                console.warn('VisualEffects.createLevelUpEffect failed:', error);
                // Fallback: start new level immediately
                this.gameOver = false;
                this.isLevelingUp = false;
                this.startLevel();
            }
        } else {
            // Fallback: start new level immediately
            this.gameOver = false;
            this.isLevelingUp = false;
            this.startLevel();
        }
    }

    startLevel() {
        this.clearTreasures();
        
        let config;
        if (this.level <= 5) {
            config = this.levelConfigs[this.level];
        } else {
            // For levels beyond 5, add more treasures and keep clicks at 1-5
            const additionalTreasures = this.level - 5;
            config = {
                treasureCount: 10 + additionalTreasures,
                minClicks: 1,
                maxClicks: 5,
                spawnHeight: -180 - (additionalTreasures * 10)
            };
        }
        
        // Spawn treasures with delay for dramatic effect
        for (let i = 0; i < config.treasureCount; i++) {
            setTimeout(() => {
                const treasure = this.createTreasure();
                if (this.gameInterval) {
                    treasure.interactive = true;
                    treasure.buttonMode = true;
                }
            }, i * 300); // Spawn each treasure 300ms apart
        }
        
        this.updateHUD();
    }

    clearTreasures() {
        // Remove from physics world
        this.treasureBodies.forEach(body => {
            if (body) {
                Matter.World.remove(this.world, body);
            }
        });
        this.treasureBodies = [];
        
        // Remove from stage
        this.treasures.forEach(treasure => {
            if (treasure && treasure.parent) {
                treasure.parent.removeChild(treasure);
            }
        });
        this.treasures = [];
    }

    async startGame() {
        if (playerData.credits < 1) {
            showAlert("Not enough credits to play!");
            return;
        }
        try {
            this.resetGame();
            await transactResource('credits', 1, 'subtract', showAlert, updatePlayerBalances);
            this.initGame();
            const startScreen = document.getElementById('start-screen');
            const gameArea = document.getElementById('game-area');
            const gameOverScreen = document.getElementById('game-over-screen');
            if (startScreen) startScreen.style.display = 'none';
            if (gameArea) gameArea.style.display = 'block';
            if (gameOverScreen) gameOverScreen.style.display = 'none';
            this.startCountdown();
        } catch (error) {
            showAlert("Failed to start game. Please try again.");
        }
    }

    onCountdownComplete() {
        this.startLevel();
        this.treasures.forEach(treasure => {
            treasure.interactive = true;
            treasure.buttonMode = true;
        });
        this.gameInterval = setInterval(() => {
            this.time++;
            this.updateHUD();
            if (this.time >= 60) {
                this.endGame("Time's Up!");
            }
        }, 1000);
    }

    resetGame() {
        this.clearTreasures();
        this.gxpAwarded = false;
        this.isLevelingUp = false; // Reset level up flag
        this.time = 0;
        this.score = 0;
        this.level = 1;
        if (this.gameInterval) {
            clearInterval(this.gameInterval);
            this.gameInterval = null;
        }
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        this.updateHUD();
    }

    stopGame() {
        this.physicsRunning = false;
        this.clearTreasures();
        
        // Clean up physics engine
        if (this.engine) {
            Matter.Engine.clear(this.engine);
        }
        
        super.stopGame();
    }
}

window.TreasureFrenzy = TreasureFrenzy; 