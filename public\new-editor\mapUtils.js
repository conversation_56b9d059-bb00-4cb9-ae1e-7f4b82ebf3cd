// mapUtils.js - Constants, textures, and utility functions for the map editor

// Core constants
const TILE_SIZE = 32;
const GRID_SIZE = 16;

// Tile textures mapping
const TILE_TEXTURES = {
  // Forest World
  grassplains: '../images/forest_world/grass.png',
  grass: '../images/forest_world/grass.png',
  water: '../images/forest_world/water.png',
  forest: '../images/forest_world/forest.png',
  crushieforest: '../images/forest_world/c_forest.png',
  castle: '../images/forest_world/castle.png',
  ruins: '../images/forest_world/ruins.png',
  town: '../images/forest_world/town.png',
  // Tropic World
  tr_water: '../images/tropic_world/tr_water.png',
  tr_castle: '../images/tropic_world/tr_castle.png',
  tr_island: '../images/tropic_world/tr_island.png',
  tr_waterland: '../images/tropic_world/tr_waterland.png',
  // Desert World
  ds_dirt: '../images/desert_world/ds_dirt.png',
  ds_castle: '../images/desert_world/ds_castle.png',
  ds_dunes: '../images/desert_world/ds_dunes.png',
  ds_ruins: '../images/desert_world/ds_ruins.png',
  ds_town: '../images/desert_world/ds_town.png',
  // Space World
  sp_normal: '../images/space_world/sp_normalB.png',
  sp_gas1: '../images/space_world/sp_gas1.png',
  sp_debris: '../images/space_world/sp_debris.png',
  sp_station1: '../images/space_world/sp_station1.png',
  sp_gplanet1: '../images/space_world/sp_gplanet1.png',
  sp_dplanet1: '../images/space_world/sp_dplanet1.png',
  sp_iplanet1: '../images/space_world/sp_iplanet1.png',
  sp_rplanet1: '../images/space_world/sp_rplanet1.png',
  // Fallbacks for missing textures
  lava: '../images/forest_world/grass.png', // fallback
  mountain: '../images/forest_world/grass.png', // fallback
  cyber: '../images/forest_world/grass.png', // fallback
  fortress: '../images/forest_world/grass.png', // fallback
};

// Navigation state
const createNavigationState = function() {
  return {
    view: 'worlds',
    world: 0,
    zone: 0
  };
};

// Domain URL configuration
const getDomainUrl = function() {
  return (typeof window !== 'undefined' && window.domain_url) ? window.domain_url : 'https://express-crushie.herokuapp.com';
};

// Utility functions
const showLoading = function(msg) {
  msg = msg || 'Loading map data...';
  const container = document.getElementById('threejs-map-container');
  if (container) {
    // Only show loading if Three.js hasn't been initialized yet
    if (!window.renderer || !window.renderer.domElement) {
      container.innerHTML = '<div style="color:#fff;text-align:center;padding:40px;font-size:1.2em;">' + msg + '</div>';
    }
  }
};

const createNavButton = function(text, iconClass, onClick) {
  const button = document.createElement('button');
  button.classList.add('editor-nav-button');
  
  const icon = document.createElement('span');
  icon.classList.add('nav-icon');
  icon.classList.add(iconClass);
  icon.style.cssText = 
    'width: 16px;' +
    'height: 16px;' +
    'background-size: contain;' +
    'background-repeat: no-repeat;' +
    'background-position: center;' +
    'flex-shrink: 0;';
  
  if (iconClass === 'world-icon') {
    icon.style.backgroundImage = 'url(../images/ui/map-viewer/World_Icon_Off.png)';
  } else if (iconClass === 'zone-icon') {
    icon.style.backgroundImage = 'url(../images/ui/map-viewer/World1_UnlockedTile.png)';
  } else if (iconClass === 'locale-icon') {
    icon.style.backgroundImage = 'url(../images/forest_world/grass.png)';
  }
  
  const textSpan = document.createElement('span');
  textSpan.textContent = text;
  
  button.appendChild(icon);
  button.appendChild(textSpan);
  
  button.addEventListener('mouseenter', function() {
    if (!button.classList.contains('active')) {
      button.classList.add('active');
      button.classList.remove('editor-nav-button-hover');
      button.classList.remove('editor-nav-button-active');
    }
  });
  
  button.addEventListener('mouseleave', function() {
    if (!button.classList.contains('active')) {
      button.classList.remove('editor-nav-button-hover');
      button.classList.remove('editor-nav-button-active');
    }
  });
  
  button.addEventListener('click', onClick);
  
  return button;
};

const updateNavButtons = function(activeButton) {
  const buttons = document.querySelectorAll('.editor-nav-button');
  buttons.forEach(function(btn) {
    btn.classList.remove('active');
    btn.classList.remove('editor-nav-button-hover');
    btn.classList.remove('editor-nav-button-active');
  });
  
  const activeBtn = Array.from(buttons).find(function(btn) {
    const icon = btn.querySelector('.' + activeButton + '-icon');
    return icon !== null;
  });
  
  if (activeBtn) {
    activeBtn.classList.add('active');
    activeBtn.classList.add('editor-nav-button-active');
  }
};

const addLabel = function(text, x, y, size, color, fontSize, scene) {
  fontSize = fontSize || 14;
  if (!scene) {
    console.error('Scene not initialized');
    return;
  }
  
  // Create a canvas for the label
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const font = "'Press Start 2P', cursive";
  const isPixelFont = font.includes('Press Start 2P') || font.includes('Pixel') || font.includes('pixel');
  
  // Use native pixel font sizes (8, 16, 32, etc.)
  let px = fontSize || 14;
  if (isPixelFont) {
    // Snap to nearest native size (8, 16, 32, 64)
    if (px <= 8) px = 8;
    else if (px <= 16) px = 16;
    else if (px <= 32) px = 32;
    else px = 64;
  }
  
  ctx.font = 'bold ' + px + 'px ' + font;
  // Estimate width/height
  const textWidth = ctx.measureText(text).width;
  canvas.width = Math.ceil(textWidth + 16);
  canvas.height = Math.ceil(px + 12);
  
  // Redraw with correct size
  ctx.font = 'bold ' + px + 'px ' + font;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillStyle = color || '#fff';
  
  // Disable smoothing for pixel fonts
  if (isPixelFont) {
    ctx.imageSmoothingEnabled = false;
  }
  
  ctx.fillText(text, Math.round(canvas.width / 2), Math.round(canvas.height / 2));
  
  const texture = new THREE.CanvasTexture(canvas);
  if (isPixelFont) {
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
  }
  
  const material = new THREE.SpriteMaterial({ map: texture, transparent: true });
  const sprite = new THREE.Sprite(material);
  
  // Round coordinates for pixel fonts
  if (isPixelFont) {
    sprite.position.set(Math.round(x), Math.round(y), 20);
  } else {
    sprite.position.set(x, y, 20);
  }
  
  sprite.scale.set(size, (canvas.height / canvas.width) * size, 1);
  scene.add(sprite);
};

// --- Mini Tile Preview Helpers (moved from mapUI.js) ---
let miniTilePreview = null;
function showMiniTilePreview(imgSrc) {
  if (!miniTilePreview) {
    miniTilePreview = document.createElement('img');
    miniTilePreview.id = 'miniTilePreview';
    miniTilePreview.style.position = 'fixed';
    miniTilePreview.style.pointerEvents = 'none';
    miniTilePreview.style.zIndex = '9999';
    miniTilePreview.style.width = '24px';
    miniTilePreview.style.height = '24px';
    miniTilePreview.style.border = '2px solid #00fff0';
    miniTilePreview.style.background = '#222';
    miniTilePreview.style.borderRadius = '4px';
    miniTilePreview.style.boxShadow = '0 2px 8px #0002';
    document.body.appendChild(miniTilePreview);
  }
  miniTilePreview.src = imgSrc;
  miniTilePreview.classList.add('visible');
}
function hideMiniTilePreview() {
  if (miniTilePreview) miniTilePreview.classList.remove('visible');
}
function updateMiniTilePreviewPosition(e) {
  if (miniTilePreview && miniTilePreview.classList.contains('visible')) {
    miniTilePreview.style.left = (e.clientX + 12) + 'px';
    miniTilePreview.style.top = (e.clientY + 12) + 'px';
  }
}

// Make functions globally accessible
window.TILE_SIZE = TILE_SIZE;
window.GRID_SIZE = GRID_SIZE;
window.TILE_TEXTURES = TILE_TEXTURES;
window.createNavigationState = createNavigationState;
window.getDomainUrl = getDomainUrl;
window.showLoading = showLoading;
window.createNavButton = createNavButton;
window.updateNavButtons = updateNavButtons;
window.addLabel = addLabel; 
window.showMiniTilePreview = showMiniTilePreview;
window.hideMiniTilePreview = hideMiniTilePreview;
window.updateMiniTilePreviewPosition = updateMiniTilePreviewPosition; 