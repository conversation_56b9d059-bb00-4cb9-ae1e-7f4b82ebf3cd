// Navigation handling for HTML buttons to sync with ThreeJS map

/**
 * Override the legacy displayWorlds function to work with ThreeJS map
 */
function displayWorlds() {
  if (typeof nav !== 'undefined') {
    nav.view = 'worlds';
    nav.world = 0;
    nav.zone = 0;
  }
  
  // Update HTML navigation buttons
  updateNavButtons(0, 0);
  
  // If ThreeJS map is active, render worlds view
  if (typeof renderWorlds === 'function') {
    renderWorlds();
    updateThreeJsNavButtons('world');
  }
  
  // Play UI sound if available
  if (typeof AudioManager !== 'undefined' && AudioManager.playUISound) {
    AudioManager.playUISound('map');
  }
}

/**
 * Override the legacy displayZones function to work with ThreeJS map
 */
function displayZones(worldId) {
  const world = Number(worldId);
  
  if (typeof nav !== 'undefined') {
    nav.view = 'zones';
    nav.world = world;
    nav.zone = 0;
  }
  
  // Update HTML navigation buttons
  updateNavButtons(world, 0);
  
  // If ThreeJS map is active, render zones view
  if (typeof renderZones === 'function') {
    renderZones();
    updateThreeJsNavButtons('zone');
  }
  
  // Play UI sound if available
  if (typeof AudioManager !== 'undefined' && AudioManager.playUISound) {
    AudioManager.playUISound('map');
  }
}

/**
 * Override the legacy displayLocales function to work with ThreeJS map
 */
function displayLocales(zoneId) {
  const zone = Number(zoneId);
  
  if (typeof nav !== 'undefined') {
    nav.view = 'locales';
    nav.zone = zone;
  }
  
  // Update HTML navigation buttons
  updateNavButtons(nav.world, zone);
  
  // If ThreeJS map is active, render locales view
  if (typeof renderLocales === 'function') {
    renderLocales();
    updateThreeJsNavButtons('locale');
  }
  
  // Play UI sound if available
  if (typeof AudioManager !== 'undefined' && AudioManager.playUISound) {
    AudioManager.playUISound('map');
  }
}
 
function setupHtmlNavigationListeners() { 
  const worldBtn = document.getElementById('world_btn');
  const zoneBtn = document.getElementById('zone_btn');
  const localeBtn = document.getElementById('locale_btn'); 
  if (worldBtn) {
    worldBtn.addEventListener('click', (e) => {
      e.preventDefault();
      displayWorlds();
    });
  }
  
  if (zoneBtn) {
    zoneBtn.addEventListener('click', (e) => {
      e.preventDefault();
      displayZones(nav.world);
    });
  }
  
  if (localeBtn) {
    localeBtn.addEventListener('click', (e) => {
      e.preventDefault();
      displayLocales(nav.zone);
    });
  }
}
 
function initializeNavigation() { 
  setupHtmlNavigationListeners(); 
  // Override legacy functions to work with ThreeJS map
  window.displayWorlds = displayWorlds;
  window.displayZones = displayZones;
  window.displayLocales = displayLocales;
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeNavigation);
} else {
  initializeNavigation();
} 