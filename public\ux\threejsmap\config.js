// Configuration for Three.js map navigation

// Image paths for worlds, zones, and tiles
const IMAGES = {
  world: {
    off: 'images/ui/map-viewer/World_Icon_Off.png',
    on: 'images/ui/map-viewer/World_Icon_On.png',
    scroll: 'images/ui/world_scroll.png'
  },
  lock: 'images/nav/lock_map_icon.png',
  zoneUnlocked: [
    'images/ui/map-viewer/World1_UnlockedTile.png',
    'images/ui/map-viewer/World2_UnlockedTile.png',
    'images/ui/map-viewer/World3_UnlockedTile.png',
    'images/ui/map-viewer/World4_UnlockedTile.png',
  ],
  zoneLocked: 'images/ui/map-viewer/Locked_Zone_Off.png',
  zoneLockedHover: 'images/ui/map-viewer/Locked_Zone_On.png',
  overlayBorder: 'images/ui/map-viewer/Overlay_Border.png',
  overlaySelect: 'images/ui/map-viewer/Overlay_Select_Lg.png',
  adventure: 'images/ui/move_icon.gif'
};

// Tile type to image mapping
const TILE_TYPE_TO_IMAGE = {
  'castle': 'images/forest_world/castle.png',
  'water': 'images/forest_world/water.png',
  'forest': 'images/forest_world/forest.png',
  'crushieforest': 'images/forest_world/c_forest.png',
  'lava': 'images/forest_world/lava.png',
  'mountain': 'images/forest_world/mountain.png',
  'cyber': 'images/forest_world/cyber.png',
  'fortress': 'images/forest_world/fortress.png',
  'grassplains': 'images/forest_world/grass.png',
  'ruins': 'images/forest_world/ruins.png',
  'town': 'images/forest_world/town.png',
  'tr_water': 'images/tropic_world/tr_water.png',
  'tr_castle': 'images/tropic_world/tr_castle.png',
  'tr_island': 'images/tropic_world/tr_island.png',
  'tr_waterland': 'images/tropic_world/tr_waterland.png',
  'ds_dirt': 'images/desert_world/ds_dirt.png',
  'ds_castle': 'images/desert_world/ds_castle.png',
  'ds_dunes': 'images/desert_world/ds_dunes.png',
  'ds_ruins': 'images/desert_world/ds_ruins.png',
  'ds_town': 'images/desert_world/ds_town.png',
  'sp_normal': 'images/space_world/sp_normalB.png',
  'sp_gas1': 'images/space_world/sp_gas1.png',
  'sp_debris': 'images/space_world/sp_debris.png',
  'sp_station1': 'images/space_world/sp_station1.png',
  'sp_gplanet1': 'images/space_world/sp_gplanet1.png',
  'sp_dplanet1': 'images/space_world/sp_dplanet1.png',
  'sp_iplanet1': 'images/space_world/sp_iplanet1.png',
  'sp_rplanet1': 'images/space_world/sp_rplanet1.png',
};

// World names for fallback
const WORLD_NAMES = ['Forest World', 'Tropical World', 'Desert World', 'Space World'];

// Renderer settings - updated for better canvas utilization
const RENDERER_CONFIG = {
  width: 592, // 512 + 40*2
  height: 572, // 512 + 30*2
  backgroundColor: 0xf0f0f0,
  cameraZ: 100
};

// Grid configurations - updated to fill canvas better with larger sizes
const GRID_CONFIG = {
  worlds: {
    size: 240,
    gap: 0,
    gridSize: 2
  },
  zones: {
    size: 120,
    gap: 0,
    gridSize: 4
  },
  locales: {
    size: 30,
    gap: 0,
    gridSize: 16
  }
}; 