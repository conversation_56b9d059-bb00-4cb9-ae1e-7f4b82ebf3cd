const axios = require("axios");
const { app_url } = require("../../config");
const { getZones, getTile } = require("../api/map-api");
const { rewardSelectionByPool } = require("../db/rewards");
const { updateTeamLocation } = require("../db/teams");
const { processRewardResult } = require("../rewards/adventure-rewards");
const { logAdventureCompletion } = require("../api/logs-api");

async function getAdventures() {
  const { data } = await axios.get(`${app_url}/adventures/`);
  return data;
}

async function getInProgressAdventures(data) {
  return data
    .filter(({ status }) => status === "In Progress")
    .map(({ adventure_id, init_steps, current_steps, team_id, owner_id, mapgrid_4, mapgrid_16, mapgrid_256 }) =>
      [adventure_id, init_steps, current_steps, team_id, owner_id, mapgrid_4, mapgrid_16, mapgrid_256]
    );
}

async function updateAdventureProgress(adventures, additionalData) {
  const progressRate = 10;

  for (let i = 0; i < adventures.length; i++) {
    const [id, req_steps, current_steps] = adventures[i];

    if (req_steps > current_steps + progressRate) {
      await updateAdventure(id, current_steps + progressRate, "In Progress");
    } else {
      await completeAdventure(adventures[i], additionalData[i]);
    }
  }
}

async function completeAdventure(adventureData, additionalData) {
  const [id, req_steps, current_steps, team_id, owner_id, grid1, grid2, grid3] = adventureData;

  try {
    await Promise.all([
      updateTeamLocation(team_id, "Napping", grid1, grid2, grid3),
      updateAdventure(id, req_steps, "Complete")
    ]);
  } catch (error) {
  }

  let teamName, zoneData, terrain;
  try {
    [teamName, zoneData, terrain] = await Promise.all([
      getTeamName(owner_id, team_id),
      getZones(),
      getTile(getZones, [additionalData.mapgrid_4, additionalData.mapgrid_16, additionalData.mapgrid_256])
    ]);
  } catch (error) {
    terrain = "default";
  }

  try {
    await Promise.all([
      logAdventureCompletion(id, team_id, owner_id, teamName, [grid1, grid2, grid3]),
      processAdventureRewards(id, team_id, owner_id, terrain, req_steps)
    ]);
  } catch (error) {
  }
}

async function getTeamName(owner_id, team_id) {
  try {
    const { data } = await axios.get(`${app_url}/teams/${owner_id}`);
    
    if (!data || !Array.isArray(data)) {
      return "Unknown Team";
    }
    
    const team = data.find(t => t.team_id === team_id);
    return team?.team_name || "Unknown Team";
  } catch (error) {
    return "Unknown Team";
  }
}

async function processAdventureRewards(id, team_id, owner_id, terrain, steps) {
  try {
    const rewardResult = await rewardSelectionByPool(owner_id, id, terrain);
    const processedResult = await processRewardResult(rewardResult, owner_id, id);

    if (processedResult.success) {
      await logRewardOutcome(id, team_id, owner_id, processedResult, "reward_created");
    } else {
      await logRewardOutcome(id, team_id, owner_id, processedResult, "reward_error", steps);
    }
  } catch (error) {
    await logRewardOutcome(id, team_id, owner_id, { error, steps }, "system_error");
  }
}

async function logRewardOutcome(id, team_id, owner_id, result, type, steps) {
  let logData;

  if (type === "reward_created") {
    logData = {
      desc: `You received a ${result.rewardType} reward for completing adventure ${id}!`,
      adventure_id: id,
      team_id,
      reward_type: result.rewardType,
      amount: result.amount || 0
    };
  } else if (type === "reward_error") {
    const limitType = result.limitType || "unknown";
    const errorMessage = limitType === "global"
      ? `Global limit reached for ${result.rewardType} rewards. Try again later.`
      : limitType === "player"
        ? `You've reached your daily limit for ${result.rewardType} rewards. Try again tomorrow.`
        : `Failed to create reward: ${result.message}`;

    logData = {
      desc: errorMessage,
      adventure_id: id,
      team_id,
      reward_type: result.rewardType || "unknown",
      limit_type: limitType,
      steps: steps
    };
  } else {
    const errorType = determineErrorType(result.error);
    logData = {
      desc: `An error occurred while processing your adventure reward: ${result.error.message}`,
      adventure_id: id,
      team_id,
      error_type: errorType,
      error_details: result.error.message,
      steps: steps
    };
  }

  try {
    await axios.post(
      `${app_url}/players/logs/`,
      {
        wax_id: owner_id,
        status: "new",
        type,
        data: JSON.stringify(logData)
      },
      { headers: { "Content-Type": "application/json" } }
    );
  } catch {
  }
}

function determineErrorType(error) {
  if (!error?.message) return "System Error";
  const message = error.message.toLowerCase();
  if (message.includes("network")) return "Network Error";
  if (message.includes("timeout")) return "Request Timeout";
  if (message.includes("database")) return "Database Error";
  if (message.includes("validation")) return "Validation Error";
  return "System Error";
}

async function updateAdventure(adventure_id, steps, status) {
  const validStatus = ["Complete", "In Progress"].includes(status) ? status : "Complete";

  try {
    await axios.put(
      `${app_url}/adventures/${adventure_id}`,
      { current_steps: steps, status: validStatus },
      { headers: { "Content-Type": "application/json" } }
    );
  } catch (error) {
  }
}

module.exports = {
  getAdventures,
  getInProgressAdventures,
  updateAdventureProgress,
  updateAdventure,
  completeAdventure,
  getTeamName,
  processAdventureRewards
};