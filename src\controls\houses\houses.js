const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

// not in use while rentals not working
const addHouse = (req, res) => {
	const {
		owner_id,
		renter_id,
		asset_id,
		asset_name,
		price,
		capacity,
		status
	} = req.body;
	pool.query(queries.exist.checkHouseExists, [asset_id], (error, results) => {
		if (results.rows.length) {
			res.send("ITEM ID already exists.");
			return;
		}
	});
	//add to db
	pool.query(queries.add.addHouse, [owner_id, renter_id, asset_id, asset_name, price, capacity, status], (error, results) => {
		res.status(201).send("NEW HOUSE ITEM added successfully!"); 
		return;
	});
};

const getHouses = (req, res) => {
	pool.query(queries.get.getHouses, (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getHousesByOwnerId = (req, res) => {
	var owner_id = req.params.owner_id;
	// const status = req.params.status;
	pool.query(queries.getby.getHousesByOwnerId, [owner_id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getHousesByRenterId = (req, res) => {
	var renter_id = req.params.renter_id;
	// const status = req.params.status;
	pool.query(queries.getby.getHousesByRenterId, [renter_id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getHousesByAssetId = (req, res) => {
	var asset_id = req.params.asset_id;
	pool.query(queries.getby.getHousesByAssetId, [asset_id], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getHousesByStatus = (req, res) => {
	var status = req.params.status;
	pool.query(queries.getby.getHousesByStatus, [status], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const getHousesByOwnerIdAndStatus = (req, res) => {
	var owner_id = req.params.owner_id;
	var status = req.params.status;
	pool.query(queries.getby.getHousesByOwnerIdAndStatus, [owner_id, status], (error, results) => {
		res.status(200).json(results.rows);
		return;
	});
};

const removeHouse = (req, res) => {
	var owner_id = req.params.owner_id;
	var asset_id = req.params.asset_id;
	pool.query(queries.remove.removeHouse, [owner_id, asset_id], (error, results) => {
		if (error) {
			console.log(error);
			return res.status(500).send("Error removing team from house: " + error);
		}
		res.status(200).send("ITEM removed successfully.");
	});
};

const updateHouse = (req, res) => {
 var asset_id = req.params.asset_id;
 var {
	 renter_id,
	 price,
	 status
 } = req.body;
 pool.query(queries.up.updateHouse, [renter_id, price, status, asset_id], (error, results) => {
	 res.status(200).send("ITEM updated successfully.");
 });
};

//"UPDATE houses SET status = $1 WHERE asset_id = $2";
const updateHouseStatus = (req, res) => {
	var status = req.params.status;
	var asset_id = req.params.asset_id;
	var {
		status,
		asset_id
	} = req.body;
	pool.query(queries.up.updateHouseStatus, [asset_id, status], (error, results) => {
		res.status(200).send("HOUSE updated successfully.");
	});
};

module.exports = {
	addHouse,
	getHouses,
	getHousesByOwnerId,
	getHousesByRenterId,
	getHousesByAssetId,
	getHousesByStatus,
	getHousesByOwnerIdAndStatus,
	removeHouse,
	updateHouse,
	updateHouseStatus
};
