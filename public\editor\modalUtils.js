// modalUtils.js
// Manages the confirmation dialog for creating new zones

// Assumes jQuery is available
// Assumes createNewZone is available globally

function createNewZoneModal() {
  const confirmationDialog = $('<div>', { id: 'confirmation-dialog', class: 'd-none' });
  const message = $('<p>').text('Are you sure you wish to create a new zone? (Warning: all locales will be deleted and replaced!)');
  const confirmBtn = $('<button>', { id: 'confirm-btn', class: 'btn-primary' }).text('Confirm');
  const cancelBtn = $('<button>', { id: 'cancel-btn', class: 'btn-secondary' }).text('Cancel');
  confirmationDialog.append(message, confirmBtn, cancelBtn);
  $('body').append(confirmationDialog);
  confirmationDialog.removeClass('d-none');
  confirmBtn.on('click', handleConfirm);
  cancelBtn.on('click', handleCancel);
}

function handleConfirm() {
  createNewZone();
  hideConfirmationDialog();
}

function handleCancel() {
  console.log('Rejected.');
  hideConfirmationDialog();
}

function hideConfirmationDialog() {
  const confirmationDialog = document.getElementById('confirmation-dialog');
  confirmationDialog.classList.add('d-none');
}

// Expose functions to global scope
window.createNewZoneModal = createNewZoneModal;