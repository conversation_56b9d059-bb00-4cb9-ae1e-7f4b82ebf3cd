class LaserDefenderPowerupManager {
    constructor(game) {
        this.game = game;
        this.activePowerups = {
            rapidFire: { active: false, duration: 0, originalCooldown: 300 },
            spreadShot: { active: false, duration: 0 },
            shield: { active: false, duration: 0 }
        };
        this.shield = null;
    }

    updatePowerups() {
        const now = Date.now();
        
        Object.keys(this.activePowerups).forEach(powerup => {
            if (this.activePowerups[powerup].active) {
                this.activePowerups[powerup].duration -= 16;
                if (this.activePowerups[powerup].duration <= 0) {
                    this.deactivatePowerup(powerup);
                }
            }
        });
        
        // Make shield follow turret
        if (this.activePowerups.shield.active && this.shield && this.game.turretManager.turret) {
            this.shield.x = this.game.turretManager.turret.x;
            this.shield.y = this.game.turretManager.turret.y;
        }
        this.game.turretManager.updateShotCooldown();
    }

    activatePowerup() {
        if (this.game.powerupProgress < this.game.powerupTarget) return;
        
        const powerups = ['rapidFire', 'spreadShot', 'shield'];
        const powerup = powerups[Math.floor(Math.random() * powerups.length)];
        
        switch (powerup) {
            case 'rapidFire':
                this.activePowerups.rapidFire.active = true;
                this.activePowerups.rapidFire.duration = 10000;
                break;
                
            case 'spreadShot':
                this.activePowerups.spreadShot.active = true;
                this.activePowerups.spreadShot.duration = 15000;
                break;
                
            case 'shield':
                this.activePowerups.shield.active = true;
                this.activePowerups.shield.duration = 8000;
                this.createShield();
                break;
        }
        
        this.game.powerupProgress = 0;
        this.game.updateHUD();
        
        if (window.VisualEffects) {
            window.VisualEffects.createExplosion(this.game.turretManager.turret.x, this.game.turretManager.turret.y, 0x00FFFF, 15, this.game.app.stage);
            
            for (let i = 0; i < 8; i++) {
                setTimeout(() => {
                    const angle = (Math.PI * 2 * i) / 8;
                    const sparkleX = this.game.turretManager.turret.x + Math.cos(angle) * 40;
                    const sparkleY = this.game.turretManager.turret.y + Math.sin(angle) * 40;
                    window.VisualEffects.createSparkle(sparkleX, sparkleY, this.game.app.stage, 0x00FFFF);
                }, i * 100);
            }
            
            const powerupNames = {
                'rapidFire': 'RAPID FIRE!',
                'spreadShot': 'SPREAD SHOT!',
                'shield': 'SHIELD!'
            };
            window.VisualEffects.createScorePopup(
                this.game.turretManager.turret.x, 
                this.game.turretManager.turret.y - 50, 
                powerupNames[powerup], 
                this.game.app.stage, 
                0x00FFFF
            );
        }
        
        if (window.GameSounds) {
            window.GameSounds.playPowerupActivate();
        }
    }

    deactivatePowerup(powerup) {
        this.activePowerups[powerup].active = false;
        this.activePowerups[powerup].duration = 0;
        
        if (powerup === 'shield') {
            this.removeShield();
        }
    }

    createShield() {
        // Remove existing shield if present
        if (this.shield) {
            this.removeShield();
        }
        // Create a white-to-cyan radial gradient for the shield
        const size = 120;
        const graphics = new PIXI.Graphics();
        const gradCanvas = document.createElement('canvas');
        gradCanvas.width = gradCanvas.height = size;
        const ctx = gradCanvas.getContext('2d');
        const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
        gradient.addColorStop(0, 'white');
        gradient.addColorStop(1, '#00ffff');
        ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();
        const texture = PIXI.Texture.from(gradCanvas);
        this.shield = new PIXI.Sprite(texture);
        this.shield.anchor.set(0.5);
        this.shield.x = this.game.turretManager.turretX;
        this.shield.y = this.game.turretManager.turretY;
        this.shield.alpha = 0.2;
        this.shield.zIndex = 999;
        this.game.app.stage.addChild(this.shield);
    }

    removeShield() {
        if (this.shield) {
            this.game.app.stage.removeChild(this.shield);
            this.shield = null;
        }
    }

    cleanup() {
        if (this.shield && this.shield.parent) {
            this.shield.parent.removeChild(this.shield);
        }
        
        Object.keys(this.activePowerups).forEach(powerup => {
            this.activePowerups[powerup].active = false;
            this.activePowerups[powerup].duration = 0;
        });
        
        this.shield = null;
    }
}

window.LaserDefenderPowerupManager = LaserDefenderPowerupManager;