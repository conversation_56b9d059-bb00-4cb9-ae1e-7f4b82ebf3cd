// Score Popup Effect
class ScorePopupEffect {
    constructor() {
        // No dependencies needed for this effect
    }

    // Create score popup
    create(x, y, score, stage, color = 0x00FF00) {
        const text = new PIXI.Text(`+${score}`, {
            fontFamily: 'Arial',
            fontSize: 24,
            fill: color,
            stroke: 0x000000,
            strokeThickness: 2
        });
        
        text.x = x;
        text.y = y;
        text.alpha = 1;
        
        stage.addChild(text);
        
        // Animate score popup
        let alpha = 1;
        let yOffset = 0;
        
        const animate = () => {
            yOffset -= 1;
            alpha -= 0.02;
            
            text.y = y + yOffset;
            text.alpha = alpha;
            
            if (alpha <= 0) {
                stage.removeChild(text);
            } else {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
} 