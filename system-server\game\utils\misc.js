function shuffleFisherYates(array) {
	let i = array.length;
	while (i--) {
		const ri = Math.floor(Math.random() * (i + 1));
		[array[i], array[ri]] = [array[ri], array[i]];
	}
	return array;
}

function getRandomElem(array) {
	var x = array[Math.round(Math.random() * (array.length - 1))];
	return x;
}

function getRandomNum() {
  return Math.floor(Math.random() * 2);
}

module.exports = {
	shuffleFisherYates,
	getRandomElem,
	getRandomNum
};
