// where the express routes are stored
const { Router } = require('express');
const allQuery = require("../controls/controller");

const router = Router();

router.get("/", allQuery.getAdventures);
router.get("/owner/:owner_id", allQuery.getAdventuresByOwnerId);
router.get("/:team_id", allQuery.getAdventuresByTeamId);

router.post("/", allQuery.addAdventure);
router.delete("/:adventure_id", allQuery.removeAdventure);
router.put("/:adventure_id", allQuery.updateAdventure);

module.exports = router;
