// Game Sounds Manager - Easy to edit sound mappings for all mini games
const GameSounds = {
    // Universal sounds used by all games
    universal: {
        countdown: '../audio/sfx/Countdown.wav',
        gameOver: '../audio/sfx/Gameover.wav',
        victory: '../audio/bgm/Victory.wav',
        levelUp: '../audio/sfx/LevelUp.wav'
    },  
    games: {
        cardMatch: {
            cardClick: '../audio/sfx/PlasticButton.wav'
        },
        bouncyClick: {
            bubblePop: '../audio/sfx/Jelly.wav'
        },
        forestScavenger: {
            collect: '../audio/sfx/RewardClaim.wav'
        },
        treasureFrenzy: {
            chestClick: '../audio/sfx/Chest.wav'
        },
        laserDefender: {
            laserShot: '../audio/sfx/PlasticButton.wav',
            enemyHit: '../audio/sfx/Jelly.wav',
            powerupActivate: '../audio/sfx/LevelUp.wav'
        }
    },

    // Helper function to play universal sounds
    playUniversal(soundName) {
        const soundPath = this.universal[soundName];
        if (soundPath) {
            const audio = new Audio(soundPath);
            audio.volume = window.AudioManager ? window.AudioManager.config.masterVolume * window.AudioManager.config.sfxVolume : 1;
            
            // Reset audio to beginning to ensure it plays
            audio.currentTime = 0;
            
            audio.play().catch((error) => {
                console.warn(`Failed to play ${soundName} sound:`, error);
            });
            return audio;
        }
        console.warn(`Universal sound '${soundName}' not found`);
        return null;
    },

    // Helper function to play game-specific sounds
    playGameSound(gameName, soundName) {
        const gameSounds = this.games[gameName];
        if (gameSounds && gameSounds[soundName]) {
            const audio = new Audio(gameSounds[soundName]);
            audio.volume = window.AudioManager ? window.AudioManager.config.masterVolume * window.AudioManager.config.sfxVolume : 1;
            
            // Reset audio to beginning to ensure it plays
            audio.currentTime = 0;
            
            audio.play().catch((error) => {
                console.warn(`Failed to play ${gameName} ${soundName} sound:`, error);
            });
            return audio;
        }
        console.warn(`Game sound '${soundName}' for game '${gameName}' not found`);
        return null;
    },

    // Convenience methods for common sounds
    playCountdown() {
        return this.playUniversal('countdown');
    },

    playGameOver() {
        return this.playUniversal('gameOver');
    },

    playVictory() {
        return this.playUniversal('victory');
    },

    playLevelUp() {
        return this.playUniversal('levelUp');
    },

    // Game-specific convenience methods
    playCardClick() {
        return this.playGameSound('cardMatch', 'cardClick');
    },

    playBubblePop() {
        return this.playGameSound('bouncyClick', 'bubblePop');
    },

    playCollect() {
        return this.playGameSound('forestScavenger', 'collect');
    },

    playChestClick() {
        return this.playGameSound('treasureFrenzy', 'chestClick');
    },

    playLaserShot() {
        return this.playGameSound('laserDefender', 'laserShot');
    },

    playEnemyHit() {
        return this.playGameSound('laserDefender', 'enemyHit');
    },

    playPowerupActivate() {
        return this.playGameSound('laserDefender', 'powerupActivate');
    }
};

window.GameSounds = GameSounds;

if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameSounds;
} 